import httpx
import asyncio
import json
import logging
from typing import Dict, Union, Any, Optional, List, Tuple, Callable
from urllib.parse import urljoin

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class AsyncApiClient:
    """增强版异步API客户端，支持GET和POST请求，提供更多错误处理和重试机制"""
    
    def __init__(
        self, 
        base_url: str, 
        auth_token: str = None, 
        timeout: float = 30.0,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        log_level: int = logging.INFO
    ):
        """
        初始化API客户端
        
        参数:
            base_url: API基础URL
            auth_token: 认证令牌
            timeout: 请求超时时间(秒)
            max_retries: 最大重试次数
            retry_delay: 重试延迟时间(秒)
            log_level: 日志级别
        """
        self.base_url = base_url.rstrip('/')
        self.auth_token = auth_token
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        # 设置默认请求头
        self.default_headers = {
            'User-Agent': 'python-requests/2.32.3',
            'Accept-Encoding': 'gzip, deflate',
            'Accept': 'application/json, */*',  # 优先接受JSON
            'Connection': 'keep-alive'
        }
        if auth_token:
            self.default_headers['Authorization'] = auth_token
        
        # 设置日志记录器
        self.logger = logging.getLogger('AsyncApiClient')
        self.logger.setLevel(log_level)
    
    async def request(
        self, 
        method: str, 
        endpoint: str, 
        params: Dict = None, 
        data: Dict = None, 
        json_data: Dict = None,
        headers: Dict = None,
        cookies: Dict = None,
        timeout: float = None,
        retry_on_status_codes: List[int] = None,
        retry_on_exceptions: List[Exception] = None,
        follow_redirects: bool = True,
        verify_ssl: bool = False,
        debug: bool = False
    ) -> Optional[Dict[str, Any]]:
        """
        发送HTTP请求并返回解析后的JSON结果
        
        参数:
            method: 请求方法，'GET' 或 'POST'
            endpoint: API端点（相对路径或完整URL）
            params: URL查询参数
            data: 表单数据（用于POST请求）
            json_data: JSON数据（用于POST请求）
            headers: 自定义请求头
            cookies: 自定义Cookie
            timeout: 此次请求的超时时间，覆盖全局设置
            retry_on_status_codes: 需要重试的HTTP状态码列表
            retry_on_exceptions: 需要重试的异常类型列表
            follow_redirects: 是否自动跟随重定向
            verify_ssl: 是否验证SSL证书
            debug: 是否打印调试信息
        
        返回:
            解析后的JSON数据或None（如果请求失败）
        """
        # 设置默认值
        if retry_on_status_codes is None:
            retry_on_status_codes = [429, 500, 502, 503, 504]
        
        if retry_on_exceptions is None:
            retry_on_exceptions = [httpx.ConnectTimeout, httpx.ReadTimeout, httpx.ConnectError]
        
        # 构建完整URL
        url = endpoint if endpoint.startswith(('http://', 'https://')) else urljoin(f"{self.base_url}/", endpoint)
        
        # 合并请求头
        request_headers = {**self.default_headers}
        if headers:
            request_headers.update(headers)
        
        if debug:
            self.logger.debug(f"请求方法: {method}")
            self.logger.debug(f"请求URL: {url}")
            if params:
                self.logger.debug(f"查询参数: {params}")
            if data:
                self.logger.debug(f"表单数据: {data}")
            if json_data:
                self.logger.debug(f"JSON数据: {json_data}")
            self.logger.debug(f"请求头: {request_headers}")
        
        # 创建客户端会话的配置
        client_kwargs = {
            'timeout': timeout or self.timeout,
            'verify': verify_ssl,
            'follow_redirects': follow_redirects,
            'cookies': cookies
        }
        
        # 重试逻辑
        for attempt in range(self.max_retries + 1):
            try:
                if attempt > 0:
                    self.logger.info(f"重试第 {attempt} 次 (共 {self.max_retries} 次)")
                    # 指数退避延迟
                    delay = self.retry_delay * (2 ** (attempt - 1))
                    await asyncio.sleep(delay)
                
                # 创建异步客户端
                async with httpx.AsyncClient(**client_kwargs) as client:
                    if debug and attempt == 0:
                        self.logger.debug("发送请求...")
                    
                    # 根据方法发送不同类型的请求
                    if method.upper() == 'GET':
                        response = await client.get(url, params=params, headers=request_headers)
                    elif method.upper() == 'POST':
                        # 选择使用表单数据还是JSON数据
                        if json_data is not None:
                            response = await client.post(url, params=params, json=json_data, headers=request_headers)
                        else:
                            response = await client.post(url, params=params, data=data, headers=request_headers)
                    else:
                        supported_methods = ['GET', 'POST']
                        if method.upper() == 'PUT':
                            response = await client.put(url, params=params, json=json_data or data, headers=request_headers)
                            supported_methods.append('PUT')
                        elif method.upper() == 'DELETE':
                            response = await client.delete(url, params=params, headers=request_headers)
                            supported_methods.append('DELETE')
                        elif method.upper() == 'PATCH':
                            response = await client.patch(url, params=params, json=json_data or data, headers=request_headers)
                            supported_methods.append('PATCH')
                        else:
                            raise ValueError(f"不支持的请求方法: {method}。支持的方法: {', '.join(supported_methods)}")
                    
                    if debug:
                        self.logger.debug(f"状态码: {response.status_code}")
                        self.logger.debug(f"响应头: {dict(response.headers)}")
                    
                    # 检查是否需要重试
                    if response.status_code in retry_on_status_codes and attempt < self.max_retries:
                        self.logger.warning(f"收到状态码 {response.status_code}，准备重试...")
                        continue
                    
                    # 处理响应
                    if 200 <= response.status_code < 300:  # 成功的状态码
                        content_type = response.headers.get('content-type', '').lower()
                        
                        if debug:
                            self.logger.debug(f"内容类型: {content_type}")
                            self.logger.debug(f"响应内容前100个字符: {response.text[:100] if response.text else 'N/A'}")
                        
                        # 尝试解析JSON
                        if 'application/json' in content_type or response.text.strip().startswith('{'):
                            try:
                                json_response = response.json()
                                if debug:
                                    self.logger.debug("成功解析JSON数据")
                                return json_response
                            except json.JSONDecodeError as e:
                                self.logger.warning(f"JSON解析错误: {e}")
                        
                        # 内容类型不是JSON，尝试手动解析
                        return self._try_parse_response(response, debug)
                    else:
                        self.logger.warning(f"请求失败，状态码: {response.status_code}")
                        
                        # 检查是否返回了错误信息
                        try:
                            error_json = response.json()
                            self.logger.warning(f"错误信息: {error_json}")
                            # 可以在这里添加特定的错误处理逻辑
                        except:
                            if response.text:
                                self.logger.warning(f"响应文本: {response.text[:200]}...")
                        
                        return None
            
            except tuple(retry_on_exceptions) as e:
                if attempt < self.max_retries:
                    self.logger.warning(f"发生错误: {e}，准备重试...")
                    continue
                self.logger.error(f"请求错误，已达到最大重试次数: {e}")
                return None
            except Exception as e:
                self.logger.error(f"未知错误: {type(e).__name__}: {e}")
                return None
        
        return None  # 所有重试都失败
    
    def _try_parse_response(self, response, debug: bool = False) -> Optional[Dict[str, Any]]:
        """尝试从响应中解析数据，支持多种格式"""
        # 如果响应为空
        if not response.text or not response.text.strip():
            if debug:
                self.logger.debug("响应内容为空")
            return None
        
        text = response.text.strip()
        
        # 尝试作为JSON解析
        try:
            # 检查文本是否看起来像JSON对象
            if (text.startswith('{') and text.endswith('}')) or (text.startswith('[') and text.endswith(']')):
                return json.loads(text)
        except json.JSONDecodeError:
            pass
        
        # 检查是否为HTML内容
        if '<html' in text.lower():
            if debug:
                self.logger.debug("服务器返回了HTML而不是JSON")
                if 'login' in text.lower():
                    self.logger.debug("可能需要重新登录或会话已过期")
            return None
        
        # 返回文本内容作为字典
        if debug:
            self.logger.debug("无法解析为JSON，返回原始文本")
        return {"text": text}
    
    async def get(self, endpoint: str, **kwargs) -> Optional[Dict[str, Any]]:
        """发送GET请求的快捷方法"""
        return await self.request("GET", endpoint, **kwargs)
    
    async def post(self, endpoint: str, **kwargs) -> Optional[Dict[str, Any]]:
        """发送POST请求的快捷方法"""
        return await self.request("POST", endpoint, **kwargs)
    
    async def put(self, endpoint: str, **kwargs) -> Optional[Dict[str, Any]]:
        """发送PUT请求的快捷方法"""
        return await self.request("PUT", endpoint, **kwargs)
    
    async def delete(self, endpoint: str, **kwargs) -> Optional[Dict[str, Any]]:
        """发送DELETE请求的快捷方法"""
        return await self.request("DELETE", endpoint, **kwargs)
    
    async def patch(self, endpoint: str, **kwargs) -> Optional[Dict[str, Any]]:
        """发送PATCH请求的快捷方法"""
        return await self.request("PATCH", endpoint, **kwargs)

# 使用示例
async def demo():
    # 创建客户端实例
    client = AsyncApiClient(
        base_url="http://10.122.31.36:8080/report/mobile/wechat",
        auth_token="SYAAkjENRNIAFVoQKylHQ51nZRNeX12-",
        max_retries=2,
        log_level=logging.DEBUG
    )
    
    print("\n=== GET请求示例 ===")
    # 使用便捷方法
    get_result = await client.get(
        "sv/afterSaleJyController/queryJyIndex",
        params={"yearMonth": "202301", "queryType": "M"},
        debug=True
    )
    print(f"GET请求结果: {get_result}")
    
    print("\n=== POST请求示例 ===")
    # 使用JSON数据的POST请求
    post_result = await client.post(
        "sv/afterSaleJyController/somePostEndpoint",  # 替换为实际的POST端点
        params={"param1": "value1"},  # URL参数
        json_data={"key1": "value1", "key2": "value2"},  # POST的JSON数据
        debug=True
    )
    print(f"POST请求结果: {post_result}")
    
    # 使用表单数据的POST请求
    form_result = await client.post(
        "sv/afterSaleJyController/anotherEndpoint",  # 替换为实际的端点
        data={"field1": "value1", "field2": "value2"},  # 表单数据
        debug=True
    )
    print(f"表单POST请求结果: {form_result}")

if __name__ == "__main__":
    asyncio.run(demo()) 