<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=375px, initial-scale=1.0">
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#1a73e8',
            secondary: '#5f6368'
          },
          borderRadius: {
            'none': '0px',
            'sm': '2px',
            DEFAULT: '4px',
            'md': '8px',
            'lg': '12px',
            'xl': '16px',
            '2xl': '20px',
            '3xl': '24px',
            'full': '9999px',
            'button': '4px'
          }
        }
      }
    }
  </script>
  <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
  <script>
      // 初始化 Mermaid
      mermaid.initialize({ startOnLoad: true });
  </script>
  <style>
    body {
      font-family: 'Noto Sans SC', sans-serif;
      background-color: #fff;
      width: 375px;
      margin: 0 auto;
    }

    .article-content {
      line-height: 1.8;
      color: #333;
    }

    .link-item {
      color: #1a73e8;
      text-decoration: none;
    }

    .link-item:hover {
      text-decoration: underline;
    }

    .collapse-content {
      display: none;
    }
  </style>
</head>

<body>
  <nav class="fixed top-0 w-full bg-white border-b border-gray-200 z-50">
    <div class="px-4 py-3 flex items-center justify-between">
      <div class="flex items-center">
        <button class="text-gray-600 mr-4">
          <i class="fas fa-arrow-left"></i>
        </button>
      </div>
      <button class="text-gray-600">
        <i class="fas fa-share"></i>
      </button>
    </div>
  </nav>

  <main class="mt-14 px-4 pb-20">
    <article>
      <header class="mb-6">
        <h2 class="text-xl font-bold mb-3">分析下今年途昂的销量</h2>
      </header>
    </article>

    <aside class="mt-8">
      <h3 class="text-lg font-bold mb-4">意图识别</h3>
      <div class="space-y-4">
        <div class="bg-gray-50 rounded-lg">
          <button class="w-full text-left p-4 font-medium mb-2 focus:outline-none flex items-center justify-between" onclick="toggleCollapse('intent-recognition')">
            理解用户问题意图
            <i class="fas fa-plus"></i>
          </button>
          <div id="intent-recognition" class="collapse-content p-4 text-sm text-gray-600">
            嗯，用户是想要分析XXX数据，判断为"数据分析"意图
          </div>
        </div>
        <div class="bg-gray-50 rounded-lg">
          <button class="w-full text-left p-4 font-medium mb-2 focus:outline-none flex items-center justify-between" onclick="toggleCollapse('parameter-extraction')">
            参数提取
            <i class="fas fa-plus"></i>
          </button>
          <div id="parameter-extraction" class="collapse-content p-4 text-sm text-gray-600">
            嗯，用户的问题中包含车型、地区、时间维度，需要从中提取参数。
          </div>
        </div>
      </div>
      <div class="max-w-2xl mx-auto mt-6">
        <p class="text-sm text-gray-700">用户意图：数据分析</p>
      </div>
    </aside>

    <aside class="mt-8">
      <h3 class="text-lg font-bold mb-4">内部和外部信息检索</h3>
      <div class="space-y-4">
        <div class="bg-gray-50 rounded-lg">
          <button class="w-full text-left p-4 font-medium mb-2 focus:outline-none flex items-center justify-between" onclick="toggleCollapse('internal-query')">
            根据问题拆解为内部查询
            <i class="fas fa-plus"></i>
          </button>
          <div id="internal-query" class="collapse-content p-4 text-sm text-gray-600">
            嗯，用户的问题可以拆解为XXX
          </div>
        </div>
        <div class="max-w-2xl mx-auto mt-6">
          <p class="text-sm text-gray-700">拆解后的内部问题：<br>1.途昂各大区销量<br>2.途昂去年销量</p>
        </div>
        <div class="bg-gray-50 rounded-lg">
          <button class="w-full text-left p-4 font-medium mb-2 focus:outline-none flex items-center justify-between" onclick="toggleCollapse('external-query')">
            根据问题拆解为联网查询
            <i class="fas fa-plus"></i>
          </button>
          <div id="external-query" class="collapse-content p-4 text-sm text-gray-600">
            嗯，用户的问题可以拆解为XXX
          </div>
        </div>
        <div class="max-w-2xl mx-auto mt-6">
          <p class="text-sm text-gray-700">拆解后的联网问题：<br>1.途昂促销策略<br>2.途昂竞品有哪些策略</p>
        </div>
      </div>
    </aside>

    <aside class="mt-8">
      <h3 class="text-lg font-bold mb-4">深度思考</h3>
      <div class="space-y-4">
        <div class="bg-gray-50 rounded-lg">
          <button class="w-full text-left p-4 font-medium mb-2 focus:outline-none flex items-center justify-between" onclick="toggleCollapse('deep-thought')">
            思考中
            <i class="fas fa-plus"></i>
          </button>
          <div id="deep-thought" class="collapse-content p-4 text-sm text-gray-600">
            嗯，用户提供了内部和外部信息，我来看下可以得出什么结论·····
          </div>
        </div>
        <div>
          <div>
            总结
          </div>
          <div class="p-4 text-sm text-gray-600">
            根据用户提供的信息，可以看出，今年途昂销量趋势上涨<br>参考资料：<br>[1]参考网页1：途昂今年销量<br>[2]参考内部数据：途昂今年销量
          </div>
          <div class="mermaid">
            xychart-beta
            title "途昂今年销量趋势"
            x-axis [jan, feb, mar, apr, may, jun, jul, aug, sep, oct, nov, dec]
            y-axis "销量" 4000 --> 11000
            bar [3000, 3500, 4000, 4500, 5000, 5500, 6000, 6500, 7000, 7500, 8000, 8500]
        </div>
        </div>
      </div>
    </aside>
  </main>
  <script>
    function toggleCollapse(id) {
      var content = document.getElementById(id);
      var button = content.previousElementSibling;
      var icon = button.querySelector('i');
      if (content.style.display === "none" || content.style.display === "") {
        content.style.display = "block";
        icon.classList.remove('fa-plus');
        icon.classList.add('fa-minus');
      } else {
        content.style.display = "none";
        icon.classList.remove('fa-minus');
        icon.classList.add('fa-plus');
      }
    }
  </script>
</body>

</html>
