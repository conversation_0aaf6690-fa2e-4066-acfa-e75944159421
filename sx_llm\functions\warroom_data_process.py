import json, copy

from utils.sx_dict import *
from utils.sx_date import *
from utils.clean_json import *
from utils.sx_log import save_process_log
from utils.sx_chart_process import chart_select, content_show
from utils.sx_request import async_request_with_retries
from utils.env_config import warroom_url

def warroom_json_generate(format_json):
    """将清洗后的Json转为可以进行数据查询的Json，以便查询想要的数据

    Args:
        format_json (String): 清洗后的Json

    Returns:
        String: 进行沙盘数据查询的Json
    """
    # 接口json转化
    warroom_json = json.loads(format_json)
    # 除了今日或者趋势查询，其余都使用分布进行统计数据查询
    if warroom_json["display"] in ["趋势"] or (warroom_json["today"] == "True" and warroom_json["kpi"] == ['转化'] and warroom_json["data_dim"] == "all"):
        warroom_json["display"] = "趋势"
    else:
        if warroom_json["display"] == "同环比":
            if "销量" in warroom_json["kpi"] or "市占" in warroom_json["kpi"]:
                warroom_json["kpi"] = ["上险"]
            elif "批售数" in warroom_json["kpi"] or "含大客户发票数" in warroom_json["kpi"] or "日均批售数" in warroom_json["kpi"] or "日均含大客户发票数" in warroom_json["kpi"]:
                warroom_json["kpi"] = ["批售"]
            elif "总库存" in warroom_json["kpi"] or "总部库存" in warroom_json["kpi"]:
                warroom_json["kpi"] = ["库存"]
            else:
                warroom_json["kpi"] = ["转化"]
        warroom_json["display"] = "分布"
    warroom_json = json.dumps(warroom_json, ensure_ascii=False)
    return warroom_json



def warroom_data_clean(data: list) -> list:
    """清洗沙盘接口返回的异常数据

    Args:
        data (list): 沙盘接口返回的数据

    Returns:
        list: 清洗后沙盘接口返回的数据
    """
    # 不展示未知
    data_clean = [i for i in data if i["location"] != "未知" and i["model"] != "未知" and i["model"] is not None]
    return data_clean


def answer_warroom(format_json,kpi,language):
    """根据语言和问题意图输出文字回复

    Args:
        format_json (String): 进行数据接口查询的json
        kpi (String): 数据查询指标
        language (String): 问题的语言

    Returns:
        answer (String): 数据查询的文字回复
    """
    format_json = json.loads(format_json)
    today = format_json["today"]
    start_time = format_json["start_time"]
    end_time = format_json["end_time"]
    date_type = format_json["date_type"]
    if today == "True":
        answer = sx_warroom_reply["实时数据"][language].format(time.strftime('%Y-%m-%d %H:%M:%S',time.localtime()))
    else:
        if kpi in ["销量","市占","上险"]:
            intention = sx_answer_source[language]["上险"]
        else:
            intention = sx_answer_source[language]["转化"]
        if start_time == end_time:
            answer = answer_date(date_type,start_time,language) + ' ' + intention
        else:
            answer = sx_date_name['from'][language] + answer_date(date_type,start_time,language)+ sx_date_name['to'][language] + answer_date(date_type,end_time,language) + intention
    return answer



def answer_warroom_compare(time_list,kpi,language):
    """根据语言和问题意图输出文字回复

    Args:
        kpi (List): 数据查询指标
        language (String): 问题的语言

    Returns:
        answer (String): 数据查询的文字回复
    """
    t_list = []
    for i in time_list:
        if answer_date(i[2],i[0],language) == answer_date(i[2],i[1],language):
            t_list.append(answer_date(i[2],i[0],language))
        else:
            t_list.append(answer_date(i[2],i[0],language)+ sx_date_name['to'][language] + answer_date(i[2],i[1],language))

    time_str = ' & '.join(t_list)

    if kpi in ["销量","市占"]:
        intention = sx_answer_source[language]["上险"]
    else:
        intention = sx_answer_source[language]["转化"]
    answer = time_str + intention
    return answer


def json_to_list(format_json, kpi):
    """将Json转成List，调用不同的数据

    Args:
        format_json (Dict): 格式化后的JSON
        kpi (List): 查询的指标

    Returns:
        List: 接口调用List
    """
    if kpi == []:
        format_json_list = []
    else:
        if isinstance(format_json, dict):
            format_json_list = []
            tmp_json_list = []
            format_json_copy = copy.deepcopy(format_json)
            del format_json_copy['time']
            for k in kpi:
                tmp_kpi_json_list = []
                if format_json['time'] == []:
                    format_json['time'] = [' - ']
                for t in format_json['time']:
                    if '-' not in t:
                        t = t + '-' + t
                    format_json_copy['start_time'],format_json_copy['end_time'] = t.split('-')
                    format_json_clean = clean_json(format_json_copy,[k])
                    if format_json_clean == '{}' or format_json_clean in tmp_json_list:
                        continue
                    tmp_json_list.append(format_json_clean)
                    tmp_kpi_json_list.append({'json_origin':copy.deepcopy(format_json_copy),'json_clean':format_json_clean})
                if tmp_kpi_json_list != []:
                    k_display = json.loads(tmp_kpi_json_list[0]['json_clean'])['display']
                    k_today = json.loads(tmp_kpi_json_list[0]['json_clean'])['today']
                    format_json_list.append({'kpi':k,'display':k_display,'today':k_today,'json_list':tmp_kpi_json_list})
        else:
            format_json_list = []
    return format_json_list


def get_cleaned_json_list(json_list: list) -> list:
    cleaned_json_list = []
    for json_obj in json_list:
        kpi = clean_kpi(json_obj['kpi'])
        format_json_list = json_to_list(json_obj, kpi)
        cleaned_json_list += format_json_list
    return cleaned_json_list


def format_cleaned_json_list(cleaned_json_list):
    # 将cleaned_json_list中的json_list去重后格式化返回
    unique_json_clean_list = list(set([json_item['json_clean'] for item in cleaned_json_list if item for json_item in item['json_list']]))
    unique_json_clean_list = [json.loads(item) for item in unique_json_clean_list]

    content_list = list(set([f"{item['time']} {','.join(item['location'])} {','.join(item['model'])} {','.join(item['kpi'])} {item['display']}"
            for item in unique_json_clean_list]))

    content = "\n".join(content_list).replace('Nationwide', '全国')
    return content

#从list中去除空值
def remove_empty_from_list(input_list):
    return [d for d in input_list if d]


def filter_warroom_data_for_llm(warroom_data):
    filtered_data = []
    for item in warroom_data:
        if isinstance(item, dict):
            filtered_item = {}
            for key, value in item.items():
                # 修复点1：增加精确的键过滤条件
                if key.endswith("Name") or (key.find("Rate") != -1 and not key.endswith("CompleteRate")):
                    continue
                # 修复点2：递归处理所有嵌套结构
                if isinstance(value, (dict, list)):
                    value = filter_warroom_data_for_llm([value] if not isinstance(value, list) else value)
                filtered_item[key] = value
            filtered_data.append(filtered_item)
        elif isinstance(item, list):
            # 修复点3：保持列表结构递归处理
            filtered_sublist = filter_warroom_data_for_llm(item)
            filtered_data.extend(filtered_sublist)
        else:
            filtered_data.append(item)
    return filtered_data



async def get_warroom_data(warroom_json):
    # 沙盘数据查询和处理
    t1 = time.time()
    response_warroom_no_clean = await async_request_with_retries(warroom_url, warroom_json)
    t2 = time.time()
    save_process_log('数据查询接口返回response_warroom_no_clean',response_warroom_no_clean,t2-t1)

    if response_warroom_no_clean == []:
        return []
    # 清洗沙盘接口查询的数据
    t1 = time.time()
    response_warroom = warroom_data_clean(response_warroom_no_clean)
    t2 = time.time()
    save_process_log('清洗后的数据查询接口返回response_warroom',response_warroom,t2-t1)
    return response_warroom



def output_chart_data(response,json_clean,warroom_data,chart_type,type,language):
    if warroom_data:
        # 输出数据格式转化
        json_show = content_show(json_clean,warroom_data,chart_type,type,language)
        save_process_log('json_show',json_show)
        if json_show:
            response = response + json_show
    return response



async def format_chart_data(i):
    response = []

    language = '中文'
    tmp_display = i['display']
    tmp_today = i['today']


    # 图表展示选择
    chart_type,type = chart_select(tmp_display)
    save_process_log('数据的图表类型chart_type和type',chart_type + ' ' + ' '.join(type))

    # 对比图放在一张图展示
    if tmp_display in ['对比','排名'] or (tmp_display == '漏斗' and tmp_today != "True"):
        save_process_log('多图展示类型','一次性展示')
        warroom_data_list = []
        time_list = []
        for j in i['json_list']:
            tmp_json_clean = j['json_clean']

            # 沙盘查询json处理
            t1 = time.time()
            # 对比情况不需要转化，可以去掉？
            warroom_json = warroom_json_generate(tmp_json_clean)
            t2= time.time()
            save_process_log('调用数据查询接口的warroom_json',warroom_json,t2-t1)

            response_warroom = await get_warroom_data(warroom_json)
            if response_warroom:
                tmp_json_clean_obj = json.loads(tmp_json_clean)
                start_time = tmp_json_clean_obj['start_time']
                end_time = tmp_json_clean_obj['end_time']
                date_type = tmp_json_clean_obj['date_type']
                
                # 沙盘数据只保留汇总值
                for tmp_data in response_warroom:
                    if tmp_data['time'] == 'total':
                        if start_time == end_time:
                            tmp_data['time'] = start_time
                        else:
                            tmp_data['time'] = start_time + '-' + end_time
                        tmp_time_list = [start_time,end_time,date_type]
                        if tmp_time_list not in time_list:
                            time_list.append(tmp_time_list)
                        warroom_data_list.append(tmp_data)
        
        save_process_log('汇总后的数据查询结果warroom_data_list',warroom_data_list)
        response = output_chart_data(response,tmp_json_clean,warroom_data_list,chart_type,type,language)


    else:
        save_process_log('多图展示类型','分多次展示')
        for j in i['json_list']:
            tmp_json_clean = j['json_clean']
            tmp_json_clean_obj = json.loads(tmp_json_clean)
            t1 = time.time()
            warroom_json = warroom_json_generate(tmp_json_clean)
            t2= time.time()
            save_process_log('调用数据查询接口的warroom_json',warroom_json,t2-t1)
            response_warroom = await get_warroom_data(warroom_json)
            response = output_chart_data(response,tmp_json_clean,response_warroom,chart_type,type,language)

    return response