from utils.sx_dict import sx_kpi_name
from utils.chart_processors.base_chart_processor import BaseChartProcessor


def filter_target_data(data: dict, data_dim: str) -> bool:
    return data["time"] == "total" and (
        (data["model"].count("-") <= 1 and data_dim == "model") or data_dim != "model"
    )


class TargetCompletionChartProcessor(BaseChartProcessor):
    """Target Completion chart processor"""

    def __init__(self, format_json) -> None:
        super().__init__(format_json)

    def chart_process(self, data, language):
        kpi = list(set([i['kpiValues'].keys() for i in data]))
        chart_list = []
        title_location = "&".join([i for i in self.json_location])
        model_group = list(set([i['model'] for i in data if i['model'] in ['ID','MAIN','HIGH','SK']]))
        
        data = [filter_target_data(i, self.json_data_dim) for i in data]

        if self.json_today == 'True':
            for kpi_temp in kpi:
                title_kpi = sx_kpi_name[kpi_temp][language]
                
                if self.json_data_dim == 'model':
                    if model_group:
                        for tmp_model_group in model_group:
                            output_data = [{"completion": i['kpiValues'][kpi_temp],
                                            "name": i["location"]+'-'+i["model"],
                                            } for i in data if (tmp_model_group == i["model"] or tmp_model_group+'-' in i["model"])]

                            title_model = tmp_model_group
                    else:
                        output_data = [
                            {
                                "completion": i["kpiValues"][kpi_temp],
                                "name": i["location"] + "-" + i["model"],
                            }
                            for i in data
                        ]

                        title_model = "&".join([i for i in self.json_model])
                else:
                    output_data = [{"completion": i['kpiValues'][kpi_temp],
                                    "name": i["location"]+'-'+i["model"],
                                    } for i in data]

                    title_model = "&".join([i for i in self.json_model])
                title = title_location + ' ' + title_model + ' ' + title_kpi
                output_data = sorted(output_data, key=lambda x: x["completion"], reverse=True)
                legend = {"completion": sx_kpi_name["completion"][language]}
                chart_list.append({"type": "chart","title": title,"chart": {"chart_type": "target_completion","data":output_data,"legend":legend}})
        else:
            for kpi_temp in kpi:
                output_data = [{"target": i['kpiValues'][kpi_temp]["target"],
                                "completion": i['kpiValues'][kpi_temp]["completion"],
                                "completion_rate": i['kpiValues'][kpi_temp]["completionRate"],
                                "name": i["location"]+'-'+i["model"],
                                "time_schedule": i['kpiValues'][kpi_temp]["timeSchedule"],
                                } for i in data]

                title_model = "&".join([i for i in self.json_model])
                title_kpi = sx_kpi_name[kpi_temp][language]
                title = title_location + ' ' + title_model + ' ' + title_kpi
                output_data = sorted(output_data, key=lambda x: x["completion_rate"], reverse=True)
                # 什么情况下算一个目标达成图
                legend = {"completion": sx_kpi_name["completion"][language],"target": sx_kpi_name["target"][language],"time_schedule": sx_kpi_name["time_schedule"][language]}
                chart_list.append({"type": "chart","title": title,"chart": {"chart_type": "target_completion","data":output_data,"legend":legend}})
