


kpi_all = ['配件批售金额', '配件批售完成', '配件批售目标', '配件批售同比', '附件批售金额', '附件批售完成', '附件批售目标', 
           '附件批售同比', '配附件利润', '配附件完成', '配附件目标', '配附件同比', '进站台次', '进站台次同比', '经销商产值', 
           '经销商产值同比', '配件直销', '配件直销同比']




prompt_kpis_extract_template = """# 角色
你是一个企业内部数据分析助手，请根据用户问题识别出现的指标。

# 要求
1.请识别用户问题中与[指标列表]中相匹配的指标。
2.仅返回[指标列表]中匹配到的原始指标名称。
3.以列表形式返回，可以返回多个指标。

# 指标列表
{kpi_all}

# 用户问题列表
{questions}

# 回答
"""

prompt_operation_extract_paras_template = """# 角色
你是一个企业内部数据助手，请根据需要查询的指标，将用户问题转化成对应的接口参数。

# 信息
1.今天日期：{today}

# 查询指标
{kpis}

# 要求
1.问题中的相对时间请使用今天日期转化为绝对时间。
2.相对年份使用完整自然年；相对季度使用完整自然季度；相对月份使用完整自然月；相对周使用完整自然周（周从星期一开始）。
3.注意今天的日期，不要生成未来的数据查询。
4.请根据接口描述返回接口请求，并以JSON格式返回参数值，不要输出```json```。
5.最终结果请输出List[JSON]格式。

# 接口描述
接口可以进行单月和年度数据查询。
请求参数	数据类型	是否必填	描述
yearMonth	String	Y	年月（格式：yyyyMM）
queryType	String	Y	MTD/YTD(值：M/Y)

# 问题
{question}

# 回答
"""


extract_paras_prompt_dict = {
    "经营": prompt_operation_extract_paras_template, 
}



prompt_generate_chart_data_template = """# 角色
你是一个企业内部数据助手，请根据提供的数据，参照提供的图表样例，生成符合图表格式的结果。

# 信息
1.今天日期：{today}

# 用户问题
{question}

# 根据用户问题查询到的数据
{data}

# 图表格式样例
1.趋势图：{{'type': 'chart', 'title': '全国 SVW-VW 展厅客流', 'chart': {{'chart_type': 'mixed', 'xAxis': ['2025W12', '2025W13', '2025W14', '2025W15', '2025W16', '2025W17'], 'series': [{{'name': '展厅客流', 'data': [63919, 62724, 65534, 54305, 55577, 7669], 'type': 'bar'}}, {{'name': '到店转化率', 'percentage': True, 'data': [34.46, 38.16, 30.02, 31.9, 31.97, 46.15], 'type': 'line'}}], 'tab_list': [{{'type': 'bar_line', 'name': '柱状图和折线图'}}]}}}}
2.分布图：{{'type': 'chart', 'title': 'ID.3 SVW-VW 上险量 分布', 'chart': {{'chart_type': 'mixed', 'xAxis': ['华北', '华南', '中南', '华东', '华中', '北方', '西南', '西北'], 'series': [{{'name': '', 'percentage': False, 'data': [234, 117, 78, 369, 463, 34, 190, 50]}}], 'tab_list': [{{'type': 'bar', 'name': '柱状图'}}, {{'type': 'line', 'name': '折线图'}}, {{'type': 'pie', 'name': '饼图'}}]}}}}
3.表格：{{'type': 'table', 'table': {{'data': [{{'时间': '202401', '地区': '全国', '品牌/车型': 'SVW-VW', '上险量': 79538, '上险量环比': '35.98%', '上险量同比': '9.96%'}}]}}}}
4.目标完成图：{{'type': 'chart', 'title': '全国 SVW-VW 发票目标', 'chart': {{'chart_type': 'target_completion', 'data': [{{'target': 5260, 'completion': 3080, 'completion_rate': 59.0, 'name': '北方-SVW-VW', 'time_schedule': 53.0}}, {{'target': 6010, 'completion': 3382, 'completion_rate': 56.0, 'name': '西北-SVW-VW', 'time_schedule': 53.0}}], 'legend': {{'completion': '完成', 'target': '目标', 'time_schedule': '时间进度'}}}}}}
5.柱状图：{{'type': 'chart', 'title': '全国 Lavida&Passat 发票 对比', 'chart': {{'chart_type': 'mixed', 'xAxis': ['全国-Lavida', '全国-Passat'], 'series': [{{'name': '全国 Lavida&Passat 发票', 'percentage': False, 'data': [102002, 90784]}}], 'tab_list': [{{'type': 'bar', 'name': '柱状图'}}]}}}}
6.转化漏斗图：{{'type': 'chart', 'chart': {{'chart_type': 'funnel', 'list': [{{'leadsNotDuplicateCnt': 1473715, 'leadsNotDuplicateCntName': '客源', 'oppCnt': 1149266, 'oppCntName': '潜客', 'leadsTransferRate': 3.72, 'leadsTransferRateName': 'LTO', 'leadsArrivalRate': 9.73, 'leadsArrivalRateName': '线索到店率', 'leadsNotDuplicateCntTarget': 2779528, 'leadsNotDuplicateCompleteRate': 0.53, 'oppCntTarget': 2345252, 'oppCompleteRate': 0.49, 'timeSchedule': 0.52, 'finishLegendName': '完成', 'targetLegendName': '目标', 'timeLineLegendName': '时间进度', 'chartTitle': '202505 全国 SVW-VW 转化漏斗'}}]}}}}

# 要求
1.根据用户问题和数据，提取合适的数据，可以为多组数据。
2.根据选择的数据，挑选数据最适合的图表展示类型。
2.将数据按照图表的格式样例进行转化，图表里的标题和内容使用数据中的实际描述，图表格式不要输出```json```。
3.如果趋势图中的数据数值较大，请转换为适合展示的单位，并在标题中标注单位，仔细检查数值的位数，转换单位时不要出错。
4.最终结果请以List[JSON]格式输出，不要输出其他内容。
5.如果数据为空，仅返回空列表 []，不要有任何其他输出。

# 回答
"""




prompt_internal_query_json_template = """# 角色
你是一个企业内部数据分析助手，请根据以下要求回答用户的问题。

# 要求
0.请判断用户的问题是否是数据查询或数据分析类问题，如果不是，请直接返回{{}}；如果是，请继续下面的分析。
1.请根据用户[问题列表]，提取每个问题中的参数，单个问题以JSON格式返回，不要输出```json```。
2.生成JSON的要求如下：
2.1.time：
2.1.1.提取用户输入中出现的明确时间，忽略"每周"、"每月"、"每日"，并以列表(list)形式保存。
2.1.2.相对时间以基准日期{today}（{weekday}）进行转化，相对时间使用完整自然年、自然季度、自然月、自然周（周从星期一开始）。
2.1.3.识别时间范围的开始和结束时间，严格使用yyyymmdd-yyyymmdd的格式。
2.1.4.如果输入的时间信息无法解析或不存在，则返回一个空列表 []。
2.2.date_type：提取用户输入查询时间的类型，到天返回"day"，到周返回"week"，到月返回"month"，到年返回"year"; 如果信息不存在, 请使用 "" 作为值。
2.3.location：提取用户输入中出现的大区、省份、城市，无需进行分析和推理，并补充"大区"、"省"、"市"关键字，多值字段，用中括号 list 结构保存，如["全国"]，["华东大区"]，["北京市","上海市"]；无相关信息，则使用 ["全国"] 作为值；"各大区" 默认返回 ["全国"]。
2.4.model：提取用户输入中的车辆品牌或车辆类别或车型名称, 多值字段, 用中括号 list 结构保存；如果信息不存在, 请使用 [] 作为值
2.5.display：提取用户输入中隐含的数据展示形式，单值字段，仅包括"趋势"、"分布"、"排名"、"对比"、"同环比（yoy and mom）"、"同比（mom）"、"环比（yoy）"、"总计"，无需添加其他不在范围的词汇; 如果信息不存在, 请使用 "" 作为值。
2.6.data_dim：
2.6.1."by SVR"表示"按大区维度下钻"的含义。
2.6.3.如果用户输入明确说明查询更下一级地区维度或出现"各大区（by region）"、"各省份（by province）"、"各城市（by city）"、"哪个大区（which region）"、"哪个省份（which province）"、"哪个城市（which city）"，返回"location"；如果用户输入明确说明查询更下一级车型维度或出现"各车型（by model）"、"哪个车型（which model）"的含义，返回"model"。
2.6.3.如果用户输入没有明确说明按地区或车型维度下钻，即使含有地区或车型相关信息，也不返回"location"或"model"，只返回"all"。
2.7.today：如果用户输入明确提到今日数据或实时数据查询，返回"True"，否则，返回"False"。
2.8.not_total：如果用户输入需要每月、每周、每日的数据，返回"True"，否则，返回"False"。
2.9.template：如果用户明确提到使用模板查询进行查询，提取模板的名称，单值字段，字符串; 如果信息不存在, 请使用 "" 作为值。
2.10.kpi：提取用户输入中包含的[指标]，多值字段，用中括号 list 结构保存，如["销量","转化"]；如果信息不存在, 请使用 [] 作为值。
3.最终输出以"question_list"和"json_list"为key的JSON格式，其中"question_list"为用户输入的问题列表，"json_list"为每个问题对应的JSON格式。


# 指标
{kpi_all}

# 问题列表
{question_list}

#回答
"""

prompt_query_decomposition_add_history_template = """# 角色
你是一个分析师，请根据以下要求进行回答。

# 要求
1.请结合[历史问题]和[当前问题]，对[当前问题]进行理解和分析。
2.考虑要收集哪些维度的信息才能回答[当前问题]。
3.根据分析维度生成查询问题，每个分析维度包含一个问题，需要结合[历史问题]和[当前问题]的信息，保证生成问题的完整性，每个问题都需要带上时间维度。
4.先输出推理过程，中间输出一个<new_line>，再输出答案。
5.概念：
5.1.上汽大众大众品牌车型：帕萨特、朗逸、凌渡、途观、桑塔纳、Polo、途岳、途铠、途昂、途安、威然、辉昂、ID系列（为纯电，包括：ID.4 X、ID.6 X、ID.3）
5.2.车辆动力类型：汽油（ICE）、新能源（NEV）、纯电（BEV）
5.3.指标：{kpi_all}

# 样例
理解历史问题：上汽大众今年的销量表现是当前问题的基础。需要了解其销量是增长、下降还是持平，以及具体的数据和趋势。
理解当前问题：基于历史销量数据，探讨如何改善上汽大众的销量表现。这可能涉及产品、市场、销售策略、客户满意度等多个方面。
分析维度：
产品维度：当前产品线是否满足市场需求？是否需要推出新车型或改进现有车型？
市场维度：目标市场是否有变化？竞争对手的表现如何？
销售策略维度：现有的销售渠道和促销策略是否有效？
客户满意度维度：客户对产品和服务的反馈如何？是否有改进空间？
外部环境维度：宏观经济、政策法规等外部因素对销量有何影响？
<new_line>
{{
  "分析维度": [
    "产品维度",
    "市场维度",
    "销售策略维度",
    "客户满意度维度",
    "外部环境维度"
  ],
  "查询问题": [
    "上汽大众当前的产品线在市场上的竞争力如何？是否有需要改进或新增的车型？",
    "上汽大众的目标市场是否有变化？主要竞争对手的销量表现如何？",
    "上汽大众现有的销售渠道和促销策略是否有效？是否有优化的空间？",
    "客户对上汽大众产品和服务的满意度如何？有哪些常见的投诉或建议？",
    "去年上汽大众的转化情况在不同时间段（如季度、月份）有何变化？",
    "当前的宏观经济环境、政策法规等外部因素对上汽大众的销量有何影响？"
  ]
}}

# 历史问题
{history_question}

# 当前问题
{current_question}

# 回答"""



if __name__ == '__main__':
    question = '今年配件批售的金额是多少'
    prompt = prompt_kpis_extract_template.format(question=question, kpi_all=kpi_all)
    print(prompt)