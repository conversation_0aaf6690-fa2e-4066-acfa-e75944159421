import asyncio
import aiohttp
import time
import csv
import os
from datetime import datetime

# 提取基础 URL
BASE_URL = 'http://127.0.0.1:8000'
# 提取公共请求头
COMMON_HEADERS = {
    'accesstoken': 'Bearer your_token',
    'Content-Type': 'application/json'
}

# 压测配置
CONCURRENCY = 10  # 并发数
TEST_USER_ID = '16579'
TEST_CONVERSATION_ID = '16579-1'
TEST_MESSAGE_ID = '12345'


async def test_user_history(session, test_id):
    """测试用户历史接口"""
    start_time = time.time()
    user_history_url = f'{BASE_URL}/user/history'
    user_history_params = {
        'user_id': TEST_USER_ID
    }
    
    try:
        async with session.get(user_history_url, params=user_history_params, headers=COMMON_HEADERS) as response:
            await response.json()
            end_time = time.time()
            response_time = end_time - start_time
            return {
                'test_id': test_id,
                'api': 'user_history',
                'status': response.status,
                'response_time': response_time,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
            }
    except Exception as e:
        end_time = time.time()
        response_time = end_time - start_time
        return {
            'test_id': test_id,
            'api': 'user_history',
            'status': 'error',
            'error': str(e),
            'response_time': response_time,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
        }


async def test_conversation_history(session, test_id):
    """测试会话历史接口"""
    start_time = time.time()
    conversation_history_url = f'{BASE_URL}/conversation/history'
    conversation_history_params = {
        'conversation_id': f"{TEST_CONVERSATION_ID}-{test_id}"
    }
    
    try:
        async with session.get(conversation_history_url, params=conversation_history_params, headers=COMMON_HEADERS) as response:
            await response.json()
            end_time = time.time()
            response_time = end_time - start_time
            return {
                'test_id': test_id,
                'api': 'conversation_history',
                'status': response.status,
                'response_time': response_time,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
            }
    except Exception as e:
        end_time = time.time()
        response_time = end_time - start_time
        return {
            'test_id': test_id,
            'api': 'conversation_history',
            'status': 'error',
            'error': str(e),
            'response_time': response_time,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
        }


async def test_feedback_operate(session, test_id):
    """测试反馈操作接口"""
    start_time = time.time()
    feedback_operate_url = f'{BASE_URL}/feedback/operate'
    feedback_operate_data = {
        'message_id': f"{TEST_MESSAGE_ID}-{test_id}",
        'feedback_type': 'good',
        'feedback_content': 'This is a positive feedback.'
    }
    
    try:
        async with session.post(feedback_operate_url, json=feedback_operate_data, headers=COMMON_HEADERS) as response:
            await response.json()
            end_time = time.time()
            response_time = end_time - start_time
            return {
                'test_id': test_id,
                'api': 'feedback_operate',
                'status': response.status,
                'response_time': response_time,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
            }
    except Exception as e:
        end_time = time.time()
        response_time = end_time - start_time
        return {
            'test_id': test_id,
            'api': 'feedback_operate',
            'status': 'error',
            'error': str(e),
            'response_time': response_time,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
        }


async def run_tests():
    """运行所有测试"""
    async with aiohttp.ClientSession() as session:
        tasks = []
        # 创建并发测试任务
        for i in range(CONCURRENCY):
            tasks.append(test_user_history(session, i))
            tasks.append(test_conversation_history(session, i))
            tasks.append(test_feedback_operate(session, i))
        
        # 等待所有测试完成
        results = await asyncio.gather(*tasks)
        return results


def analyze_results(results):
    """分析测试结果"""
    # 按 API 类型分组结果
    api_results = {}
    for result in results:
        api = result['api']
        if api not in api_results:
            api_results[api] = []
        api_results[api].append(result)
    
    # 计算每个 API 的统计信息
    stats = {}
    for api, api_result in api_results.items():
        response_times = [r['response_time'] for r in api_result if r['status'] != 'error']
        if response_times:
            stats[api] = {
                'min': min(response_times),
                'max': max(response_times),
                'avg': sum(response_times) / len(response_times),
                'total': len(response_times),
                'errors': len(api_result) - len(response_times)
            }
        else:
            stats[api] = {
                'min': 0,
                'max': 0,
                'avg': 0,
                'total': 0,
                'errors': len(api_result)
            }
    
    return stats, api_results


def save_results_to_csv(results, filename):
    """保存结果到 CSV 文件"""
    with open(filename, 'w', newline='') as f:
        fieldnames = ['test_id', 'api', 'status', 'response_time', 'timestamp']
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        for result in results:
            # 只保留需要的字段
            row = {field: result.get(field, '') for field in fieldnames}
            writer.writerow(row)
    
    print(f"结果已保存到文件: {filename}")


def print_stats(stats):
    """打印统计信息到终端"""
    print("\n压测统计报告:")
    print("=" * 80)
    print(f"{'API':<20} {'最小响应时间(s)':<15} {'最大响应时间(s)':<15} {'平均响应时间(s)':<15} {'总请求数':<10} {'错误数':<10}")
    print("-" * 80)
    
    for api, stat in stats.items():
        print(f"{api:<20} {stat['min']:<15.4f} {stat['max']:<15.4f} {stat['avg']:<15.4f} {stat['total']:<10} {stat['errors']:<10}")
    print("=" * 80)


def print_csv_to_terminal(results):
    """将结果以 CSV 格式打印到终端"""
    print("\n压测详细结果:")
    print("=" * 80)
    print(f"{'测试ID':<8} {'API':<20} {'状态':<8} {'响应时间(s)':<15} {'时间戳':<30}")
    print("-" * 80)
    
    for result in results:
        status = result['status'] if isinstance(result['status'], str) else result['status']
        print(f"{result['test_id']:<8} {result['api']:<20} {status:<8} {result['response_time']:<15.4f} {result['timestamp']:<30}")
    print("=" * 80)


async def main():
    """主函数"""
    print(f"开始压测，并发数: {CONCURRENCY}")
    start_time = time.time()
    
    # 运行测试
    results = await run_tests()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 分析结果
    stats, api_results = analyze_results(results)
    
    # 打印结果到终端
    print_stats(stats)
    print_csv_to_terminal(results)
    
    # 保存结果到 CSV 文件
    unix_time = int(time.time())
    filename = f"history_date_{unix_time}.csv"
    save_results_to_csv(results, filename)
    
    print(f"\n压测总耗时: {total_time:.2f} 秒")
    print(f"总请求数: {len(results)}")
    print(f"平均每秒请求数: {len(results) / total_time:.2f}")


if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main()) 