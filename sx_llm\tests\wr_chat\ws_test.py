# pip install websocket-client==1.6.3
import websocket
import json
import threading
import time


def on_message(ws, message):
    # print(json.loads(message)['content'],end='',flush=True)
    print(json.loads(message))

def on_error(ws, error):
    print(error)
    ws.close()

def on_close(ws, close_status_code, close_msg):
    print("### closed ###")

def on_open(ws):
    print("Opened connection")
    # 启动一个新线程来处理用户输入
    input_thread = threading.Thread(target=handle_input, args=(ws,))
    input_thread.daemon = True
    input_thread.start()

def on_ping(ws, message):
    # print("Got a ping! A pong reply has already been automatically sent.")
    pass

def on_pong(ws, message):
    # print("Got a pong! No need to respond")
    pass

def handle_input(ws):
    while True:
        question = input("Enter a message to send (or type 'exit' to close): ")
        if question == "exit":
            ws.close()
            print("WebSocket connection closed.")

        if question.strip() == "":
            continue

        messageBody = {
            "userId": "test",
            "sessionId": "11",
            "questionText": question,
            "client": "1",
            "category": "1"
        }
        ws.send(json.dumps(messageBody))

if __name__ == "__main__":
    # uri = "ws://*************:8000/api/V1/chat/ws" # DEV
    # uri = "ws://*************:8080/api/V1/chat/ws" # 映射后
    # uri = "ws://*************:8000/api/V1/chat/ws" # 生产
    # uri = "ws://*************:8000/api/V1/chat/ws" # DEV2
    # uri = "ws://0.0.0.0:8000/api/V1/chat/ws/new" # dev local
    uri = "wss://sandbox-openapi.svw-volkswagen.com/robot/api/V1/chat/ws/new" # dev local 本地访问
    websocket.enableTrace(False)
    ws = websocket.WebSocketApp(uri,
                              on_open=on_open,
                              on_message=on_message,
                              on_error=on_error,
                              on_close=on_close,
                              on_ping=on_ping,
                              on_pong=on_pong)

    ws.run_forever()