import json

from utils.config import *
from functions.coze_workflow_executor import CozeWorkflowExecutor


# Coze相关参数
coze_workflow_url = 'https://api.coze.cn/v1/workflow/run?='



async def get_web_contents(query: str, count: int = 3) -> dict:
    executor = CozeWorkflowExecutor()
    workflow_id = '7470708539339489320'
    parameters = {'query_list': query, 'count': count}
    result =  await executor.execute_workflow(workflow_id, parameters)
    return json.loads(result.data)['output']


# 批量获取网页内容
async def get_web_contents_batch(query_list: list, count: int = 3) -> dict:
    executor = CozeWorkflowExecutor()
    workflow_id = '7474809031053377571'
    parameters = {'query_list': query_list, 'count': count}
    result =  await executor.execute_workflow(workflow_id, parameters)
    return json.loads(result.data)['output']


# 根据批量获取网页结果进行格式化
def format_web_contents_batch(web_contents: dict) -> str:
    formatted_contents = ''
    num = 1
    for data in web_contents:
        contents = data['data']['webPages']['value']
        for content in contents:
            name = content['name']
            snippet = content['snippet']
            url = content['url']
            formatted_contents += f'【参考网页{num}】\n{name}\n{url}\n{snippet}\n'
            num += 1
    return formatted_contents


def format_output_web_contents_batch(web_contents: dict) -> str:
    output_formatted_contents = ''
    num = 1
    for data in web_contents:
        contents = data['data']['webPages']['value']
        for content in contents:
            name = content['name']
            url = content['url']
            output_formatted_contents += f'【参考网页{num}】\n{name}\n{url}\n'
            num += 1
    return output_formatted_contents


def format_web_contents(web_contents: list) -> str:
    formatted_contents = ''
    num = 1
    for content in web_contents:
        name = content['name']
        snippet = content['snippet']
        url = content['url']
        formatted_contents += f'【参考网页{num}】\n{url}\n' + f'【网页标题】\n{name}\n' + f'【网页内容】\n{snippet}\n\n'
        num += 1
    return formatted_contents



if __name__ == '__main__':
    import time
    start = time.time()
    query_list = ["福建省会是什么",
                  "吉林省会是什么"]
    async def main():
        result = await get_web_contents_batch(query_list)
        print(format_web_contents_batch(result))
    import asyncio
    asyncio.run(main())
    # web_contents = get_web_contents('如果2030年只有一家合资车企活着，会是哪家，请给出理由', 50)
    # for i in web_content:
    #     print(i['name'])
    #     print(i['snippet'])
    #     print(i['url'])
    # print("\n\n\nweb_content", web_content)
    end = time.time()
    print(end - start)
    # print(format_web_contents(web_contents))