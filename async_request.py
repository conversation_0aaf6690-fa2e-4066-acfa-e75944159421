import aiohttp
import asyncio
import json

async def test():
    async def request_data(api, headers, data):
        try:
            # 手动拼接 URL
            api = api + '?' + '&'.join([f"{key}={value}" for key, value in data.items()])
            print("[异步请求] URL:", api)

            # 设置请求头
            headers = {
                'User-Agent': 'python-requests/2.32.3', 
                'Accept-Encoding': 'gzip, deflate', 
                'Accept': 'application/json',  # 明确要求 JSON 响应
                'Connection': 'keep-alive', 
                'Authorization': 'SYAAkjENRNIAFVoQKylHQ51nZRNeX12-'
            }
            print("[异步请求] Headers:", headers)

            async with aiohttp.ClientSession(
                cookie_jar=aiohttp.CookieJar(),
                connector=aiohttp.TCPConnector(ssl=False, force_close=True),
                version=aiohttp.HttpVersion11
            ) as session:
                async with session.get(
                    api,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10),
                    allow_redirects=True  # 允许重定向
                ) as response:
                    print("[异步响应] Headers:", response.headers)
                    print("Status Code:", response.status)
                    
                    # 读取原始内容
                    raw_data = await response.read()
                    print("Raw Data Length:", len(raw_data))
                    
                    # 检查内容类型
                    content_type = response.headers.get('Content-Type', '')
                    print(f"Content-Type: {content_type}")
                    
                    # 尝试解析响应内容
                    if len(raw_data) > 0:
                        if 'application/json' in content_type:
                            return await response.json()
                        else:
                            # 如果是HTML但可能包含JSON数据，尝试提取
                            try:
                                text_content = raw_data.decode('utf-8')
                                print("响应内容前100个字符:", text_content[:100])
                                
                                # 尝试强制解析为JSON
                                try:
                                    return json.loads(text_content)
                                except json.JSONDecodeError:
                                    print("无法将响应内容解析为JSON")
                                    return None
                            except UnicodeDecodeError:
                                print("无法解码响应内容")
                                return None
                    else:
                        print("响应内容为空")
                        return None
        except Exception as e:
            print(f"请求出错: {e}")
            return None
    
    # 使用与同步请求完全相同的参数
    response = await request_data(
        api="http://10.122.31.36:8080/report/mobile/wechat/sv/afterSaleJyController/queryJyIndex",
        headers={},  # 实际头已在函数内覆盖
        data={"yearMonth": "202301", "queryType": "M"}  # 与同步请求保持一致
    )
    print("Final Response:", response)

asyncio.run(test()) 