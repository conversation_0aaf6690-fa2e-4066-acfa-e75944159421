# import base64
# import io, requests
# from IPython.display import Image, display
# from PIL import Image as im
# import matplotlib.pyplot as plt

# def mm(graph):
#     graphbytes = graph.encode("utf8")
#     base64_bytes = base64.urlsafe_b64encode(graphbytes)
#     base64_string = base64_bytes.decode("ascii")
#     img = im.open(io.BytesIO(requests.get('https://mermaid.ink/img/' + base64_string).content))
#     plt.imshow(img)
#     plt.axis('off') # allow to hide axis
#     plt.savefig('image.png', dpi=1200)

# mm("""
# graph LR;
#     A--> B & C & D
#     B--> A & E
#     C--> A & E
#     D--> A & E
#     E--> B & C & D
# """)


# import mermaid as md
# from mermaid.graph import Graph

# sequence = Graph('Sequence-diagram',"""
# stateDiagram-v2
#     [*] --> Still
#     Still --> [*]

#     Still --> Moving
#     Moving --> Still
#     Moving --> Crash
#     Crash --> [*]
# """)
# render = md.Mermaid(sequence)
# render # !! note this only works in the notebook that rendered the html.

# import requests

# open_api_url = "172.20.242.32:28771"

# def get_secret_keys():
#     url = f"http://{open_api_url}/account-config/v1/private/account/deepSeek"
#     headers = {"Content-Type": "application/json"}
#     result = requests.get(url, headers=headers).json()
#     secret_keys = result["data"]
#     return secret_keys

# print(get_secret_keys())

# import os
# import json
# import asyncio
# from redis.asyncio import Redis

# from utils.env_config import redis_env
# from utils.config import REDIS_PASSWORD

# class RedisDB:
#     def __init__(self):
#         # 直接创建异步连接对象
#         self.config = self.get_config()
#         self.password = REDIS_PASSWORD
#         self.host = self.config['host']
#         self.port = self.config['port']
#         self.db = self.config['db']
#         self.redis_connection = None

#     def get_config(self):
#         parent_dir = os.path.dirname(os.path.abspath(__file__))
#         file_path = os.path.join(parent_dir, 'config.json')
#         with open(file_path, 'r') as f:
#             config = json.load(f)['Redis'][redis_env]
#         return config


#     async def connect(self):
#         # 创建异步连接
#         self.redis_connection = await Redis.from_url(
#             f"redis://:{self.password}@{self.host}:{self.port}",
#             decode_responses=True,
#             db=self.db
#         )


#     async def insert_conversation_message(self, key, message):
#         await self.redis_connection.rpush(key, message)

#     async def set_key_value(self, key, value):
#         await self.redis_connection.set(key, value)
        
#     async def get_keys(self, keys):
#         return await self.redis_connection.mget(keys)
    
#     async def get_key(self, key):
#         return await self.redis_connection.get(key)

#     async def get_history_all(self, key):
#         return await self.redis_connection.lrange(key, 0, -1)

#     async def close(self):
#         await self.redis_connection.aclose()

# if __name__ == '__main__':
# # 使用示例
#     async def main():
#         redis_db = RedisDB()
#         await redis_db.connect()
#         try:
#             # await redis_db.insert_conversation_message('user:test_key1111', '你好')
#             messages = await redis_db.get_history_all('test_key1232131231')
#             aaaa = [{k: v for k, v in json.loads(item).items() if k in ['role', 'content']} for item in messages]
#             print(aaaa)
#             print(messages)
#         finally:
#             await redis_db.close()

#     # 运行异步主函数
#     asyncio.run(main())
#     # redis_db = RedisDB()
#     # print(redis_db.config)

# import json
# format_json = json.dumps({"time": "202503-202503", "start_time": "202503", "end_time": "202503", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["总库存"], "display": "分布", "today": "False", "data_dim": "all", "not_total": "False", "template_id": ""},ensure_ascii=False)
# data = [{'time': 'total', 'location': '全国', 'parentLocation': '全国', 'model': 'SVW-VW', 'brand': None, 'kpiValues': {'stockTotalCnt': 154054}}, {'time': '202503', 'location': '全国', 'parentLocation': '全国', 'model': 'SVW-VW', 'brand': None, 'kpiValues': {'stockTotalCnt': 154054}}]
# chart_type = 'mixed'
# type = ["bar","line",'pie']
# language = '英文'
# from utils.sx_chart_process import content_show
# print(content_show(format_json,data,chart_type,type,language))

# import json
# a = '{"a":1,"b":2}'
# print(a)
# b = json.loads(a)
# b['a'] = 3
# print(b)
# print(a)



# warroom_data = [{'type': 'text', 'text': {'content': '2025年4月 转化数据：'}}, {'type': 'chart', 'chart': {'chart_type': 'funnel', 'list': [{'leadsNotDuplicateCnt': 119575, 'leadsNotDuplicateCntName': '客源', 'deliveryEinvoiceNotSvkCnt': 3145, 'deliveryEinvoiceNotSvkCntName': '零售发票', 'orderCnt': 3302, 'orderCntName': '订单', 'oppTestdriveCnt': 3702, 'oppTestdriveCntName': '试乘试驾', 'oppWalkinCnt': 9471, 'oppWalkinCntName': '展厅客流', 'oppCnt': 109771, 'oppCntName': '潜客', 'leadsTransferRate': 2.76, 'leadsTransferRateName': 'LTO', 'leadsArrivalRate': 7.92, 'leadsArrivalRateName': '线索到店率', 'testdriveRate': 39.09, 'testdriveRateName': '试乘试驾率', 'oppWalkinTransferRate': 34.86, 'oppWalkinTransferRateName': '到店转化率', 'orderDeliveryNotSvkRate': 95.25, 'orderDeliveryNotSvkRateName': '订单成交率', 'leadsNotDuplicateCntTarget': 2731860, 'leadsNotDuplicateCompleteRate': 0.04, 'oppCntTarget': 2081853, 'oppCompleteRate': 0.05, 'oppWalkinCntTarget': 279923, 'oppWalkinCompleteRate': 0.03, 'oppTestdriveCntTarget': 118019, 'oppTestdriveCompleteRate': 0.03, 'orderCntTarget': 92663, 'orderCompleteRate': 0.04, 'deliveryEinvoiceNotSvkCntTarget': 87140, 'deliveryEinvoiceNotSvkCompleteRate': 0.04, 'timeSchedule': 0.07, 'finishLegendName': '完成', 'targetLegendName': '目标', 'timeLineLegendName': '时间进度', 'chartTitle': 'total 全国 SVW-VW 转化漏斗'}, {'leadsNotDuplicateCnt': 119575, 'leadsNotDuplicateCntName': '客源', 'deliveryEinvoiceNotSvkCnt': 3145, 'deliveryEinvoiceNotSvkCntName': '零售发票', 'orderCnt': 3302, 'orderCntName': '订单', 'oppTestdriveCnt': 3702, 'oppTestdriveCntName': '试乘试驾', 'oppWalkinCnt': 9471, 'oppWalkinCntName': '展厅客流', 'oppCnt': 109771, 'oppCntName': '潜客', 'leadsTransferRate': 2.76, 'leadsTransferRateName': 'LTO', 'leadsArrivalRate': 7.92, 'leadsArrivalRateName': '线索到店率', 'testdriveRate': 39.09, 'testdriveRateName': '试乘试驾率', 'oppWalkinTransferRate': 34.86, 'oppWalkinTransferRateName': '到店转化率', 'orderDeliveryNotSvkRate': 95.25, 'orderDeliveryNotSvkRateName': '订单成交率', 'leadsNotDuplicateCntTarget': 2731860, 'leadsNotDuplicateCompleteRate': 0.04, 'oppCntTarget': 2081853, 'oppCompleteRate': 0.05, 'oppWalkinCntTarget': 279923, 'oppWalkinCompleteRate': 0.03, 'oppTestdriveCntTarget': 118019, 'oppTestdriveCompleteRate': 0.03, 'orderCntTarget': 92663, 'orderCompleteRate': 0.04, 'deliveryEinvoiceNotSvkCntTarget': 87140, 'deliveryEinvoiceNotSvkCompleteRate': 0.04, 'timeSchedule': 0.07, 'finishLegendName': '完成', 'targetLegendName': '目标', 'timeLineLegendName': '时间进度', 'chartTitle': '202504 全国 SVW-VW 转化漏斗'}]}}]
# warroom_data = [{'type': 'text', 'text': {'content': 'From 2024W06 to 2024W09 Conversion Data:'}}, {'type': 'chart', 'title': 'Nationwide SVW-VW Showroom Traffic', 'chart': {'chart_type': 'mixed', 'xAxis': ['2024W06', '2024W07', '2024W08', '2024W09'], 'series': [{'name': 'Showroom Traffic', 'data': [25228, 33453, 66710, 82938], 'type': 'bar'}, {'name': 'ST to NCO', 'percentage': True, 'data': [56.15, 23.07, 23.86, 19.13], 'type': 'line'}], 'tab_list': [{'type': 'bar_line', 'name': 'Bar & Line'}]}}, {'type': 'text', 'text': {'content': 'From 2025W04 to 2025W07 Conversion Data:'}}, {'type': 'chart', 'title': 'Nationwide SVW-VW Showroom Traffic', 'chart': {'chart_type': 'mixed', 'xAxis': ['2025W04', '2025W05', '2025W06', '2025W07'], 'series': [{'name': 'Showroom Traffic', 'data': [58109, 8363, 63741, 66132], 'type': 'bar'}, {'name': 'ST to NCO', 'percentage': True, 'data': [53.38, 39.57, 28.54, 28.18], 'type': 'line'}], 'tab_list': [{'type': 'bar_line', 'name': 'Bar & Line'}]}}, {'type': 'text', 'text': {'content': 'From 2024W06 to 2024W09 Conversion Data:'}}, {'type': 'chart', 'title': 'SVW-VW Nationwide ST to NCO Distribution', 'chart': {'chart_type': 'mixed', 'xAxis': ['huadong', 'huanan', 'zhongnan', 'xinan', 'xibei', 'beifang', 'huabei', 'huazhong'], 'series': [{'name': '', 'percentage': True, 'data': [25.96, 21.12, 30.63, 31.8, 22.58, 28.5, 22.73, 24.18]}], 'tab_list': [{'type': 'bar', 'name': 'Bar'}, {'type': 'line', 'name': 'Line'}]}}]
# warroom_data = [{'type': 'text', 'text': {'content': '2024年1月至2024年12月转化数据：'}}, {'type': 'chart', 'chart': {'chart_type': 'funnel', 'list': [{'leadsNotDuplicateCnt': 35117032, 'leadsNotDuplicateCntName': '客源', 'deliveryEinvoiceNotSvkCnt': 1008957, 'deliveryEinvoiceNotSvkCntName': '零售发票', 'orderCnt': 1252920, 'orderCntName': '订单', 'oppTestdriveCnt': 1618775, 'oppTestdriveCntName': '试乘试驾', 'oppWalkinCnt': 3889928, 'oppWalkinCntName': '展厅客流', 'oppCnt': 26440513, 'oppCntName': '潜客', 'leadsTransferRate': 3.57, 'leadsTransferRateName': 'LTO', 'leadsArrivalRate': 11.08, 'leadsArrivalRateName': '线索到店率', 'testdriveRate': 41.61, 'testdriveRateName': '试乘试驾率', 'oppWalkinTransferRate': 32.21, 'oppWalkinTransferRateName': '到店转化率', 'orderDeliveryNotSvkRate': 80.53, 'orderDeliveryNotSvkRateName': '订单成交率', 'leadsNotDuplicateCntTarget': 36727407, 'leadsNotDuplicateCompleteRate': 0.96, 'oppCntTarget': 28399579, 'oppCompleteRate': 0.93, 'oppWalkinCntTarget': 4474836, 'oppWalkinCompleteRate': 0.87, 'oppTestdriveCntTarget': 1801862, 'oppTestdriveCompleteRate': 0.9, 'orderCntTarget': 1364166, 'orderCompleteRate': 0.92, 'deliveryEinvoiceNotSvkCntTarget': 1264918, 'deliveryEinvoiceNotSvkCompleteRate': 0.8, 'timeSchedule': 1.0, 'finishLegendName': '完成', 'targetLegendName': '目标', 'timeLineLegendName': '时间进度', 'chartTitle': 'total 全国 SVW-VW 转化漏斗'}, {'leadsNotDuplicateCnt': 2939393, 'leadsNotDuplicateCntName': '客源', 'deliveryEinvoiceNotSvkCnt': 124599, 'deliveryEinvoiceNotSvkCntName': '零售发票', 'orderCnt': 145796, 'orderCntName': '订单', 'oppTestdriveCnt': 153349, 'oppTestdriveCntName': '试乘试驾', 'oppWalkinCnt': 372320, 'oppWalkinCntName': '展厅客流', 'oppCnt': 2187580, 'oppCntName': '潜客', 'leadsTransferRate': 4.96, 'leadsTransferRateName': 'LTO', 'leadsArrivalRate': 12.67, 'leadsArrivalRateName': '线索到店率', 'testdriveRate': 41.19, 'testdriveRateName': '试乘试驾率', 'oppWalkinTransferRate': 39.16, 'oppWalkinTransferRateName': '到店转化率', 'orderDeliveryNotSvkRate': 85.46, 'orderDeliveryNotSvkRateName': '订单成交率', 'leadsNotDuplicateCntTarget': 3225242, 'leadsNotDuplicateCompleteRate': 0.91, 'oppCntTarget': 2497312, 'oppCompleteRate': 0.88, 'oppWalkinCntTarget': 472396, 'oppWalkinCompleteRate': 0.79, 'oppTestdriveCntTarget': 181064, 'oppTestdriveCompleteRate': 0.85, 'orderCntTarget': 144259, 'orderCompleteRate': 1.01, 'deliveryEinvoiceNotSvkCntTarget': 122972, 'deliveryEinvoiceNotSvkCompleteRate': 1.01, 'timeSchedule': 1.0, 'finishLegendName': '完成', 'targetLegendName': '目标', 'timeLineLegendName': '时间进度', 'chartTitle': '202401 全国 SVW-VW 转化漏斗'}, {'leadsNotDuplicateCnt': 2347864, 'leadsNotDuplicateCntName': '客源', 'deliveryEinvoiceNotSvkCnt': 43755, 'deliveryEinvoiceNotSvkCntName': '零售发票', 'orderCnt': 62191, 'orderCntName': '订单', 'oppTestdriveCnt': 89259, 'oppTestdriveCntName': '试乘试驾', 'oppWalkinCnt': 203771, 'oppWalkinCntName': '展厅客流', 'oppCnt': 1678894, 'oppCntName': '潜客', 'leadsTransferRate': 2.65, 'leadsTransferRateName': 'LTO', 'leadsArrivalRate': 8.68, 'leadsArrivalRateName': '线索到店率', 'testdriveRate': 43.8, 'testdriveRateName': '试乘试驾率', 'oppWalkinTransferRate': 30.52, 'oppWalkinTransferRateName': '到店转化率', 'orderDeliveryNotSvkRate': 70.36, 'orderDeliveryNotSvkRateName': '订单成交率', 'leadsNotDuplicateCntTarget': 2028634, 'leadsNotDuplicateCompleteRate': 1.16, 'oppCntTarget': 1394505, 'oppCompleteRate': 1.2, 'oppWalkinCntTarget': 279826, 'oppWalkinCompleteRate': 0.73, 'oppTestdriveCntTarget': 97079, 'oppTestdriveCompleteRate': 0.92, 'orderCntTarget': 74859, 'orderCompleteRate': 0.83, 'deliveryEinvoiceNotSvkCntTarget': 67720, 'deliveryEinvoiceNotSvkCompleteRate': 0.65, 'timeSchedule': 1.0, 'finishLegendName': '完成', 'targetLegendName': '目标', 'timeLineLegendName': '时间进度', 'chartTitle': '202402 全国 SVW-VW 转化漏斗'}, {'leadsNotDuplicateCnt': 2814559, 'leadsNotDuplicateCntName': '客源', 'deliveryEinvoiceNotSvkCnt': 62807, 'deliveryEinvoiceNotSvkCntName': '零售发票', 'orderCnt': 83587, 'orderCntName': '订单', 'oppTestdriveCnt': 148481, 'oppTestdriveCntName': '试乘试驾', 'oppWalkinCnt': 324856, 'oppWalkinCntName': '展厅客流', 'oppCnt': 2092662, 'oppCntName': '潜客', 'leadsTransferRate': 2.97, 'leadsTransferRateName': 'LTO', 'leadsArrivalRate': 11.54, 'leadsArrivalRateName': '线索到店率', 'testdriveRate': 45.71, 'testdriveRateName': '试乘试驾率', 'oppWalkinTransferRate': 25.73, 'oppWalkinTransferRateName': '到店转化率', 'orderDeliveryNotSvkRate': 75.14, 'orderDeliveryNotSvkRateName': '订单成交率', 'leadsNotDuplicateCntTarget': 2691427, 'leadsNotDuplicateCompleteRate': 1.05, 'oppCntTarget': 2046553, 'oppCompleteRate': 1.02, 'oppWalkinCntTarget': 364573, 'oppWalkinCompleteRate': 0.89, 'oppTestdriveCntTarget': 145776, 'oppTestdriveCompleteRate': 1.02, 'orderCntTarget': 112870, 'orderCompleteRate': 0.74, 'deliveryEinvoiceNotSvkCntTarget': 103026, 'deliveryEinvoiceNotSvkCompleteRate': 0.61, 'timeSchedule': 1.0, 'finishLegendName': '完成', 'targetLegendName': '目标', 'timeLineLegendName': '时间进度', 'chartTitle': '202403 全国 SVW-VW 转化漏斗'}, {'leadsNotDuplicateCnt': 2656255, 'leadsNotDuplicateCntName': '客源', 'deliveryEinvoiceNotSvkCnt': 60886, 'deliveryEinvoiceNotSvkCntName': '零售发票', 'orderCnt': 75019, 'orderCntName': '订单', 'oppTestdriveCnt': 126153, 'oppTestdriveCntName': '试乘试驾', 'oppWalkinCnt': 281274, 'oppWalkinCntName': '展厅客流', 'oppCnt': 1928966, 'oppCntName': '潜客', 'leadsTransferRate': 2.82, 'leadsTransferRateName': 'LTO', 'leadsArrivalRate': 10.59, 'leadsArrivalRateName': '线索到店率', 'testdriveRate': 44.85, 'testdriveRateName': '试乘试驾率', 'oppWalkinTransferRate': 26.67, 'oppWalkinTransferRateName': '到店转化率', 'orderDeliveryNotSvkRate': 81.16, 'orderDeliveryNotSvkRateName': '订单成交率', 'leadsNotDuplicateCntTarget': 2580281, 'leadsNotDuplicateCompleteRate': 1.03, 'oppCntTarget': 1948999, 'oppCompleteRate': 0.99, 'oppWalkinCntTarget': 344058, 'oppWalkinCompleteRate': 0.82, 'oppTestdriveCntTarget': 143479, 'oppTestdriveCompleteRate': 0.88, 'orderCntTarget': 96721, 'orderCompleteRate': 0.78, 'deliveryEinvoiceNotSvkCntTarget': 92792, 'deliveryEinvoiceNotSvkCompleteRate': 0.66, 'timeSchedule': 1.0, 'finishLegendName': '完成', 'targetLegendName': '目标', 'timeLineLegendName': '时间进度', 'chartTitle': '202404 全国 SVW-VW 转化漏斗'}, {'leadsNotDuplicateCnt': 2512631, 'leadsNotDuplicateCntName': '客源', 'deliveryEinvoiceNotSvkCnt': 65454, 'deliveryEinvoiceNotSvkCntName': '零售发票', 'orderCnt': 84584, 'orderCntName': '订单', 'oppTestdriveCnt': 127900, 'oppTestdriveCntName': '试乘试驾', 'oppWalkinCnt': 297882, 'oppWalkinCntName': '展厅客流', 'oppCnt': 1842122, 'oppCntName': '潜客', 'leadsTransferRate': 3.37, 'leadsTransferRateName': 'LTO', 'leadsArrivalRate': 11.86, 'leadsArrivalRateName': '线索到店率', 'testdriveRate': 42.94, 'testdriveRateName': '试乘试驾率', 'oppWalkinTransferRate': 28.4, 'oppWalkinTransferRateName': '到店转化率', 'orderDeliveryNotSvkRate': 77.38, 'orderDeliveryNotSvkRateName': '订单成交率', 'leadsNotDuplicateCntTarget': 3075659, 'leadsNotDuplicateCompleteRate': 0.82, 'oppCntTarget': 2328982, 'oppCompleteRate': 0.79, 'oppWalkinCntTarget': 399275, 'oppWalkinCompleteRate': 0.75, 'oppTestdriveCntTarget': 169082, 'oppTestdriveCompleteRate': 0.76, 'orderCntTarget': 102545, 'orderCompleteRate': 0.82, 'deliveryEinvoiceNotSvkCntTarget': 94303, 'deliveryEinvoiceNotSvkCompleteRate': 0.69, 'timeSchedule': 1.0, 'finishLegendName': '完成', 'targetLegendName': '目标', 'timeLineLegendName': '时间进度', 'chartTitle': '202405 全国 SVW-VW 转化漏斗'}, {'leadsNotDuplicateCnt': 2632544, 'leadsNotDuplicateCntName': '客源', 'deliveryEinvoiceNotSvkCnt': 64534, 'deliveryEinvoiceNotSvkCntName': '零售发票', 'orderCnt': 78588, 'orderCntName': '订单', 'oppTestdriveCnt': 127479, 'oppTestdriveCntName': '试乘试驾', 'oppWalkinCnt': 290211, 'oppWalkinCntName': '展厅客流', 'oppCnt': 1927023, 'oppCntName': '潜客', 'leadsTransferRate': 2.99, 'leadsTransferRateName': 'LTO', 'leadsArrivalRate': 11.02, 'leadsArrivalRateName': '线索到店率', 'testdriveRate': 43.93, 'testdriveRateName': '试乘试驾率', 'oppWalkinTransferRate': 27.08, 'oppWalkinTransferRateName': '到店转化率', 'orderDeliveryNotSvkRate': 82.12, 'orderDeliveryNotSvkRateName': '订单成交率', 'leadsNotDuplicateCntTarget': 3095823, 'leadsNotDuplicateCompleteRate': 0.85, 'oppCntTarget': 2399726, 'oppCompleteRate': 0.8, 'oppWalkinCntTarget': 399046, 'oppWalkinCompleteRate': 0.73, 'oppTestdriveCntTarget': 162100, 'oppTestdriveCompleteRate': 0.79, 'orderCntTarget': 106477, 'orderCompleteRate': 0.74, 'deliveryEinvoiceNotSvkCntTarget': 98153, 'deliveryEinvoiceNotSvkCompleteRate': 0.66, 'timeSchedule': 1.0, 'finishLegendName': '完成', 'targetLegendName': '目标', 'timeLineLegendName': '时间进度', 'chartTitle': '202406 全国 SVW-VW 转化漏斗'}, {'leadsNotDuplicateCnt': 2950140, 'leadsNotDuplicateCntName': '客源', 'deliveryEinvoiceNotSvkCnt': 73495, 'deliveryEinvoiceNotSvkCntName': '零售发票', 'orderCnt': 87872, 'orderCntName': '订单', 'oppTestdriveCnt': 131956, 'oppTestdriveCntName': '试乘试驾', 'oppWalkinCnt': 310811, 'oppWalkinCntName': '展厅客流', 'oppCnt': 2191632, 'oppCntName': '潜客', 'leadsTransferRate': 2.98, 'leadsTransferRateName': 'LTO', 'leadsArrivalRate': 10.54, 'leadsArrivalRateName': '线索到店率', 'testdriveRate': 42.46, 'testdriveRateName': '试乘试驾率', 'oppWalkinTransferRate': 28.27, 'oppWalkinTransferRateName': '到店转化率', 'orderDeliveryNotSvkRate': 83.64, 'orderDeliveryNotSvkRateName': '订单成交率', 'leadsNotDuplicateCntTarget': 2349942, 'leadsNotDuplicateCompleteRate': 1.26, 'oppCntTarget': 1866798, 'oppCompleteRate': 1.17, 'oppWalkinCntTarget': 283715, 'oppWalkinCompleteRate': 1.1, 'oppTestdriveCntTarget': 121745, 'oppTestdriveCompleteRate': 1.08, 'orderCntTarget': 87604, 'orderCompleteRate': 1.0, 'deliveryEinvoiceNotSvkCntTarget': 87292, 'deliveryEinvoiceNotSvkCompleteRate': 0.84, 'timeSchedule': 1.0, 'finishLegendName': '完成', 'targetLegendName': '目标', 'timeLineLegendName': '时间进度', 'chartTitle': '202407 全国 SVW-VW 转化漏斗'}, {'leadsNotDuplicateCnt': 3462874, 'leadsNotDuplicateCntName': '客源', 'deliveryEinvoiceNotSvkCnt': 85083, 'deliveryEinvoiceNotSvkCntName': '零售发票', 'orderCnt': 103169, 'orderCntName': '订单', 'oppTestdriveCnt': 148858, 'oppTestdriveCntName': '试乘试驾', 'oppWalkinCnt': 360815, 'oppWalkinCntName': '展厅客流', 'oppCnt': 2544928, 'oppCntName': '潜客', 'leadsTransferRate': 2.98, 'leadsTransferRateName': 'LTO', 'leadsArrivalRate': 10.42, 'leadsArrivalRateName': '线索到店率', 'testdriveRate': 41.26, 'testdriveRateName': '试乘试驾率', 'oppWalkinTransferRate': 28.59, 'oppWalkinTransferRateName': '到店转化率', 'orderDeliveryNotSvkRate': 82.47, 'orderDeliveryNotSvkRateName': '订单成交率', 'leadsNotDuplicateCntTarget': 3087066, 'leadsNotDuplicateCompleteRate': 1.12, 'oppCntTarget': 2404527, 'oppCompleteRate': 1.06, 'oppWalkinCntTarget': 326438, 'oppWalkinCompleteRate': 1.11, 'oppTestdriveCntTarget': 138336, 'oppTestdriveCompleteRate': 1.08, 'orderCntTarget': 96194, 'orderCompleteRate': 1.07, 'deliveryEinvoiceNotSvkCntTarget': 94312, 'deliveryEinvoiceNotSvkCompleteRate': 0.9, 'timeSchedule': 1.0, 'finishLegendName': '完成', 'targetLegendName': '目标', 'timeLineLegendName': '时间进度', 'chartTitle': '202408 全国 SVW-VW 转化漏斗'}, {'leadsNotDuplicateCnt': 3522986, 'leadsNotDuplicateCntName': '客源', 'deliveryEinvoiceNotSvkCnt': 92847, 'deliveryEinvoiceNotSvkCntName': '零售发票', 'orderCnt': 119004, 'orderCntName': '订单', 'oppTestdriveCnt': 152207, 'oppTestdriveCntName': '试乘试驾', 'oppWalkinCnt': 376563, 'oppWalkinCntName': '展厅客流', 'oppCnt': 2761488, 'oppCntName': '潜客', 'leadsTransferRate': 3.38, 'leadsTransferRateName': 'LTO', 'leadsArrivalRate': 10.69, 'leadsArrivalRateName': '线索到店率', 'testdriveRate': 40.42, 'testdriveRateName': '试乘试驾率', 'oppWalkinTransferRate': 31.6, 'oppWalkinTransferRateName': '到店转化率', 'orderDeliveryNotSvkRate': 78.02, 'orderDeliveryNotSvkRateName': '订单成交率', 'leadsNotDuplicateCntTarget': 3490272, 'leadsNotDuplicateCompleteRate': 1.01, 'oppCntTarget': 2682158, 'oppCompleteRate': 1.03, 'oppWalkinCntTarget': 353127, 'oppWalkinCompleteRate': 1.07, 'oppTestdriveCntTarget': 150477, 'oppTestdriveCompleteRate': 1.01, 'orderCntTarget': 110493, 'orderCompleteRate': 1.08, 'deliveryEinvoiceNotSvkCntTarget': 111325, 'deliveryEinvoiceNotSvkCompleteRate': 0.83, 'timeSchedule': 1.0, 'finishLegendName': '完成', 'targetLegendName': '目标', 'timeLineLegendName': '时间进度', 'chartTitle': '202409 全国 SVW-VW 转化漏斗'}, {'leadsNotDuplicateCnt': 3383961, 'leadsNotDuplicateCntName': '客源', 'deliveryEinvoiceNotSvkCnt': 103229, 'deliveryEinvoiceNotSvkCntName': '零售发票', 'orderCnt': 136758, 'orderCntName': '订单', 'oppTestdriveCnt': 144060, 'oppTestdriveCntName': '试乘试驾', 'oppWalkinCnt': 374149, 'oppWalkinCntName': '展厅客流', 'oppCnt': 2672993, 'oppCntName': '潜客', 'leadsTransferRate': 4.04, 'leadsTransferRateName': 'LTO', 'leadsArrivalRate': 11.06, 'leadsArrivalRateName': '线索到店率', 'testdriveRate': 38.5, 'testdriveRateName': '试乘试驾率', 'oppWalkinTransferRate': 36.55, 'oppWalkinTransferRateName': '到店转化率', 'orderDeliveryNotSvkRate': 75.48, 'orderDeliveryNotSvkRateName': '订单成交率', 'leadsNotDuplicateCntTarget': 3713867, 'leadsNotDuplicateCompleteRate': 0.91, 'oppCntTarget': 2979836, 'oppCompleteRate': 0.9, 'oppWalkinCntTarget': 374459, 'oppWalkinCompleteRate': 1.0, 'oppTestdriveCntTarget': 146622, 'oppTestdriveCompleteRate': 0.98, 'orderCntTarget': 122480, 'orderCompleteRate': 1.12, 'deliveryEinvoiceNotSvkCntTarget': 116547, 'deliveryEinvoiceNotSvkCompleteRate': 0.89, 'timeSchedule': 1.0, 'finishLegendName': '完成', 'targetLegendName': '目标', 'timeLineLegendName': '时间进度', 'chartTitle': '202410 全国 SVW-VW 转化漏斗'}, {'leadsNotDuplicateCnt': 2900714, 'leadsNotDuplicateCntName': '客源', 'deliveryEinvoiceNotSvkCnt': 107604, 'deliveryEinvoiceNotSvkCntName': '零售发票', 'orderCnt': 127895, 'orderCntName': '订单', 'oppTestdriveCnt': 132167, 'oppTestdriveCntName': '试乘试驾', 'oppWalkinCnt': 341847, 'oppWalkinCntName': '展厅客流', 'oppCnt': 2279169, 'oppCntName': '潜客', 'leadsTransferRate': 4.41, 'leadsTransferRateName': 'LTO', 'leadsArrivalRate': 11.78, 'leadsArrivalRateName': '线索到店率', 'testdriveRate': 38.66, 'testdriveRateName': '试乘试驾率', 'oppWalkinTransferRate': 37.41, 'oppWalkinTransferRateName': '到店转化率', 'orderDeliveryNotSvkRate': 84.13, 'orderDeliveryNotSvkRateName': '订单成交率', 'leadsNotDuplicateCntTarget': 3691310, 'leadsNotDuplicateCompleteRate': 0.79, 'oppCntTarget': 2946624, 'oppCompleteRate': 0.77, 'oppWalkinCntTarget': 419769, 'oppWalkinCompleteRate': 0.81, 'oppTestdriveCntTarget': 164900, 'oppTestdriveCompleteRate': 0.8, 'orderCntTarget': 142534, 'orderCompleteRate': 0.9, 'deliveryEinvoiceNotSvkCntTarget': 127838, 'deliveryEinvoiceNotSvkCompleteRate': 0.84, 'timeSchedule': 1.0, 'finishLegendName': '完成', 'targetLegendName': '目标', 'timeLineLegendName': '时间进度', 'chartTitle': '202411 全国 SVW-VW 转化漏斗'}, {'leadsNotDuplicateCnt': 2993111, 'leadsNotDuplicateCntName': '客源', 'deliveryEinvoiceNotSvkCnt': 124664, 'deliveryEinvoiceNotSvkCntName': '零售发票', 'orderCnt': 148457, 'orderCntName': '订单', 'oppTestdriveCnt': 136906, 'oppTestdriveCntName': '试乘试驾', 'oppWalkinCnt': 355429, 'oppWalkinCntName': '展厅客流', 'oppCnt': 2333056, 'oppCntName': '潜客', 'leadsTransferRate': 4.96, 'leadsTransferRateName': 'LTO', 'leadsArrivalRate': 11.87, 'leadsArrivalRateName': '线索到店率', 'testdriveRate': 38.52, 'testdriveRateName': '试乘试驾率', 'oppWalkinTransferRate': 41.77, 'oppWalkinTransferRateName': '到店转化率', 'orderDeliveryNotSvkRate': 83.97, 'orderDeliveryNotSvkRateName': '订单成交率', 'leadsNotDuplicateCntTarget': 3697884, 'leadsNotDuplicateCompleteRate': 0.81, 'oppCntTarget': 2903559, 'oppCompleteRate': 0.8, 'oppWalkinCntTarget': 458154, 'oppWalkinCompleteRate': 0.78, 'oppTestdriveCntTarget': 181202, 'oppTestdriveCompleteRate': 0.76, 'orderCntTarget': 167130, 'orderCompleteRate': 0.89, 'deliveryEinvoiceNotSvkCntTarget': 148638, 'deliveryEinvoiceNotSvkCompleteRate': 0.84, 'timeSchedule': 1.0, 'finishLegendName': '完成', 'targetLegendName': '目标', 'timeLineLegendName': '时间进度', 'chartTitle': '202412 全国 SVW-VW 转化漏斗'}]}}]

# def filter_warroom_data_for_llm(warroom_data):
#     filtered_data = []
#     for item in warroom_data:
#         if isinstance(item, dict):
#             filtered_item = {}
#             for key, value in item.items():
#                 # 修复点1：增加精确的键过滤条件
#                 if key.endswith("Name") or (key.find("Rate") != -1 and not key.endswith("CompleteRate")):
#                     continue
#                 # 修复点2：递归处理所有嵌套结构
#                 if isinstance(value, (dict, list)):
#                     value = filter_warroom_data_for_llm([value] if not isinstance(value, list) else value)
#                 filtered_item[key] = value
#             filtered_data.append(filtered_item)
#         elif isinstance(item, list):
#             # 修复点3：保持列表结构递归处理
#             filtered_sublist = filter_warroom_data_for_llm(item)
#             filtered_data.extend(filtered_sublist)
#         else:
#             filtered_data.append(item)
#     return filtered_data


# result = filter_warroom_data_for_llm(warroom_data)
# print(result)



# import ast
# a = '[\n    {\n        "type": "chart",\n        "title": "taiCi 趋势 (单位: 千)",\n        "chart": {\n            "chart_type": "mixed",\n            "xAxis": ["total", "sb", "cb", "sg", "ybwx", "zhsp"],\n            "series": [\n                {\n                    "name": "amount",\n                    "data": [92.0, 9.9, 50.4, 15.8, 21.9, 8.0],\n                    "type": "bar"\n                },\n                {\n                    "name": "tbRate",\n                    "percentage": True,\n                    "data": [-8.22, -6.99, -6.77, -11.39, -12.80, -51.46],\n                    "type": "line"\n                },\n                {\n                    "name": "hbRate",\n                    "percentage": True,\n                    "data": [-64.73, -58.76, -63.72, -66.34, -67.60, -76.12],\n                    "type": "line"\n                }\n            ],\n            "tab_list": [\n                {\n                    "type": "bar_line",\n                    "name": "柱状图和折线图"\n                }\n            ]\n        }\n    },\n    {\n        "type": "chart",\n        "title": "chanZhi 趋势 (单位: 万)",\n        "chart": {\n            "chart_type": "mixed",\n            "xAxis": ["total", "sb", "cb", "sg", "ybwx", "zhsp"],\n            "series": [\n                {\n                    "name": "amount",\n                    "data": [20268.8, 472.1, 6398.9, 9529.5, 2644.6, 1221.9],\n                    "type": "bar"\n                },\n                {\n                    "name": "tbRate",\n                    "percentage": True,\n                    "data": [-8.70, -0.99, -4.22, -12.15, -11.26, 0.74],\n                    "type": "line"\n                },\n                {\n                    "name": "hbRate",\n                    "percentage": True,\n                    "data": [-65.13, -56.98, -63.34, -66.01, -67.36, -64.25],\n                    "type": "line"\n                }\n            ],\n            "tab_list": [\n                {\n                    "type": "bar_line",\n                    "name": "柱状图和折线图"\n                }\n            ]\n        }\n    }\n]'

# result = ast.literal_eval(a)
# print(result)


# a = [{'amount': '92.0千', 'tbRate': '-8.22%', 'name': 'taiCi', 'hbRate': '-64.73%', 'type': 'total'}, {'amount': '20268.8万', 'tbRate': '-8.70%', 'name': 'chanZhi', 'hbRate': '-65.13%', 'type': 'total'}, {'amount': '9.9千', 'tbRate': '-6.99%', 'name': 'taiCi', 'hbRate': '-58.76%', 'type': 'sb'}, {'amount': '472.1万', 'tbRate': '-0.99%', 'name': 'chanZhi', 'hbRate': '-56.98%', 'type': 'sb'}, {'amount': '50.4千', 'tbRate': '-6.77%', 'name': 'taiCi', 'hbRate': '-63.72%', 'type': 'cb'}, {'amount': '6398.9万', 'tbRate': '-4.22%', 'name': 'chanZhi', 'hbRate': '-63.34%', 'type': 'cb'}, {'amount': '15.8千', 'tbRate': '-11.39%', 'name': 'taiCi', 'hbRate': '-66.34%', 'type': 'sg'}, {'amount': '9529.5万', 'tbRate': '-12.15%', 'name': 'chanZhi', 'hbRate': '-66.01%', 'type': 'sg'}, {'amount': '21.9千', 'tbRate': '-12.80%', 'name': 'taiCi', 'hbRate': '-67.60%', 'type': 'ybwx'}, {'amount': '2644.6万', 'tbRate': '-11.26%', 'name': 'chanZhi', 'hbRate': '-67.36%', 'type': 'ybwx'}, {'amount': '8.0千', 'tbRate': '-51.46%', 'name': 'taiCi', 'hbRate': '-76.12%', 'type': 'zhsp'}, {'amount': '1221.9万', 'tbRate': '0.74%', 'name': 'chanZhi', 'hbRate': '-64.25%', 'type': 'zhsp'}]
# b = []
# b.extend(a)
# print(b)


# from test_a import add
# print(add(1,2))



# import requests
# import json

# url = "http://test-api.infra.sitc/aiservice/copilot/callcenter/chat"

# payload = json.dumps({
#   "dep_id": "CID-3",
#   "emp_id": "18333",
#   "dialog_id": "202502101357-07580",
#   "query": "班车时间",
#   "language": "zh",
#   "device_type": "pc",
#   "streaming": True
# })
# headers = {
#   'X-App-Id': '1V6OQsT9dghV',
#   'X-Timestamp': '20250210135716',
#   'X-Sequence-No': '20250210135716',
#   'Content-Type': 'application/json'
# }

# response = requests.post(url, headers=headers, data=payload, stream=True)

# try:
#     for line in response.iter_lines():
#         if line:
#             decoded_line = line.decode('utf-8')
#             if decoded_line.startswith('data:'):
#                 event_data = json.loads(decoded_line[5:].strip())
#                 print("Received event:", event_data)
# except requests.exceptions.ChunkedEncodingError as e:
#     print(f"Stream closed: {e}")


# error_msg = '代码执行错误'
# result = type('', (), {"content": [type('', (), {"text": error_msg})]})()
# print(result)
# a = '\n'.join([content.text for content in result.content])
# print(a)


import json
import re

# 模拟第一种可能的数据 (包含Markdown)
raw_data_1 = """```json
[
    {
        "type": "chart",
        "title": "上汽大众车系份额分布",
        "chart": {
            "chart_type": "mixed",
            "xAxis": ["01月", "02月", "03月"],
            "series": [
                {
                    "name": "未识别车系",
                    "data": [20.98, 15.73, 13.65],
                    "type": "bar"
                },
                {
                    "name": "帕萨特",
                    "data": [9.61, 10.91, 8.36],
                    "type": "bar"
                }
            ]
        }
    }
]
```"""

# 模拟第二种可能的数据 (纯字符串)
raw_data_2 = """[
    {
        "type": "chart",
        "title": "上汽大众车系份额分布",
        "chart": {
            "chart_type": "mixed",
            "xAxis": ["01月", "02月", "03月"],
            "series": [
                {
                    "name": "朗逸",
                    "data": [9.05, 8.96, 6.86],
                    "type": "bar"
                },
                {
                    "name": "ID.纯电未识别车系",
                    "data": [6.83, null, 6.81],
                    "type": "bar"
                }
            ]
        }
    }
]"""

def parse_llm_output_to_list(raw_data: str) -> list:
    """
    一个健壮的函数，用于解析大模型可能返回的多种格式的JSON字符串。

    Args:
        raw_data: 从大模型获取的原始字符串。

    Returns:
        解析后的Python列表，如果解析失败则返回空列表或抛出异常。
    """
    # 1. 移除字符串两端的空白字符 (包括换行符)
    clean_data = raw_data.strip()

    # 2. (核心步骤) 使用正则表达式查找被 `[` 和 `]` 包裹的JSON内容
    #    这可以很有效地处理前后可能存在的Markdown标记或其他文本
    match = re.search(r'\[.*\]', clean_data, re.DOTALL)
    if not match:
        print("错误：在字符串中未找到有效的列表/JSON结构。")
        return []

    json_string = match.group(0)

    # 3. 解析JSON字符串
    try:
        # 将提取出的字符串解析为Python列表
        parsed_list = json.loads(json_string)
        return parsed_list
    except json.JSONDecodeError as e:
        print(f"JSON 解析失败: {e}")
        # 这里可以根据你的需要返回空列表或重新抛出异常
        # return []
        raise e

# --- 测试 ---

# 测试第一种数据
print("--- 正在解析第一种数据 (包含Markdown) ---")
parsed_list_1 = parse_llm_output_to_list(raw_data_1)
print("解析结果:", parsed_list_1)
print("数据类型:", type(parsed_list_1))
if parsed_list_1:
    print("第一个元素的标题:", parsed_list_1[0].get('title'))

print("\n" + "="*50 + "\n")

# 测试第二种数据
print("--- 正在解析第二种数据 (纯字符串) ---")
parsed_list_2 = parse_llm_output_to_list(raw_data_2)
print("解析结果:", parsed_list_2)
print("数据类型:", type(parsed_list_2))
if parsed_list_2:
    print("第一个元素的系列名称:", parsed_list_2[0]['chart']['series'][0].get('name'))