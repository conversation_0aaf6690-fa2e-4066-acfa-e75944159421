accelerate==0.19.0
aiofiles==23.1.0
aiohttp==3.8.4
aiosignal==1.3.1
altair==5.0.1
anyio==3.7.0
appdirs==1.4.4
asgiref==3.7.2
astroid==2.13.5
asttokens==2.2.1
async-timeout==4.0.2
attrs==23.1.0
backcall==0.2.0
beautifulsoup4==4.12.2
bentoml==1.0.22
black==22.12.0
boltons==23.0.0
brotlipy==0.7.0
bs4==0.0.1
build==0.10.0
cattrs==23.1.2
certifi==2022.12.7
cffi==1.15.1
cfgv==3.3.1
charset-normalizer==2.0.4
circus==0.18.0
click==8.1.3
click-option-group==0.5.5
cloudpickle==2.2.1
cmake==3.26.3
coloredlogs==15.0.1
conda-content-trust==0.1.3
conda-package-handling==2.0.2
conda_package_streaming==0.7.0
contextlib2==21.6.0
contourpy==1.0.7
cpm-kernels==1.0.11
cryptography==39.0.1
cycler==0.11.0
dataclasses-json==0.5.8
datasets==2.12.0
decorator==5.1.1
deepmerge==1.1.0
Deprecated==1.2.14
dill==0.3.6
distlib==0.3.6
exceptiongroup==1.1.1
executing==1.2.0
fastapi==0.97.0
ffmpy==0.3.0
filelock==3.12.2
filetype==1.2.0
fonttools==4.40.0
frozenlist==1.3.3
fs==2.4.16
fsspec==2023.6.0
gradio==3.35.2
gradio_client==0.2.7
greenlet==2.0.2
grpcio==1.56.0
grpcio-health-checking==1.48.2
h11==0.14.0
httpcore==0.17.2
httpx==0.24.1
huggingface-hub==0.14.1
humanfriendly==10.0
identify==2.5.24
idna==3.4
importlib-metadata==6.0.1
inflection==0.5.1
iniconfig==2.0.0
ipython==8.10.0
isort==5.12.0
jedi==0.18.2
Jinja2==3.1.2
joblib==1.2.0
jsonpatch==1.32
jsonpointer==2.1
jsonschema==4.17.3
kiwisolver==1.4.4
langchain==0.0.214
langchainplus-sdk==0.0.17
lazy-object-proxy==1.9.0
linkify-it-py==2.0.2
lit==16.0.6
llama-index==0.6.32
markdown-it-py==2.2.0
MarkupSafe==2.1.2
marshmallow==3.19.0
marshmallow-enum==1.5.1
matplotlib==3.7.1
matplotlib-inline==0.1.6
mccabe==0.7.0
mdit-py-plugins==0.3.3
mdurl==0.1.2
mpmath==1.3.0
multidict==6.0.4
multiprocess==0.70.14
mypy==0.991
mypy-extensions==1.0.0
networkx==3.1
nltk==3.8.1
nodeenv==1.8.0
numexpr==2.8.4
numpy==1.25.0
nvidia-cublas-cu11==**********
nvidia-cuda-cupti-cu11==11.7.101
nvidia-cuda-nvrtc-cu11==11.7.99
nvidia-cuda-runtime-cu11==11.7.99
nvidia-cudnn-cu11==********
nvidia-cufft-cu11==*********
nvidia-curand-cu11==**********
nvidia-cusolver-cu11==********
nvidia-cusparse-cu11==*********
nvidia-nccl-cu11==2.14.3
nvidia-nvtx-cu11==11.7.91
openai==0.27.8
openapi-schema-pydantic==1.2.4
openllm==0.1.17
opentelemetry-api==1.17.0
opentelemetry-instrumentation==0.38b0
opentelemetry-instrumentation-aiohttp-client==0.38b0
opentelemetry-instrumentation-asgi==0.38b0
opentelemetry-instrumentation-grpc==0.38b0
opentelemetry-sdk==1.17.0
opentelemetry-semantic-conventions==0.38b0
opentelemetry-util-http==0.38b0
optimum==1.8.8
orjson==3.9.1
packaging==23.0
pandas==2.0.2
parso==0.8.3
pathspec==0.11.1
pexpect==4.8.0
pickleshare==0.7.5
Pillow==9.5.0
pip==23.0.1
pip-requirements-parser==32.0.1
pip-tools==6.13.0
platformdirs==3.8.0
pluggy==1.0.0
pre-commit==3.2.0
prometheus-client==0.17.0
prompt-toolkit==3.0.38
protobuf==3.20.2
psutil==5.9.5
ptyprocess==0.7.0
pure-eval==0.2.2
pyarrow==12.0.0
pycosat==0.6.4
pycparser==2.21
pydantic==1.10.9
pydub==0.25.1
Pygments==2.15.1
pylint==2.15.10
PyMySQL==1.0.3
pynvml==11.5.0
pyOpenSSL==23.0.0
pyparsing==3.1.0
pypdf==3.11.0
pyproject_hooks==1.0.0
pyrsistent==0.19.3
PySocks==1.7.1
pytest==7.2.1
pytest-asyncio==0.21.0
pytest-dotenv==0.5.2
python-dateutil==2.8.2
python-dotenv==1.0.0
python-json-logger==2.0.7
python-multipart==0.0.6
pytz==2023.3
PyYAML==6.0
pyzmq==25.0.2
rake-nltk==1.0.6
regex==2023.6.3
requests==2.28.1
responses==0.18.0
rich==13.3.5
ruamel.yaml==0.17.21
ruamel.yaml.clib==0.2.6
ruff==0.0.259
schema==0.7.5
scikit-learn==1.2.2
scipy==1.10.1
semantic-version==2.10.0
sentence-transformers==2.2.2
sentencepiece==0.1.99
setuptools==65.6.3
simple-di==0.1.5
six==1.16.0
sniffio==1.3.0
soupsieve==2.4.1
SQLAlchemy==2.0.17
stack-data==0.6.2
starlette==0.27.0
sympy==1.12
tabulate==0.9.0
tenacity==8.2.2
threadpoolctl==3.1.0
tiktoken==0.4.0
tokenizers==0.13.3
tomli==2.0.1
tomlkit==0.11.8
toolz==0.12.0
torch==2.0.1
torchvision==0.15.2
tornado==6.3.2
tqdm==4.65.0
traitlets==5.9.0
transformers==4.29.2
triton==2.0.0
types-docutils==********
types-pyOpenSSL==********
types-redis==*******
types-requests==*********
types-setuptools==********
types-urllib3==**********
typing_extensions==4.5.0
typing-inspect==0.8.0
tzdata==2023.3
uc-micro-py==1.0.2
urllib3==1.26.15
uvicorn==0.22.0
virtualenv==20.23.1
watchfiles==0.19.0
wcwidth==0.2.6
websockets==11.0.3
wheel==0.38.4
wrapt==1.15.0
xxhash==3.2.0
yarl==1.9.2
zipp==3.15.0
zstandard==0.19.0
