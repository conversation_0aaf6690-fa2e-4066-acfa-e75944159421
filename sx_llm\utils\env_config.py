# warroom查询接口
# dev
# warroom_url = "https://sandbox-openapi.svw-volkswagen.com/robot/v2/open/adapter/vw/listCompetitionAnalyse"
# warroom_url = "https://sandbox-openapi.svw-volkswagen.com/robot/v3/open/adapter/vw/listCompetitionAnalyse"
# prd
# warroom_url = "http://172.20.128.84:31510/robot/v2/open/adapter/vw/listCompetitionAnalyse"
warroom_url = "http://172.20.128.84:31510/robot/v3/open/adapter/vw/listCompetitionAnalyse"

# 沙盘数据最新日期查询
# dev
# latest_date_url = "https://sandbox-openapi.svw-volkswagen.com/robot/v3/open/adapter/queryLastDataDate"
# prd
latest_date_url = "http://172.20.128.84:31510/robot/v3/open/adapter/queryLastDataDate"
# latest_date_url = "https://openapi.svw-volkswagen.com/robot/v3/open/adapter/queryLastDataDate"

# Redis环境
redis_env = "local"

# 开放网关地址
# dev
open_api_url = "172.20.242.32:28771"
# prd
# open_api_url = "172.20.128.84:31510"

env = "dev"