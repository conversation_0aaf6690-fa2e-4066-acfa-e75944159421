import os
import json
import asyncio
from redis.asyncio import Redis

from utils.env_config import redis_env
from utils.config import REDIS_PASSWORD

class RedisDB:
    def __init__(self):
        # 直接创建异步连接对象
        self.config = self.get_config()
        self.password = REDIS_PASSWORD
        self.host = self.config['host']
        self.port = self.config['port']
        self.db = self.config['db']
        self.redis_connection = None

    def get_config(self):
        parent_dir = os.path.dirname(os.path.abspath(__file__))
        file_path = os.path.join(parent_dir, 'config.json')
        with open(file_path, 'r') as f:
            config = json.load(f)['Redis'][redis_env]
        return config


    async def connect(self):
        # 创建异步连接
        self.redis_connection = await Redis.from_url(
            "redis://:{self.password}@{self.host}:{self.port}db={self.db}",
            decode_responses=True
        )


    async def insert_conversation_message(self, key, message):
        await self.redis_connection.rpush(key, message)

    async def get_history_all(self, key):
        return await self.redis_connection.lrange(key, 0, -1)

    async def close(self):
        await self.redis_connection.aclose()

if __name__ == '__main__':
# 使用示例
    # async def main():
    #     redis_db = RedisDB()
    #     await redis_db.connect()
    #     try:
    #         await redis_db.insert_conversation_message('user:test_key1111', '你好')
    #         messages = await redis_db.get_history_all('test_key')
    #         print(messages)
    #     finally:
    #         await redis_db.close()

    # # 运行异步主函数
    # asyncio.run(main())
    redis_db = RedisDB()
    print(redis_db.config)