import json

from ..utils.clean_json import clean_json
# from utils import clean_json

def single_kpi_json_process(kpi, format_json, json_sets):
    """对单个kpi的json进行处理，返回处理后的json列表
    """
    single_kpi_json_list = []
    for t in format_json['time']:
        format_json['start_time'],format_json['end_time'] = t.split('-')
        format_json_clean = clean_json(format_json,[kpi])
        if format_json_clean == '{}' or format_json_clean in json_sets:
            continue
        json_sets.add(format_json_clean)
        single_kpi_json_list.append({'json_origin':format_json,'json_clean':format_json_clean})
    
    if not single_kpi_json_list:
        return None
        
    first_json = json.loads(single_kpi_json_list[0]['json_clean'])
    return {
        'kpi': kpi,
        'display': first_json['display'],
        'today': first_json['today'],
        'json_list': single_kpi_json_list
    }


if __name__ == '__main__':
    kpi = '销量'
    format_json = {'time': ['20240101-20241231'], 'date_type': 'year', 'model': [], 'location': ['华北大区'], 'display': '分布', 'today': 'False', 'data_dim': 'location', 'not_total': 'False', 'template': ''}
    json_sets = set()

    print(single_kpi_json_process(kpi, format_json, json_sets))