import ast
import copy
import json
import asyncio
import aiohttp  # 推荐使用aiohttp替代requests

# from utils.config import *
from prompt import *
from api import *




import openai
# 阿里云私有化部署
async def chat_by_token(content: list):
    client = openai.AsyncOpenAI(
        api_key="MWJhM2Y2ZTQ3NThjNWY2Y2M0Mjk2M2U0YzhhMDE2ZTZiZWY0MjZmMQ==",
        base_url="http://1680606300477185.cn-shanghai.pai-eas.aliyuncs.com/api/predict/deepseek_svw_1_a.deepseek_svw_1/v1"
    )
    response = await client.chat.completions.create(
        model="DeepSeek-R1",
        messages=content,
        stream=True
    )
    return response


async def llm_chat(prompt, **kwargs):
    prompt_query_analysis = prompt.format(**kwargs)
    content = [{"role": "user","content": prompt_query_analysis}]
    response = await chat_by_token(content)

    ### deepseek模型流式输出
    result = ''
    is_thinking = True
    async for chunk in response:
        content = chunk.choices[0].delta.content
        if content == '<think>':
            print("⭐思考中⭐")
            continue
        else:
            if content == '</think>':
                is_thinking = False
                print("⭐最终回答⭐")
                continue
            else:
                if not is_thinking:
                    result += content
        print(content, end='')
    return result




coze_workflow_url = 'https://api.coze.cn/v1/workflow/run?='
coze_api_key = "pat_Qs27QB54NUGZZBz972jrEjN7H0cVB0A693qVFfnsMWkpoqAVLHhF86TwgXN1VAif"
workflow_id = '7470708539339489320'  # WEB_SEARCH工作流的workflow_id






async def get_web_contents(query: str, count: int = 3) -> dict:
    payload = json.dumps({
        "workflow_id": workflow_id,
        "parameters": {"query": query, "count": count}
    })
    
    headers = {
        "Authorization": f"Bearer {coze_api_key}",
        "Content-Type": "application/json"
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(coze_workflow_url, headers=headers, data=payload) as response:
            response.raise_for_status()
            result = await response.json()
            return json.loads(result['data'])['output']



class DataAnalyseAgent:
    
    @staticmethod
    async def get_internal_query_analysis_results(question: str) -> dict:
        return await llm_chat(prompt_internal_query_json_template, question=question, today=today, weekday=weekday, kpi_all=kpi_all)
    
    @staticmethod
    def get_json_list(raw_json_str: str) -> list:
        raw_json = json.loads(raw_json_str)
        json_list = raw_json['json_list']
        return json_list

    @staticmethod
    def get_cleaned_json_list(json_list: list) -> list:
        cleaned_json_list = []
        for json_obj in json_list:
            kpi = clean_kpi(json_obj['kpi'])
            format_json_list = json_to_list(json_obj, kpi)
            print(format_json_list)
            cleaned_json_list += format_json_list
        return cleaned_json_list


    # @staticmethod
    # def data_query(format_json_list: list) -> list:
    #     response = []

    #     for i in format_json_list:

    #         tmp_kpi = i['kpi']
    #         tmp_display = i['display']
            
    #         # 图表展示选择
    #         chart_type,type = chart_select(tmp_display)

    #         for j in i['json_list']:
    #             tmp_json_clean = j['json_clean']
    #             warroom_json = warroom_json_generate(tmp_json_clean)
    #             response_warroom_no_clean = await async_request_with_retries(warroom_url, warroom_json)

    #             if response_warroom_no_clean == []:
    #                 continue
    #             else:
    #                 response_warroom = warroom_data_clean(response_warroom_no_clean)
    #                 if response_warroom == []:
    #                     continue
    #                 # 输出数据格式转化
    #                 json_show = content_show(tmp_json_clean,response_warroom,chart_type,type,'英文')
    #                 # 如果查询数据为空
    #                 if json_show == []:
    #                     continue
    #                 # 如果查询不为空
    #                 else:
    #                     qa_answer = answer_warroom(tmp_json_clean,tmp_kpi,'英文')
    #                     text_show = [{"type": "text","text": {"content": qa_answer}}]
    #                     response = response + text_show + json_show

    #     return response



    @staticmethod
    async def process_single_json(json_item, chart_type, type, tmp_kpi):
        """处理单个 json 请求的异步函数"""
        tmp_json_clean = json_item['json_clean']
        warroom_json = warroom_json_generate(tmp_json_clean)
        response_warroom_no_clean = await async_request_with_retries(warroom_url, warroom_json)

        if not response_warroom_no_clean:
            return []

        response_warroom = warroom_data_clean(response_warroom_no_clean)
        if not response_warroom:
            return []

        json_show = content_show(tmp_json_clean, response_warroom, chart_type, type, '英文')
        if not json_show:
            return []

        qa_answer = answer_warroom(tmp_json_clean, tmp_kpi, '英文')
        text_show = [{"type": "text", "text": {"content": qa_answer}}]
        return text_show + json_show

    @staticmethod
    async def process_format_json(item):
        """处理单个 format_json 项的异步函数"""
        tmp_kpi = item['kpi']
        tmp_display = item['display']
        
        # 图表展示选择
        chart_type, type = chart_select(tmp_display)
        
        # 并发处理该项下的所有 json_list
        tasks = [
            DataAnalyseAgent.process_single_json(json_item, chart_type, type, tmp_kpi)
            for json_item in item['json_list']
        ]
        results = await asyncio.gather(*tasks)
        
        # 合并该项的所有结果
        return [result for result in results if result]

    @staticmethod
    async def data_query(format_json_list: list) -> list:
        # 创建所有 format_json 项的任务
        tasks = [DataAnalyseAgent.process_format_json(item) for item in format_json_list]
        
        # 并发执行所有任务
        all_results = await asyncio.gather(*tasks)
        
        # 将所有结果扁平化处理成一个列表
        response = []
        for results in all_results:
            for result in results:
                response.extend(result)
                
        return response


    
    @staticmethod
    async def get_query_analysis_results(question):
        return await llm_chat(prompt_query_analysis_template, question=question)
    
    @staticmethod
    def clean_json(raw_json_str):
        # 去除json中的空格，将字符串解析为json
        json_str = raw_json_str.replace(' ', '')
        print(json_str)
        json_obj = json.loads(json_str)
        return json_obj
    
    @staticmethod
    def get_search_queries(json_obj):
        search_queries = json_obj['查询问题']
        return search_queries
    
    @staticmethod
    def search_web_contents(search_queries):
        web_contents = []
        for query in search_queries:
            web_contents += get_web_contents(query)
        return web_contents

    @classmethod
    async def get_warroom_data(cls, question: str) -> dict:
        # query_results = await DataAnalyseAgent.get_internal_query_analysis_results(question)
        # json_list = DataAnalyseAgent.get_json_list(query_results)
        # cleaned_json_list = DataAnalyseAgent.get_cleaned_json_list(json_list)
        cleaned_json_list = [{'kpi': '日均含大客户发票数', 'display': '趋势', 'today': 'False', 'json_list': [{'json_origin': {'date_type': 'quarter', 'location': ['全国'], 'model': ['途观L'], 'display': '趋势', 'data_dim': 'all', 'today': 'False', 'not_total': 'True', 'template': '', 'kpi': ['日均销量', '库存当量'], 'start_time': '20250101', 'end_time': '20250331'}, 'json_clean': '{"time": "202501-202502", "start_time": "202501", "end_time": "202502", "date_type": "month", "model": ["Tiguan"], "location": ["Nationwide"], "kpi": ["日均含大客户发票数", "含大客户发票数日均同比", "含大客户发票数日均环比"], "display": "趋势", "today": "False", "data_dim": "all", "not_total": "True", "template_id": ""}'}]}, {'kpi': '总库存当量', 'display': '趋势', 'today': 'False', 'json_list': [{'json_origin': {'date_type': 'quarter', 'location': ['全国'], 'model': ['途观L'], 'display': '趋势', 'data_dim': 'all', 'today': 'False', 'not_total': 'True', 'template': '', 'kpi': ['日均销量', '库存当量'], 'start_time': '20250101', 'end_time': '20250331'}, 'json_clean': '{"time": "202501-202502", "start_time": "202501", "end_time": "202502", "date_type": "month", "model": ["Tiguan"], "location": ["Nationwide"], "kpi": ["总库存当量", "总库存"], "display": "趋势", "today": "False", "data_dim": "all", "not_total": "True", "template_id": ""}'}]}]
        data_result = await DataAnalyseAgent.data_query(cleaned_json_list)
        return data_result
        




    @classmethod
    async def execute_full_flow(cls, question: str):
        # 获取查询分析结果
        analysis_results = await cls.get_query_analysis_results(question)
        
        # 清理并解析JSON
        clean_results = cls.clean_json(analysis_results)
        
        # 获取搜索查询
        search_queries = cls.get_search_queries(clean_results)
        
        
        # 创建异步任务列表（直接调用异步函数）
        tasks = [get_web_contents(q) for q in search_queries]
        
        # 并行执行所有异步请求
        results = await asyncio.gather(*tasks)
        
        # 扁平化结果列表
        return [item for sublist in results for item in sublist]
        
        
        # # 并行化网络请求
        # tasks = [asyncio.to_thread(get_web_contents, q) for q in search_queries]
        # results = await asyncio.gather(*tasks)
        # return [item for sublist in results for item in sublist]
        

        
        # # 搜索网络内容
        # web_contents = cls.search_web_contents(search_queries)
        
        # return web_contents
    
if __name__ == '__main__':
    # question = '分析去年途昂的销售转化情况'
    # result = DataAnalyseAgent.get_query_analysis_results(question)
    async def test_get_query_analysis_results():
        question = "查询本月途观L的销售情况"
        # result = await DataAnalyseAgent.get_internal_query_analysis_results(question)
        # print(f"结果: {result}")
        # json_list = DataAnalyseAgent.get_json_list(result)
        # json_list = [{'time': ['20250201-20250228'], 'date_type': 'month', 'location': ['全国'], 'model': ['途观L'], 'display': '', 'data_dim': 'model', 'today': 'False', 'not_total': 'True', 'template': '', 'kpi': ['销量']}, {'time': ['20250201-20250228'], 'date_type': 'month', 'location': ['全国'], 'model': ['途观L'], 'display': '', 'data_dim': 'location', 'today': 'False', 'not_total': 'True', 'template': '', 'kpi': ['销售表现']}]
        # print(f"json_list: {json_list}")
        # cleaned_json_list = DataAnalyseAgent.get_cleaned_json_list(json_list)
        # print(f"cleaned_json_list: {cleaned_json_list}")
        # data_result = await DataAnalyseAgent.data_query(cleaned_json_list)
        data_result = await DataAnalyseAgent.get_warroom_data(question)
        print(f"data_result: {data_result}")

    # 创建事件循环
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        # 运行测试函数
        loop.run_until_complete(test_get_query_analysis_results())
    finally:
        # 关闭事件循环
        loop.run_until_complete(loop.shutdown_asyncgens())
        loop.close()
    # print('\n---------------------------------------------------')
    # json_obj = DataAnalyseAgent.clean_json(result)
    # print(json_obj)
    # search_queries = DataAnalyseAgent.get_search_queries(json_obj)
    # print(search_queries)
    # search_queries = ['去年途昂的总销量和销售转化率具体数值是多少？', '去年途昂各季度/月份的销售转化率变化趋势如何？']
    # web_contents = DataAnalyseAgent.search_web_contents(search_queries)
    # print(web_contents)
    