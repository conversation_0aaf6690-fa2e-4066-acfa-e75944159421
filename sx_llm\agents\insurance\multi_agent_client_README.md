# 多智能体客户端 (Multi-Agent Client)

这个项目提供了一个使用A2A协议连接多个智能体的客户端实现。它允许您发现智能体、发送任务、获取任务状态和结果，以及管理与多个智能体的交互。特别地，它能够根据用户输入自动选择最合适的智能体来处理请求，并支持使用大模型来分析用户意图并选择智能体。

## 功能特点

- 发现和连接多个A2A协议智能体
- **根据用户输入自动选择合适的智能体**
  - **使用大模型分析用户意图并选择智能体**
  - 使用规则匹配方式作为备选
- 向不同智能体发送任务
- 获取任务状态和结果
- 查看智能体的技能和能力
- 等待任务完成并获取结果
- 提取智能体回复
- 维护会话状态和上下文

## 前提条件

- Python 3.8+
- 已安装以下依赖:
  - `httpx`
  - `httpx_sse` (用于流式传输，如果需要)
  - `google-generativeai` (用于大模型智能体选择器)
  - A2A协议相关类型和客户端库
- Google API密钥 (如果要使用大模型智能体选择器)

## 快速开始

1. 确保您已经启动了至少一个A2A协议智能体服务器
2. 如果要使用大模型智能体选择器，设置Google API密钥:

```bash
# Windows
set GOOGLE_API_KEY=your_api_key_here

# Linux/Mac
export GOOGLE_API_KEY=your_api_key_here
```

3. 运行演示脚本:

```bash
# Windows
run_multi_agent_demo.bat

# 或者直接使用Python
python multi_agent_client_demo.py
```

4. 默认情况下，演示将尝试连接到 `http://localhost:10000`
5. 如果您想连接到其他智能体，可以指定多个URL:

```bash
python multi_agent_client_demo.py --agents http://localhost:10000 http://localhost:10001
```

6. 您可以选择是否使用大模型来选择智能体:

```bash
# 使用大模型（默认）
python multi_agent_client_demo.py --use-llm

# 不使用大模型，仅使用规则匹配
python multi_agent_client_demo.py --no-llm

# 直接指定API密钥
python multi_agent_client_demo.py --api-key your_api_key_here
```

## 使用示例

### 基本用法

```python
import asyncio
import os
from multi_agent_client import MultiAgentClient

async def main():
    # 创建多智能体客户端，启用大模型智能体选择器
    api_key = os.environ.get("GOOGLE_API_KEY")  # 从环境变量获取API密钥
    client = MultiAgentClient(use_llm=True, api_key=api_key)

    # 发现并连接多个智能体
    await client.discover_agent("http://localhost:10000")
    await client.discover_agent("http://localhost:10001")

    # 列出所有已连接的智能体
    agents = client.list_agents()
    print(f"已连接的智能体: {agents}")

    # 使用大模型自动选择智能体并发送消息
    try:
        task = await client.send_message("你好，请介绍一下你自己")
        print(f"大模型选择了智能体: {client.current_agent}")

        # 等待任务完成
        completed_task = await client.wait_for_task_completion(task.id)

        # 提取智能体回复
        response = client.extract_agent_response(completed_task)
        print(f"智能体回复: {response}")
    except ValueError:
        print("无法自动选择智能体，请明确指定")

    # 手动指定智能体发送任务
    agent_name = agents[0]["name"]
    task = await client.send_task(agent_name, "你好，请介绍一下你自己")

    # 等待任务完成
    completed_task = await client.wait_for_task_completion(task.id)

    # 提取智能体回复
    response = client.extract_agent_response(completed_task)
    print(f"智能体回复: {response}")

# 运行主函数
asyncio.run(main())
```

### 不使用大模型的用法

```python
import asyncio
from multi_agent_client import MultiAgentClient

async def main():
    # 创建多智能体客户端，禁用大模型智能体选择器
    client = MultiAgentClient(use_llm=False)

    # 发现并连接智能体
    await client.discover_agent("http://localhost:10000")

    # 使用规则匹配方式自动选择智能体
    task = await client.send_message("你好，请介绍一下你自己")
    print(f"规则匹配选择了智能体: {client.current_agent}")

    # 等待任务完成
    completed_task = await client.wait_for_task_completion(task.id)

    # 提取智能体回复
    response = client.extract_agent_response(completed_task)
    print(f"智能体回复: {response}")

# 运行主函数
asyncio.run(main())
```

### 高级用法

```python
import asyncio
from multi_agent_client import MultiAgentClient

async def main():
    # 创建多智能体客户端
    client = MultiAgentClient()

    # 发现并连接多个智能体
    agent1 = await client.discover_agent("http://localhost:10000")
    agent2 = await client.discover_agent("http://localhost:10001")

    # 获取第一个智能体的技能
    skills = client.get_agent_skills(agent1.name)
    print(f"{agent1.name} 的技能: {skills}")

    # 使用自动选择功能发送消息
    # 这条消息明确提到了第一个智能体的名称，所以会自动选择它
    task1 = await client.send_message(f"请 {agent1.name} 分析以下数据: 1, 2, 3, 4, 5")

    # 这条消息会根据内容自动选择合适的智能体
    task2 = await client.send_message("生成一个创意故事")

    # 等待两个任务都完成
    completed_task1 = await client.wait_for_task_completion(task1.id)
    completed_task2 = await client.wait_for_task_completion(task2.id)

    # 提取并打印结果
    response1 = client.extract_agent_response(completed_task1)
    response2 = client.extract_agent_response(completed_task2)

    # 获取每个任务使用的智能体
    agent1_name = client.tasks[task1.id]["agent_name"]
    agent2_name = client.tasks[task2.id]["agent_name"]

    print(f"{agent1_name} 的回复: {response1}")
    print(f"{agent2_name} 的回复: {response2}")

    # 连续对话 - 会自动使用上一次的智能体
    follow_up_task = await client.send_message("请继续上一个故事")
    completed_follow_up = await client.wait_for_task_completion(follow_up_task.id)
    follow_up_response = client.extract_agent_response(completed_follow_up)

    print(f"后续对话回复: {follow_up_response}")
    print(f"使用的智能体: {client.current_agent}")

# 运行主函数
asyncio.run(main())
```

## API参考

### MultiAgentClient类

主要的客户端类，用于管理与多个智能体的连接和交互。

#### 属性

- `agents` - 已连接的智能体字典，键为智能体名称，值为(AgentCard, A2AClient)元组
- `tasks` - 已发送的任务字典，键为任务ID，值为任务信息
- `session_id` - 当前会话ID
- `current_agent` - 当前活跃的智能体名称
- `use_llm` - 是否使用大模型来选择智能体
- `agent_selector` - 大模型智能体选择器实例

#### 方法

- `discover_agent(agent_url)` - 发现并连接一个智能体
- `list_agents()` - 列出所有已连接的智能体
- `select_agent_for_message(message)` - 根据用户消息自动选择合适的智能体
- `send_message(message)` - 发送消息并自动选择合适的智能体
- `send_task(agent_name, message)` - 向指定智能体发送任务
- `get_task(task_id)` - 获取任务状态和结果
- `get_agent_skills(agent_name)` - 获取指定智能体的技能列表
- `wait_for_task_completion(task_id, timeout, interval)` - 等待任务完成
- `extract_agent_response(task)` - 从任务中提取智能体的回复文本
- `_extract_keywords(text)` - 从文本中提取关键词（内部方法）

### AgentSelector类

使用大模型来判断用户意图并选择合适的智能体。

#### 属性

- `api_key` - Google API密钥
- `model` - Gemini生成式模型实例
- `system_prompt_template` - 系统提示模板

#### 方法

- `select_agent(agents, user_message, current_agent)` - 使用大模型选择最合适的智能体

## 注意事项

- 确保您的智能体服务器支持A2A协议
- 默认情况下，客户端会在智能体的标准路径 `/.well-known/agent.json` 查找智能体卡片
- 任务完成可能需要一些时间，请耐心等待
- 使用大模型智能体选择器需要Google API密钥
- 如果没有API密钥或大模型调用失败，会自动回退到规则匹配方式
- 大模型选择器会分析用户意图、智能体技能和上下文，选择最合适的智能体
- 您可以通过设置`use_llm=False`来禁用大模型选择器，仅使用规则匹配方式
