import json
import asyncio
import traceback
from typing import List, Dict

from agents.base_agent import BaseAgent
from agents.manage.prompt import *



class ManageAgent(BaseAgent):
    def __init__(self):
        super().__init__()  # 调用父类初始化
        self.sub_questions: List[str] = []  # 初始化子问题列表
        self.question_classification: Dict[str, List[str]] = {}  # 初始化问题分类结果
        self.business_classification: Dict[str, List[str]] = {}  # 初始化业务域列表
        self.task_functions: List[str] = []
        self.data_all = [] # 用于收集全部的数据
        self.sub_agents = [] # 用于收集子任务


    # async def rewrite_question(self):
    #     # 问题改写
    #     # 样例：['今年的销量是多少', '今年的转化率是多少']
    #     yield await self.format_sse_message('\n\n**正在分解问题**\n', content_type='thinking')
    #     prompt = prompt_question_rewrite_template.format(current_question = self.question, history_question = self.history_question)
    #     content = [{"role": "user","content": prompt}]
    #     result = await self.process_llm.stream_response(content)
    #     self.sub_questions = ast.literal_eval(result)
    #     # if self.is_deep_thinking:
    #     yield await self.format_sse_message(f'问题分解结果：{self.sub_questions}', content_type='thinking')


    # async def classify_question(self):
    #     # 问题分类：分为数据查询、数据分析、其他。
    #     # 样例：{'数据类': ['今年的销量是多少', '今年的转化率是多少'], '其他': []}
    #     yield await self.format_sse_message('\n\n**正在问题业务分类**\n', content_type='thinking')
    #     prompt = prompt_question_classification_template.format(questions = self.sub_questions)
    #     content = [{"role": "user","content": prompt}]
    #     result = await self.process_llm_0324.stream_response(content)
    #     self.question_classification = json.loads(result)
    #     yield await self.format_sse_message(f'问题业务分类：{self.question_classification}', content_type='thinking')

    
    async def classify_business(self):
        # 业务域分类
        # 样例：{'销售': ['今年的销量是多少', '今年的转化率是多少']}
        yield await self.format_sse_message('\n\n**正在分析问题**\n', content_type='thinking')
        prompt = prompt_business_classification_template.format(sales_kpis = sales_kpis, aftersales_kpis = aftersales_kpis, current_question = self.question, history_question = self.history_question)
        self.save_log('新流程-业务域分类prompt',prompt)
        content = [{"role": "user","content": prompt}]
        result = await self.process_llm_0324.stream_response(content)
        self.business_classification = json.loads(result)
        self.save_log('新流程-子问题分解结果',self.business_classification)
        yield await self.format_sse_message(f'识别到多个子问题查询：{self.business_classification}', content_type='thinking')
        

    def create_task_functions(self):
        # 仅执行数据查询
        task_functions = []
        if '销售' in self.business_classification:
            question_list = self.business_classification['销售']
            for question in question_list:
                from agents.sales.sales_agent import SalesAgent
                agent = SalesAgent()
                agent.get_profiles_from_manager(self)
                agent.question = question
                task_functions.append(agent.event_stream_queue)
                # 将代理实例保存到列表
                self.sub_agents.append(agent)
        if '售后' in self.business_classification:
            from agents.aftersales.aftersales_agent import AftersalesAgent
            question_list = self.business_classification['售后']
            for question in question_list:
                agent = AftersalesAgent()
                agent.get_profiles_from_manager(self)
                agent.question = question
                task_functions.append(agent.event_stream_queue)
                # 将代理实例保存到列表
                self.sub_agents.append(agent)
        return task_functions


    def get_data_from_sub_agent(self):
        """从子代理中获取数据"""
        for sub_agent in self.sub_agents:
            if sub_agent.data_all:
                self.data_all.extend(sub_agent.data_all)
            if sub_agent.formatted_web_contents:
                self.formatted_web_contents += sub_agent.formatted_web_contents
            if sub_agent.charts:
                self.charts.extend(sub_agent.charts)
                
            
    async def event_stream(self):
        try:
            # 返回问题分解结果
            async for item in self.classify_business():
                yield f'data: {json.dumps(item,ensure_ascii=False)}\n\n'
            task_functions = self.create_task_functions()
            print(task_functions)
            async for item in self.ordered_stream(task_functions):
                yield f'data: {json.dumps(item,ensure_ascii=False)}\n\n'
            # 获取子代理中的数据，并返回图表
            self.get_data_from_sub_agent()
            async for item in self.get_analysis_result():
                yield f'data: {json.dumps(item,ensure_ascii=False)}\n\n'
        except asyncio.CancelledError:
            print("捕获到 CancelledError，开始清理")
            try:
                # 使用 shield 防止清理被取消
                await asyncio.shield(self.update_conversation_history())
            except Exception as e:
                e_msg = traceback.format_exc()
                self.save_log('异常捕获',e_msg)
                print('发生异常：',e_msg )
            print("客户端已断开连接")
            raise  # 重新抛出以便框架处理
        except Exception as e:
            e_msg = traceback.format_exc()
            print(e_msg)
            self.save_log('异常捕获',e_msg)
            item = await self.format_sse_message('对不起，由于问题过于复杂，我暂时无法回答', content_type="text", ended=True)
            yield f'data: {json.dumps(item,ensure_ascii=False)}\n\n'
            print('发生异常：',e_msg )
        finally:
            print("SSE返回已结束")
            

    async def get_analysis_result(self):
        if self.is_deep_thinking:
            self.thinking_content += '\n\n**整合分析**\n'
            yield await self.format_sse_message(content='\n\n**整合分析**\n', content_type="thinking")
        prompt = prompt_data_analysis_template.format(question=self.sub_questions, data=self.data_all, web_contents=self.formatted_web_contents, today=self.today)
        self.save_log('新流程-manage的数据分析prompt',prompt)
        content = {"role": "user","content": prompt}
        self.history_message.append(content)
        if self.is_deep_thinking:
            response = await self.analyse_llm.async_chat_by_token(self.history_message)
            is_thinking = True
            async for chunk in response:
                # 火山云流式输出
                if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
                    content = chunk.choices[0].delta.reasoning_content
                    content = content.replace('\n\n', '\n')
                    self.thinking_content += content
                    yield await self.format_sse_message(content, content_type="thinking")
                else:
                    if is_thinking:
                        is_thinking = False
                    content = chunk.choices[0].delta.content
                    content = content.replace('\n\n', '\n')
                    self.final_answer += content
                    yield await self.format_sse_message(content, content_type="text")
        else:
            response = await self.process_llm_0324.async_chat_by_token(self.history_message)
            result = ''
            async for chunk in response:
                content = chunk.choices[0].delta.content
                yield await self.format_sse_message(content=content, content_type="text")
                result += content
                self.final_answer += content
        if self.charts:
            for data in self.charts:
                if data['type'] == 'chart' or data['type'] == 'table':
                    if data not in self.charts:
                        yield await self.format_sse_message(data, content_type="chart", ended=False)
                self.save_log('新流程-最终的出图结果', self.charts)
        
        yield await self.format_sse_message('', content_type="text", ended=True)


if __name__ == '__main__':
    import asyncio
    # question = '今年呢'
    question = '去年配件批售金额'
    # question = '今年的销量和转化情况怎么样'
    agent = ManageAgent()
    agent.question = question
    agent.is_deep_thinking = False
    agent.is_internet_search = False
    agent.access_token = "NvGW4h-YTGQAFVoQKylHQ5PMIvjCvnk0"
    # manage_agent.history_question = ['去年的销量和转化情况怎么样']
    # asyncio.run(manage_agent.rewrite_question())
    # print(manage_agent.sub_questions)
    # asyncio.run(manage_agent.classify_question())
    # print(manage_agent.question_classification)
    # async def main():
    #     async for item in manage_agent.classify_business():
    #         print(item)
    # asyncio.run(main())
    # print(manage_agent.business_classification)
    
        # 测试事件流
    async def test_stream():
        async for event in agent.event_stream():
            print("收到事件:", event)  # 去掉前面的"data: "

    # 运行测试
    asyncio.run(test_stream())