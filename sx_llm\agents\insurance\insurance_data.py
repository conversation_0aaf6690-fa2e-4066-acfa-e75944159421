import time
from mcp.server.fastmcp import FastMCP

from utils.sx_request import async_request_with_retries
from utils.sx_log import save_process_log
from utils.env_config import warroom_url
from functions.warroom_data_process import warroom_data_clean

# 初始化MCP服务器
mcp = FastMCP("InsuranceDataServer")

@mcp.tool()
async def get_insurance_data(question: str):
    """
    根据用户问题查询车辆上险数据（包括销量和市占数据）
    :param question: 用户问题
    :return: 根据用户问题查询到的上险数据
    """

    t1 = time.time()
    response_warroom_no_clean = await async_request_with_retries(warroom_url, warroom_json)
    t2 = time.time()
    save_process_log('数据查询接口返回response_warroom_no_clean',response_warroom_no_clean,t2-t1)

    if response_warroom_no_clean == []:
        return []
    # 清洗沙盘接口查询的数据
    t1 = time.time()
    response_warroom = warroom_data_clean(response_warroom_no_clean)
    t2 = time.time()
    save_process_log('清洗后的数据查询接口返回response_warroom',response_warroom,t2-t1)
    return response_warroom

if __name__ == "__main__":
    mcp.run(transport="sse")