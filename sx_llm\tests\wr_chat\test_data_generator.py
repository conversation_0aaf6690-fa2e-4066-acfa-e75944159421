import pandas as pd 
import random





data_kpi = ["上险", "销量", "市占", "客源数", "潜客数", "到店数", 
                    "试乘试驾数", "订单数", "发票数", "批售数", "含大客户发票数", "库存",
                    "客源目标", "潜客目标", "到店目标", "试乘试驾目标", "订单目标", "发票目标",
                    "线索转化率（LTO）", "线索到店率", "试乘试驾率", "到店转化率", "订单成交率",
                    "批售目标", "含大客户发票目标"]

random_number = random.randint(0, 10)

random.sample(data_kpi, 3)

data_json = '{"start_time": "202301", "end_time": "202312", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["发票数", "订单成交率"], "display": "趋势", "today": "False", "data_dim": "all"}'