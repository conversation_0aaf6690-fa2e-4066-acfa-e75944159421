import random
import json
import os
from datetime import datetime, timedelta
from configure_logger import configure_logger


@configure_logger
class TestCaseGenerator:
    def __init__(self):
        pass

    def generate_type_1(self, num_cases, min_month, max_month, model_max=2, model_min=0, rssc_max=2, rssc_min=0, answer_type=1):
        test_cases = []
        csv_file = "model_name_insurance.tsv"
        for _ in range(num_cases):
            start_time = self._generate_date(min_month, max_month)
            end_time = self._generate_date(start_time, max_month)
            model_mappings = self._read_model_mappings(csv_file)
            model_name_cn = self._random_values(list(model_mappings.keys()), max_num=model_max, min_num=model_min)
            rssc_cn = self._random_values(["华东", "华南", "西南", "北方", "中南", "华北", "华中", "西北"], max_num=rssc_max, min_num=rssc_min)
            intention = self._random_values(["销量", "同比", "环比", "市占"], max_num=4, min_num=1)
            question = self._generate_question_type_1(start_time, end_time, model_name_cn, rssc_cn, intention, model_mappings)
            if answer_type == 1:
                answer = self._generate_answer(start_time, end_time, model_name_cn, rssc_cn, intention, realtime="False")
            elif answer_type == 2:
                answer = "数据查询"
            test_case = {"question": question, "answer": answer}
            test_cases.append(test_case)
        return test_cases

    def generate_type_2(self, num_cases, min_month, max_month, model_max=2, model_min=0, rssc_max=2, rssc_min=0, intention_min=1, intention_max=11, answer_type=1):
        test_cases = []
        csv_file = 'model_name_insurance.tsv'
        for _ in range(num_cases):
            start_time = self._generate_date(min_month, max_month)
            end_time = self._generate_date(start_time, max_month)
            model_mappings = self._read_model_mappings(csv_file)
            model_name_cn = self._random_values(list(model_mappings.keys()), max_num=model_max, min_num=model_min)
            rssc_cn = self._random_values(["华东", "华南", "西南", "北方", "中南", "华北", "华中", "西北"], max_num=rssc_max, min_num=rssc_min)
            intention = self._random_values(["客源", "潜客", "到店", "试乘试驾", "订单", "发票", "线索到店转化率", "试乘试驾转化率", "到店转化率", "LTO转化率", "订单成交转化率"], max_num=intention_max, min_num=intention_min)
            question = self._generate_question_type_2(start_time, end_time, model_name_cn, rssc_cn, intention, model_mappings)
            if answer_type == 1:
                answer = self._generate_answer(start_time, end_time, model_name_cn, rssc_cn, intention, realtime="False")
            elif answer_type == 2:
                answer = "数据查询"
            test_case = {"question": question, "answer": answer}
            test_cases.append(test_case)
        return test_cases

    def generate_type_3(self, num_cases, min_month, max_month, answer_type=1):
        test_cases = []
        for _ in range(num_cases):
            start_time = self._generate_date(min_month, max_month)
            end_time = self._generate_date(start_time, max_month)
            intention = self._random_values(["筛选", "返回"], max_num=1, min_num=1)[0]
            if intention == "返回":
                start_time = ""
                end_time = ""
            question = self._generate_question_type_3(start_time, end_time, intention)
            if answer_type == 1:
                answer = self._generate_answer_3(start_time, end_time, intention=intention)
            elif answer_type == 2:
                answer = "UI交互"
            test_case = {"question": question, "answer": answer}
            test_cases.append(test_case)
        return test_cases

    def generate_type_4(self, num_cases, file_name, answer_type=1, answer=""):
        test_cases = []

        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        data_file_path = os.path.join(parent_dir, 'data', file_name)

        with open(data_file_path, 'r', encoding='utf-8') as file:
            for line in file:
                line_json = json.loads(line)
                if answer_type == 2:
                    line_json["answer"] = answer if answer else "知识库查询"
                test_cases.append(line_json)
        
        random.shuffle(test_cases)
        return test_cases[:min(num_cases, len(test_cases))]

    def _random_true(self, probability=0.5):
        return random.random() < probability

    def _is_valid_date(self, *args):
        length_set = set(len(str(date_str)) for date_str in args)
        valid_lengths = {4, 5, 6, 0}

        return len(length_set) == 1 and length_set.pop() in valid_lengths

    def _generate_date(self, min_date="202201", max_date="202312"):
        # min_date 和 min_date 传入6位数字为年月，返回介于 min_date 和 max_date 的一个随机6位年月
        # min_date 和 min_date 传入5位数字为年季度，返回介于 min_date 和 max_date 的一个随机5位年季度
        # min_date 和 min_date 传入4位数字为年，返回介于 min_date 和 max_date 的一个随机4位年
        def generate_year_month_date(min_year_month, max_year_month):
            start_year = int(min_year_month[:4])
            end_year = int(max_year_month[:4])
            year = random.randint(start_year, end_year)
            if year == start_year and start_year == end_year:
                month = random.randint(int(min_year_month[4:]), int(max_year_month[4:]))
            elif year == start_year:
                month = random.randint(int(min_year_month[4:]), 12)
            elif year == end_year:
                month = random.randint(1, int(max_year_month[4:]))
            else:
                month = random.randint(1, 12)
            return f"{year:04d}{month:02d}"
        
        def generate_year_quarter_date(min_year_quarter, max_year_quarter):
            start_year = int(min_year_quarter[:4])
            end_year = int(max_year_quarter[:4])
            year = random.randint(start_year, end_year)
            if year == start_year and start_year == end_year:
                quarter = random.randint(int(min_year_quarter[4:]), int(max_year_quarter[4:]))
            elif year == start_year:
                quarter = random.randint(int(min_year_quarter[4:]), 4)
            elif year == end_year:
                quarter = random.randint(1, int(max_year_quarter[4:]))
            else:
                quarter = random.randint(1, 4)
            
            return f"{year:04d}{quarter}"
        
        if self._is_valid_date(min_date, max_date):
            if len(min_date) == 6 and len(max_date) == 6:
                return generate_year_month_date(min_date, max_date)
            elif len(min_date) == 5 and len(max_date) == 5:
                return generate_year_quarter_date(min_date, max_date)
            elif len(min_date) == 4 and len(max_date) == 4:
                min_year = int(min_date)
                max_year = int(max_date)
                year = random.randint(min_year, max_year)
                return f"{year:04d}"
        else:
            raise ValueError("Invalid input dates")

    def _random_values(self, source_list, max_num, min_num=0):
        
        max_num = min(max_num, len(source_list))
        min_num = max(0, min_num)
        num_values = random.randint(min_num, max_num)
        
        shuffled_list = source_list.copy()
        random.shuffle(shuffled_list)
        
        return shuffled_list[:num_values]

    def _read_model_mappings(self, csv_file):
        # 读取csv文件，第一列为车型英文名，第二列为车型中文名，文件不含表头
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        data_file_path = os.path.join(parent_dir, 'data', csv_file)

        mappings = {}
        with open(data_file_path, 'r', encoding='utf-8') as file:
            for line in file:
                parts = line.strip().split('\t')
                if len(parts) == 2:
                    mappings[parts[0]] = parts[1]
        return mappings

    def _generate_time_range(self, start_time, end_time):
        def generate_month_str(start_time, end_time):
            year_start = start_time[:4]
            year_end = end_time[:4]
            month_start = int(start_time[4:])
            month_end = int(end_time[4:])
            
            year_current = self._get_relative_date("%Y")
            year_last = str(int(year_current) - 1)

            if year_start == year_end and month_start == month_end:
                if start_time == self._get_relative_date("%Y%m"):
                    return random.choice(["本月","当月",f"{year_start}年{month_start}月"])
                elif start_time == self._get_relative_date("%Y%m", time_interval=-1, interval_unit="m"):
                    return random.choice(["上月","上个月",f"{year_start}年{month_start}月"])
                else:
                    return f"{year_start}年{month_start}月"

            if year_start == year_end:
                if year_start == year_current:
                    year_start = random.choice(["今", start_time[2:4], year_start])
                elif year_start == year_last:
                    year_start = random.choice(["去", start_time[2:4], year_start])
                else:
                    year_start = random.choice([start_time[2:4], year_start])
                if self._random_true(probability=0.7):
                    return random.choice([f"{year_start}年{month_start}到{month_end}月", f"{year_start}年{month_start}月到{month_end}月"])

            if self._random_true():
                year_start = start_time[2:4]
                year_end = end_time[2:4]

            return f"{year_start}年{month_start}月到{year_end}年{month_end}月"

        def generate_year_str(start_time, end_time):
            if start_time == end_time:
                return random.choice([f"{start_time}年", f"{start_time[2:4]}年"])
            
            return random.choice([f"{start_time}到{end_time}年"])

        def generate_quarter_str(start_time, end_time):
            quarter_dict = {"1":"一", "2":"二", "3":"三", "4":"四"}
            if start_time == end_time:
                if start_time == self._get_relative_date("%Y%Q") and self._random_true(0.2):
                    return "本季度"
                elif start_time == self._get_relative_date("%Y%Q", time_interval=-3, interval_unit="m") and self._random_true(0.2):
                    return random.choice(["上季度", "上个季度"])

                return random.choice([start_time[:4], start_time[2:4]]) + "年" + random.choice(["第",""]) + random.choice([start_time[-1:], quarter_dict.get(start_time[-1:])]) + "季度"
            elif start_time[:4] == end_time[:4] and start_time[-1:] == "1" and end_time[-1:] != "1" and self._random_true(0.9):
                if start_time[:4] == self._get_relative_date("%Y"):
                    q_year = "今"
                elif start_time[:4] == self._get_relative_date("%Y", time_interval=-1, interval_unit="y"):
                    q_year = "去"
                return random.choice([q_year, start_time[:4], start_time[2:4]]) + random.choice(["年前", "年第1到第", "年第一季度到第", "年第1季度到第"]) + str(end_time[-1:]) + "季度"

            q_start = random.choice([start_time[:4], start_time[2:4]]) + "年" + random.choice(["第",""]) + random.choice([start_time[-1:], quarter_dict.get(start_time[-1:])]) + "季度"
            q_end = random.choice([end_time[:4], end_time[2:4]]) + "年" + random.choice(["第",""]) + random.choice([end_time[-1:], quarter_dict.get(end_time[-1:])]) + "季度"
            return f"{q_start}到{q_end}"

        if self._is_valid_date(start_time, end_time):
            if len(start_time) == 6:
                return generate_month_str(start_time, end_time)
            elif len(start_time) == 5:
                return generate_quarter_str(start_time, end_time)
            elif len(start_time) == 4:
                return generate_year_str(start_time, end_time)
        else:
            raise ValueError("Invalid input dates")
        
    def _generate_question_type_1(self, start_time, end_time, model_name_cn, rssc_cn, intention, model_mappings):

        date_range = ""
        date_range = self._generate_time_range(start_time, end_time)

        model_part = ""
        if model_name_cn and model_name_cn != [""]:
            model_part = "，".join([model_mappings.get(model_name) for model_name in model_name_cn if model_name in model_mappings.keys()])

        rssc_part = ""
        if rssc_cn and rssc_cn != [""]:
            rssc_values = "和".join(rssc_cn)
            rssc_part = f"{rssc_values}大区"

        model_rssc_part = "在" if rssc_part != "" and model_part != "" else ""

        intention_question = intention.copy()
        if "同比" in intention_question and "环比" in intention_question:
            if self._random_true():
                intention_question.remove("同比")
                intention_question.remove("环比")
                intention_question.append("同环比")
        if "市占" in intention_question:
            if self._random_true():
                intention_question[intention_question.index("市占")] = "市场占有率"
        
        if len(intention_question) == 1:
            intention_part = "的" + intention_question[0]
        else:
            intention_part = "的" + random.choice([",","，"]).join(intention_question[:-1]) + "和" + intention_question[-1]

        question = f"{date_range}{model_part}{model_rssc_part}{rssc_part}{intention_part}"
        return question

    def _generate_question_type_2(self, start_time, end_time, model_name_cn, rssc_cn, intention, model_mappings):
        return self._generate_question_type_1(start_time, end_time, model_name_cn, rssc_cn, intention, model_mappings)

    def _generate_question_type_3(self, start_time, end_time, intention):
        if intention == "返回":
            return random.choice(["返回", "返回主界面", "请返回主界面", "返回大屏", "退出", "关闭对话"])

        if intention == "筛选":
            date_range = ""
            date_range = self._generate_time_range(start_time, end_time)
            from_str = "从" if "到" in date_range else ""
            question = random.choice(["请筛选", "选取", "筛选大屏", "筛选", "过滤"]) + random.choice(["", from_str]) + date_range + random.choice(["", "的数据"])

            return question
        return ""

    def _generate_answer(self, start_time, end_time, model_name_cn, rssc_cn, intention, realtime):
        model = json.dumps(model_name_cn, ensure_ascii=False)
        rssc = json.dumps(rssc_cn, ensure_ascii=False)
        intent = json.dumps(intention, ensure_ascii=False)

        if self._is_valid_date(start_time, end_time):
            if len(start_time) == 4:
                start_time = f"{start_time}01"
                end_time =  f"{end_time}12"
            elif len(start_time) == 5:
                start_dict = {"1":"01", "2":"04", "3":"07", "4":"10"}
                end_dict = {"1":"03", "2":"06", "3":"09", "4":"12"}
                start_time = f"{start_time[:4]}{start_dict.get(start_time[-1:])}"
                end_time = f"{end_time[:4]}{end_dict.get(end_time[-1:])}"
            
            return f'{{ "start_time": "{start_time}", "end_time": "{end_time}", "model_name_cn": {model}, "rssc_cn": {rssc}, "intention": {intent}, "realtime": "{realtime}" }}'
        else:
            raise ValueError("Invalid input dates")

    def _generate_answer_3(self, start_time, end_time, intention):
        intent = json.dumps(intention, ensure_ascii=False)

        if self._is_valid_date(start_time, end_time):
            if len(start_time) == 4:
                start_time = f"{start_time}01"
                end_time =  f"{end_time}12"
            elif len(start_time) == 5:
                start_dict = {"1":"01", "2":"04", "3":"07", "4":"10"}
                end_dict = {"1":"03", "2":"06", "3":"09", "4":"12"}
                start_time = f"{start_time[:4]}{start_dict.get(start_time[-1:])}"
                end_time = f"{end_time[:4]}{end_dict.get(end_time[-1:])}"
            
            return f'{{ "start_time": "{start_time}", "end_time": "{end_time}", "intention": {intent} }}'
        else:
            raise ValueError("Invalid input dates")

    def _get_relative_date(self, output_format="%Y%m", time_interval=0, interval_unit="m"):
        if interval_unit not in {"d", "m", "y"}:
            raise ValueError("Invalid interval_unit. Use 'd', 'm', or 'y'.")

        if interval_unit == "d":
            delta = timedelta(days=time_interval)
        elif interval_unit == "m":
            delta = timedelta(days=time_interval * 30)  # Approximation of months
        else:
            delta = timedelta(days=time_interval * 365)  # Approximation of years

        current_date = datetime.now()
        new_date = current_date + delta

        if "%Q" in output_format:
            quarter = (new_date.month - 1) // 3 + 1
            output_format = output_format.replace("%Q", str(quarter))
        
        return new_date.strftime(output_format)



if __name__ == "__main__":

    generator = TestCaseGenerator()
    print(generator._generate_date())
    print(generator._generate_date(min_date="202201"))
    print(generator._generate_date(min_date="202201", max_date="202308"))
    print(generator._generate_date(min_date="2022", max_date="2023"))
    print(generator._generate_date(min_date="20221", max_date="20232"))
    print(generator._generate_date(min_date="2000", max_date="2022"))

    model_mappings = generator._read_model_mappings("model_name_insurance.tsv")
    print(list(model_mappings.keys()))
    print(generator._random_values(list(model_mappings.keys()), max_num=3))

    print(generator._random_values(["销量", "同比", "环比", "市占"], max_num=4, min_num=1))

    question = generator._generate_question_type_1(start_time='202301',end_time='202304', model_name_cn=['Polo HB', 'T-Cross', 'Tharu'], 
    rssc_cn=[""], intention=['市占'], model_mappings=model_mappings)

    print(question)

    question = generator._generate_question_type_1(start_time='202205',end_time='202304', model_name_cn=['Polo HB', 'T-Cross', 'Tharu'], 
    rssc_cn=["华东","华南"], intention=['市占',"同比","环比"], model_mappings=model_mappings)

    print(question)

    print(generator._is_valid_date("2022"))                  # True，长度为 4
    print(generator._is_valid_date("2021", "0123", "1234"))  # True，长度都为 4
    print(generator._is_valid_date("2021", "01", "12345"))   # False，长度不一致
    print(generator._is_valid_date("1234", "5678"))          # True，长度都为 4
    print(generator._is_valid_date("12", "123", "1234"))     # False，长度不一致
    print(generator._is_valid_date(202212, 202302))          # True，长度都为 6

    print("-"*20)
    print(generator._generate_time_range('202308','202308'))
    print(generator._generate_time_range('202307','202307'))
    print(generator._generate_time_range('202306','202306'))
    print(generator._generate_time_range('202203','202211'))
    print(generator._generate_time_range('202203','202211'))

    print(generator._get_relative_date())  # 当前日期
    print(generator._get_relative_date(output_format="%Y-%m-%d", time_interval=10, interval_unit="d"))  # 10天后的日期
    print(generator._get_relative_date(output_format="%Y-%m-%d", time_interval=2, interval_unit="m"))  # 2个月后的日期
    print(generator._get_relative_date(output_format="%Y-%m-%d", time_interval=1, interval_unit="y"))  # 1年后的日期
    print(generator._get_relative_date(output_format="%Y-%m-%d", time_interval=-1, interval_unit="d"))  # 1天前的日期
    print(generator._get_relative_date(output_format="%Y%m", time_interval=-1, interval_unit="m"))  # 1个月前的日期
    print(generator._get_relative_date(output_format="%Y%Q"))                                     # 当前季度
    print(generator._get_relative_date(output_format="%Y%Q", time_interval=-3, interval_unit="m"))  # 上个季度

    print(generator.generate_type_4(3, file_name="index_query.txt"))
    print(generator.generate_type_4(3, file_name="index_query.txt", answer_type=2))
    print(generator.generate_type_4(3, file_name="index_query.txt", answer_type=2, answer="未知意图"))

    print(generator._generate_answer('202202', '202303', ['车系'], ['东北'], ['销售'], realtime="False"))
    print(generator._generate_answer('2022', '2022', ['车系'], ['东北'], ['销售'], realtime="False"))
    print(generator._generate_answer('20221', '20233', ['车系'], ['东北'], ['销售'], realtime="False"))
    print(generator._generate_answer('20224', '20231', ['车系'], ['东北'], ['销售'], realtime="False"))

    test_cases = generator.generate_type_1(num_cases=3, min_month="202201", max_month="202308")
    print("-"*20)
    print(test_cases)

    test_cases = generator.generate_type_1(num_cases=3, min_month="202201", max_month="202308", model_max=0, rssc_max=1)
    print("-"*20)
    print(test_cases)

    test_cases = generator.generate_type_2(num_cases=3, min_month="202201", max_month="202308")
    print("-"*20)
    print(test_cases)

    test_cases = generator.generate_type_2(num_cases=3, min_month="202201", max_month="202308", model_max=0, rssc_max=1, intention_max=4)
    print("-"*20)
    print(test_cases)

    test_cases = generator.generate_type_3(num_cases=3, min_month="202201", max_month="202308")
    print("-"*20)
    print(test_cases)

    test_cases = generator.generate_type_3(num_cases=3, min_month="202201", max_month="202308")
    print("-"*20)
    print(test_cases)

    test_cases = generator.generate_type_1(num_cases=3, min_month="2022", max_month="2023")
    print("-"*20)
    print(test_cases)

    test_cases = generator.generate_type_2(num_cases=3, min_month="20221", max_month="20233", model_max=0, rssc_max=1, intention_max=4)
    print("-"*20)
    print(test_cases)

    test_cases = generator.generate_type_3(num_cases=3, min_month="20231", max_month="20233")
    print("-"*20)
    print(test_cases)