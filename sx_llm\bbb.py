import httpx
import asyncio
import json
from typing import Dict, Union, Any, Optional

class AsyncApiClient:
    """通用异步API客户端，支持GET和POST请求"""
    
    def __init__(self, base_url: str, auth_token: str = None, timeout: float = 30.0):
        self.base_url = base_url
        self.auth_token = auth_token
        self.timeout = timeout
        self.default_headers = {
            'User-Agent': 'python-requests/2.32.3',
            'Accept-Encoding': 'gzip, deflate',
            'Accept': '*/*',
            'Connection': 'keep-alive'
        }
        if auth_token:
            self.default_headers['Authorization'] = auth_token
    
    async def request(self, 
                     method: str, 
                     endpoint: str, 
                     params: Dict = None, 
                     data: Dict = None, 
                     json_data: Dict = None,
                     headers: Dict = None,
                     result_key: str = None,
                     debug: bool = True) -> Optional[Dict[str, Any]]:
        """
        发送请求并返回解析后的JSON结果
        
        参数:
            method: 请求方法，'GET' 或 'POST'
            endpoint: API端点（相对路径）
            params: URL查询参数（GET请求）
            data: 表单数据（POST请求）
            json_data: JSON数据（POST请求）
            headers: 自定义请求头
            debug: 是否打印调试信息
        
        返回:
            解析后的JSON数据或None（如果请求失败）
        """
        # 构建完整URL
        url = f"{self.base_url}/{endpoint}" if not endpoint.startswith('http') else endpoint
        url = url.rstrip('/')
        
        # 合并请求头
        request_headers = {**self.default_headers}
        if headers:
            request_headers.update(headers)
        
        if debug:
            print(f"请求方法: {method}")
            print(f"请求URL: {url}")
            if params:
                print(f"查询参数: {params}")
            if data:
                print(f"表单数据: {data}")
            if json_data:
                print(f"JSON数据: {json_data}")
            print(f"请求头: {request_headers}")
        
        try:
            # 创建异步客户端
            async with httpx.AsyncClient(
                timeout=self.timeout,
                verify=False,  # 禁用SSL验证，生产环境可能需要启用
                follow_redirects=True  # 允许重定向
            ) as client:
                if debug:
                    print("发送请求...")
                
                # 根据方法发送不同类型的请求
                if method.upper() == 'GET':
                    response = await client.get(url, params=params, headers=request_headers)
                elif method.upper() == 'POST':
                    # 选择使用表单数据还是JSON数据
                    if json_data is not None:
                        response = await client.post(url, params=params, json=json_data, headers=request_headers)
                    else:
                        response = await client.post(url, params=params, data=data, headers=request_headers)
                else:
                    raise ValueError(f"不支持的请求方法: {method}")
                
                if debug:
                    print(f"状态码: {response.status_code}")
                    print(f"响应头: {response.headers}")
                
                # 处理响应
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    if debug:
                        print(f"内容类型: {content_type}")
                        print(f"响应内容前100个字符: {response.text[:100] if response.text else 'N/A'}")
                    
                    # 尝试解析JSON
                    try:
                        json_response = response.json()
                        if debug:
                            print(f"成功解析JSON数据")
                        return json_response[result_key]
                    except json.JSONDecodeError as e:
                        if debug:
                            print(f"JSON解析错误: {e}")
                            if '<html' in response.text.lower():
                                print("服务器返回了HTML而不是JSON")
                                if 'login' in response.text.lower():
                                    print("发现登录页面，可能需要重新认证")
                        
                        # 尝试直接解析文本内容为JSON
                        try:
                            if response.text and response.text.strip():
                                text = response.text.strip()
                                if text.startswith('{') and text.endswith('}'):
                                    return json.loads(text)
                        except:
                            pass
                        
                        return None
                else:
                    if debug:
                        print(f"请求失败，状态码: {response.status_code}")
                    return None
        
        except httpx.RequestError as e:
            if debug:
                print(f"请求错误: {e}")
            return None
        except Exception as e:
            if debug:
                print(f"未知错误: {type(e).__name__}: {e}")
            return None

# 使用示例
async def demo():
    # 创建客户端实例
    client = AsyncApiClient(
        base_url="http://************:8080/report/mobile/wechat",
        auth_token="NvGW4h-YTGQAFVoQKylHQ5PMIvjCvnk0"
    )
    
    # GET请求示例
    get_result = await client.request(
        method="GET",
        endpoint="sv/afterSaleJyController/queryJyIndex",
        params={"yearMonth": "202301", "queryType": "M"}
    )
    print(f"GET请求结果: {get_result}")
    
    # POST请求示例
    post_result = await client.request(
        method="POST",
        endpoint="sv/afterSaleJyController/somePostEndpoint",  # 替换为实际的POST端点
        params={"param1": "value1"},  # URL参数
        json_data={"key1": "value1", "key2": "value2"}  # POST的JSON数据
    )
    print(f"POST请求结果: {post_result}")

# if __name__ == "__main__":
#     asyncio.run(demo()) 


import asyncio
# from universal_async_client import AsyncApiClient
import time

async def parallel_requests_demo():
    # 创建客户端实例
    client = AsyncApiClient(
        base_url="http://************:8080/report/mobile/wechat",
        auth_token="NvGW4h-YTGQAFVoQKylHQ5PMIvjCvnk0"
    )
    
    print("开始执行并行请求...")
    start_time = time.time()
    
    # 定义多个请求任务
    async def task1():
        # 第一个月份的数据
        result = await client.request(
            method="GET",
            endpoint="sv/afterSaleJyController/queryJyIndex",
            params={"yearMonth": "202301", "queryType": "M"},
            result_key="result",
            debug=False  # 设置为True会显示详细日志
        )
        return {"name": "一月数据", "result": result}
    
    async def task2():
        # 第二个月份的数据
        result = await client.request(
            method="GET",
            endpoint="sv/afterSaleJyController/queryJyIndex",
            params={"yearMonth": "202302", "queryType": "M"},
            result_key="result",
            debug=False
        )
        return {"name": "二月数据", "result": result}
    
    async def task3():
        # 第三个月份的数据
        result = await client.request(
            method="GET",
            endpoint="sv/afterSaleJyController/queryJyIndex",
            params={"yearMonth": "202303", "queryType": "M"},
            result_key="result",
            debug=False
        )
        return {"name": "三月数据", "result": result}
    
    async def task4():
        # POST请求示例
        result = await client.request(
            method="POST",
            endpoint="sv/afterSaleJyController/somePostEndpoint",  # 替换为实际的POST端点
            json_data={"key1": "value1", "key2": "value2"},
            result_key="result",
            debug=False
        )
        return {"name": "POST请求", "result": result}
    
    # 使用gather并行执行所有任务
    results = await asyncio.gather(
        task1(),
        task2(),
        task3(),
        task4(),
        return_exceptions=True  # 允许单个任务失败而不影响其他任务
    )
    
    # 处理结果
    for i, result in enumerate(results, 1):
        if isinstance(result, Exception):
            print(f"任务{i}失败: {result}")
        else:
            print(f"任务{i} - {result['name']}完成")
            print(f"结果: {result['result']}")
            print("-" * 30)
    
    end_time = time.time()
    print(f"所有请求完成，总耗时: {end_time - start_time:.2f}秒")

    # 另一种批量请求方式 - 使用列表推导式动态生成多个请求
    print("\n批量请求示例...")
    
    # 准备多个月份的参数
    months = ["202301", "202302", "202303", "202304", "202305"]
    
    # 使用列表推导创建请求任务
    tasks = [
        client.request(
            method="GET",
            endpoint="sv/afterSaleJyController/queryJyIndex",
            params={"yearMonth": month, "queryType": "M"},
            result_key="result",
            debug=False
        )
        for month in months
    ]
    
    # 并行执行
    batch_start = time.time()
    batch_results = await asyncio.gather(*tasks, return_exceptions=True)
    batch_end = time.time()
    
    # 处理结果
    for i, (month, result) in enumerate(zip(months, batch_results), 1):
        if isinstance(result, Exception):
            print(f"月份 {month} 请求失败: {result}")
        else:
            print(f"月份 {month} 请求成功: {result}")
    
    print(f"批量请求完成，总耗时: {batch_end - batch_start:.2f}秒")
    
    # 演示如何处理混合请求类型（GET和POST）
    print("\n混合类型请求示例...")
    
    # 创建不同类型的请求
    mixed_tasks = [
        # GET请求
        client.request("GET", "sv/afterSaleJyController/queryJyIndex", 
                      params={"yearMonth": "202301", "queryType": "M"},
                      result_key="result"),
        # POST请求 - 表单数据
        client.request("POST", "https://sandbox-openapi.svw-volkswagen.com/robot/v3/open/adapter/vw/listCompetitionAnalyse",
                      data={"time": "202401-202401", "start_time": "202401", "end_time": "202401", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["销量"], "display": "趋势", "today": "False", "data_dim": "all", "not_total": "False", "template_id": ""},
                      result_key="data"),
        # POST请求 - JSON数据
        client.request("POST", "https://sandbox-openapi.svw-volkswagen.com/robot/v3/open/adapter/vw/listCompetitionAnalyse", 
                      json_data={"time": "202401-202401", "start_time": "202401", "end_time": "202401", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["销量"], "display": "趋势", "today": "False", "data_dim": "all", "not_total": "False", "template_id": ""},result_key="data")
    ]
    
    # 执行混合请求
    mixed_results = await asyncio.gather(*mixed_tasks, return_exceptions=True)
    
    # 输出结果
    request_types = ["GET查询", "POST表单", "POST JSON"]
    for req_type, result in zip(request_types, mixed_results):
        print(f"{req_type} 结果: {result}")

if __name__ == "__main__":
    asyncio.run(parallel_requests_demo())