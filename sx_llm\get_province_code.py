import json
import requests
from datetime import datetime
from mcp.server.fastmcp import FastMCP

# 创建 MCP 服务器实例
mcp = FastMCP("GetAftersalesData")

@mcp.tool()
def get_current_time():
    """
    获取当前时间，用于将用户给出的时间转换为标准时间格式
    :return: 当前时间
    """
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    return {"current_time": current_time}

@mcp.tool()
def get_province_code():
    """
    获取全部省份代码
    :return: 省份名称和省份代码
    """
    url = 'http://dac.csvw.com/report//mobile/wechat/sv/FjjxcManageController/getProvinceList'
    token = 'KA7rScUKRvcAFVD33LhZzbbrLEhKfpVt'
    headers = {
        'Authorization': token,
        'Content-Type': 'application/json'
    }
    response = requests.get(url, headers=headers)
    return json.loads(response.text)['result']

@mcp.tool()
def get_business_data(regionCode: str = None, agencyCode: str = None, businessManagerNum: str = None, 
                      dealerCode: str = None, provinceCode: str = None, cityCode: str = None, dimension: str = None, 
                      dateValue: str = None, endDateValue: str = None):
    """
    获取售后经营数据
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 维度:MTD/QTD/YTD/OTHER
    :param dateValue: 示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :return: 省份名称和省份代码
    """
    url = 'http://dac.csvw.com/report/mobile/wechat/sv/seviceComprehensiveReportController/performanceData'
    token = 'KA7rScUKRvcAFVD33LhZzbbrLEhKfpVt'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue
    }
    response = requests.get(url, params=params, headers=headers)
    return json.loads(response.text)['result']

# 运行 MCP 服务器
if __name__ == "__main__":
    mcp.run()
    # response = get_province_code()
    # print(response)
    # response = get_current_time()
    # print(response)
    