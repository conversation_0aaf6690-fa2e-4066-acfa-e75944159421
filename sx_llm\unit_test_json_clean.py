import csv
from tqdm import tqdm
import os
import json
import re
import sys
import time
import requests
sys.path.append('D:/code/gpt/warroom_llm/sx_llm/')
from http import HTTPStatus
import dashscope

parent_dir = os.path.dirname(os.path.abspath(__file__))
file = os.path.join(parent_dir, 'tests', 'data', 'auto_test_json_extract_data.csv')


def get_access_token():
    """
    使用 API Key，Secret Key 获取access_token，替换下列示例中的应用API Key、应用Secret Key
    """
        
    # url = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=4UeHb4PaemCIaEQGFUthzgGE&client_secret=ppDaYf0WYZjAUlBjoA3L8ebEs8Khqpqw"
    url = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=fW1PBg9AVBXSUdFigdNdsdtt&client_secret=UCy0s691b118gKGdZoGL9cwEDEPyK0nv"

    payload = json.dumps("")
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    
    response = requests.request("POST", url, headers=headers, data=payload)
    return response.json().get("access_token")


def wenxin_request(prompt):
        
    # url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro?access_token=" + get_access_token()
    # url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-4.0-8k-0329?access_token=" + get_access_token()
    url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-4.0-8k-0104?access_token=" + get_access_token()
    
    payload = json.dumps({
        "messages": [{"role": "user", "content": prompt}],
        "stream": False,
        "temperature": 0.01,
    })
    headers = {
        'Content-Type': 'application/json'
    }
    t1 = time.time()
    response = requests.request("POST", url, headers=headers, data=payload, stream=False)

    response_json = json.loads(response.text)['result']
    return response_json



def call_with_messages(content):
    messages = [{'role': 'user', 'content': content}]
    response = dashscope.Generation.call(
        # model='llama3-70b-instruct',
        model='qwen1.5-110b-chat',
        temperature = 0,
        messages=messages,
        result_format='text',  
        api_key = 'sk-tDdROccVi2'
    )
    if response.status_code == HTTPStatus.OK:
        return response['output']['text']
    else:
        print('Request id: %s, Status code: %s, error code: %s, error message: %s' % (
            response.request_id, response.status_code,
            response.code, response.message
        ))

def extract_json_from_markdown(input_str):
    # 替换中文引号或其他非ASCII引号为英文双引号
    input_str = input_str.replace("“", "\"").replace("”", "\"")

    # 替换字符 \xa0 为普通空格
    input_str = input_str.replace("\xa0", " ")

    # 替换换行符为空
    input_str = input_str.replace("\n", "")
    # 替换布尔值为字符串
    if "\"False\"" not in input_str and "\"True\"" not in input_str:
        input_str = input_str.replace("False", "\"False\"")
        input_str = input_str.replace("True", "\"True\"")

    # 正则表达式匹配Markdown中的JSON部分
    pattern = r'```json(.*?)```'
    match = re.search(pattern, input_str, re.IGNORECASE | re.DOTALL)
    
    if match is not None:
        # 提取JSON字符串
        json_str = match.group(1)

        try:
            json_obj = json.loads(json_str)
            return json_obj
        except json.JSONDecodeError:
            pass
    
    input_str = input_str.replace("\"{", "{")
    input_str = input_str.replace("}\"", "}")
    input_str = input_str.replace("\\", "")


    # 尝试直接解析整个输入字符串为JSON
    try:
        json_obj = json.loads(input_str)
        if isinstance(json_obj, dict):
            return json_obj
        if isinstance(json_obj, str):
            # 如果是字符串尝试再解析一次
            json_obj = json.loads(json_obj)
            return json_obj
    except json.JSONDecodeError:
        pass

    # 正则表达式匹配大括号里的内容并尝试解析为JSON
    pattern = r'{.*?}'
    matches = re.findall(pattern, input_str)
    for match in matches:
        try:
            json_obj = json.loads(match)
            return json_obj
        except json.JSONDecodeError:
            pass

    return None

# Llama3prompt
# prompt = """[Requirement]
# 1. Return using JSON format, output does not contain any other content or analysis process, do not return ``` JSON ```, only JSON text. The content of JSON is generated as follows:
# 2.1. time:
# 2.1.1. Extract explicit times that appear in user input and save them in the form of a list.
# 2.1.2. The relative time is converted based on the benchmark date of {today}, using the complete natural year, the complete natural quarter, and the complete natural month.
# 2.1.3. Identify the start time and end time of the time range, strictly using the format of yyyymmdd-yyyymmdd.
# 2.1.4. If the input time information cannot be parsed or does not exist, return an empty list [].
# 2.2. date_type: Extract the type of query time input by the user, return "day" by day, "week" by week, "month" by month, and "year" by year; If the information does not exist, use "" as the value.
# 2.3. location: Extract the regions, provinces, and cities that appear in user input without any analysis and inference, and supplement keywords "region", "province", and "city". Save results in a list format, such as ["Nationwide"], ["Huadong region"], ["Beijing city", "Shanghai city"]; If there is no relevant information, return ["Nationwide"]; By default, "by region" or "by SVR" return to [National].
# 2.4. model: Extract the vehicle brand, vehicle category, or vehicle model name from the user input, as well as multi value fields, and save them with a list structure in square brackets; If the information does not exist, please use [] as the value.
# 2.5. display: Extract possible data display forms from user input, only return keywords "trend", "distribution", "ranking", "comparison", "year-on-year (yoy)", "month on month (mom)"; If the information does not exist, return "".
# 2.6. data_dim:
# 2.6.1. "by SVR" equivalent to "by region".
# 2.6.2. If the user input contains keywords "by region", "by province", "by city", or explicitly mentions drilling down on "region", "province", or "city", return "location".
# 2.6.3. If the user input contains keywords "by brand", "by model", or explicitly mentions drilling down on "brand" or "model", return "model".
# 2.6.4. If the user input does not explicit mentions about drill down on region or model, even if user input contains specified region or model information, do not analyse or inference, only return "all".
# 2.7. today: If the user input only involves today's data or real-time data, return "True"; otherwise, return "False".
# 2.8.not_total: If the user inputs monthly, weekly, or daily data, return "True"; otherwise, return "False".
# 2.9. template: If the user explicitly mentions using a template for querying, extract the name of the template, save result as a string; If the information does not exist, return "".

# [User input]:
# {question}
# [Output]: """

# 千帆110Bprompt
# prompt = """[要求]
# 1.以JSON格式返回，输出不包含其他内容或分析过程，不要返回```json```，只有JSON文本
# 2.JSON的内容按如下方式生成：
# 2.1.time：
# 2.1.1.识别用户输入中出现的一个或多个绝对或相对时间描述，忽略时间间隔描述。
# 2.1.2.相对时间以{today}为基准进行转化，相对时间使用完整自然年、自然季度、自然月。
# 2.1.3.每个时间时间段严格使用yyyymmdd-yyyymmdd的格式进行返回，不进行区间的拆分。
# 2.1.4.结果以列表(list)形式返回，如果输入的时间信息无法解析或不存在，则返回一个空列表 []。
# 2.2.date_type：提取用户输入查询时间的类型，按天返回"day"，按周返回"week"，按月返回"month"，按年返回"year"; 如果信息不存在, 请使用 "" 作为值。
# 2.3.location：提取用户输入中出现的大区、省份、城市，无需进行分析和推理，并补充"大区"、"省"、"市"关键字，多值字段，用中括号 list 结构保存，如["全国"]，["华东大区"]，["北京市","上海市"]；无相关信息，则使用 ["全国"] 作为值；"各大区" 默认返回 ["全国"]。
# 2.4.model：提取用户输入中的车辆品牌或车辆类别或车型名称, 多值字段, 用中括号 list 结构保存；如果信息不存在, 请使用 [] 作为值。
# 2.5.display：提取用户输入中隐含的数据展示形式，单值字段，仅包括"趋势","分布","排名","对比","同环比（yoy and mom）","同比（mom）","环比（yoy）"，无需添加其他不在范围的词汇; 如果信息不存在, 请使用 "" 作为值。
# 2.6.data_dim：
# 2.6.1."by SVR"表示"按大区维度下钻"的含义。
# 2.6.2.如果用户输入明确说明按地区维度下钻或出现"各大区"、"各省份"、"各城市"，返回"location"；如果用户输入明确说明按车型维度下钻或出现"各车型"，返回"model"。
# 2.6.3.如果用户输入没有明确说明按地区或车型维度下钻，即使含有地区或车型相关信息，也不返回"location"或"model"，只返回"all"。
# 2.7.today：如果用户输入仅涉及今日数据或实时数据，返回"True"，否则，返回"False"。
# 2.8.not_total：如果用户输入需要每月、每周、每日的数据，返回"True"，否则，返回"False"。
# 2.9.template：如果用户明确提到使用模板查询进行查询，提取模板的名称，单值字段，字符串; 如果信息不存在, 请使用 "" 作为值。

# [用户输入]：
# {question}
# [输出]："""

# 生产prompt
prompt = """[功能]
根据用户的输入，按照要求提取关键参数，并输出。

[要求]
1.以JSON格式输出，内容只有JSON文本，不包含分析过程，不要返回```json```。
2.JSON的内容按如下方式生成：
2.1.time：按照步骤提取参数。
2.1.1.提取用户输入出现的一个或多个时间描述，以列表(list)形式保存；如果时间信息无法解析或不存在，则返回一个空列表 []。
2.1.2.相对时间以基准日期{today}转化绝对时间，相对时间使用完整自然年、自然季度、自然月；绝对时间无需转化。
2.1.3.全部时间最终转为yyyymmdd-yyyymmdd的格式。
2.2.date_type：提取用户输入查询时间的类型，到天返回"day"，到周返回"week"，到月返回"month"，到季度返回"quarter"，到年返回"year"; 如果信息不存在, 请使用 "" 作为值。
2.3.location：提取用户输入出现的特定大区、省份、城市，无需进行分析和推理，提取结果补充"大区"、"省"、"市"关键字，多值字段，用中括号 list 结构保存，如["全国"]，["华东大区"]，["北京市","上海市"]；无相关信息，则使用 ["全国"] 作为值；"各大区" 默认返回 ["全国"]。
2.4.model：提取用户输入出现的特定车辆品牌、车辆类别、车型名称, 多值字段, 用中括号 list 结构保存；如果信息不存在, 请使用 [] 作为值。
2.5.display：提取用户输入可能的数据展示形式，单值字段，仅包括"趋势","分布","排名","对比","同环比（yoy and mom）","同比（mom）","环比（yoy）"，无需添加其他不在范围的词汇; 如果信息不存在, 请使用 "" 作为值。
2.6.data_dim：
If [用户输入]包含"各大区"或"各省份"或"各城市"或表明要按照地区维度下钻的含义或"by SVR"在[用户输入]中 Then 返回"location"
ELif [用户输入]包含"各车型"或表明要按照车型维度下钻的含义, Then 返回"model"
Else 返回"all" End
2.7.today：如果用户输入仅涉及今日数据或实时数据，返回"True"，否则，返回"False"。
2.8.not_total：如果用户输入需要每月、每周、每日的数据，返回"True"，否则，返回"False"。
2.9.template：如果用户明确提到使用模板查询进行查询，提取模板的名称，单值字段，字符串; 如果信息不存在, 请使用 "" 作为值。

[用户输入]：
{question}

[输出]："""











if __name__ == '__main__':
    results = []
    # 打开CSV文件
    with open(file, 'r', encoding='utf-8') as f:
        # 创建CSV读取器
        csv_reader = csv.reader(f, delimiter='\t')
        # 逐行读取CSV内容
        for row in tqdm(csv_reader):
            print(row)
            try:
                if row == ['', ''] or row == []:
                    continue
                question,answer = row

                # today = 'April 26, 2024'
                today = '2024年5月9日'
                content = prompt.format(question=question, today=today)
                print(content)
                # llama3调用
                # response_json = call_with_messages(content)
                # 文心调用
                response_json = wenxin_request(content)
                print(response_json)
                print('***')
                format_json = extract_json_from_markdown(response_json)

                data1 = format_json
                data2 = json.loads(answer)
                inconsistent_keys = []
                for k in data1.keys():
                    v1 = data1[k]
                    v2 = data2[k]
                    if k in ["model","location"]:
                        v1.sort()
                        v2.sort()
                    
                    if v1 != v2:
                        inconsistent_keys.append(k)

                if inconsistent_keys == []:
                    is_equal = True
                else:
                    is_equal = False
                results.append([question,answer,format_json,is_equal,inconsistent_keys])
            except Exception as e:
                print(row)
            
            # time.sleep(60)
    print(results) 
    answer_type = 'json'
    output_file = os.path.join(parent_dir, 'tests', 'data', 'unit_test_'+ answer_type +'_result_'+'wenxin0104_修改原prompt'+'-20240509'+'V2'+'.xlsx')
    import pandas as pd 
    result_df = pd.DataFrame(results)
    result_df.columns = ["问题","标准答案","生成答案","是否相等","不相等的字段"]
    print(result_df)
    result_df.to_excel(output_file,index=None)



