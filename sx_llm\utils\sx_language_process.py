







def clean_language(language: str) -> str:
    """language 清洗，只有中文和英文，默认是英文"""

    if language != "中文":
        language = "英文"
    return language


def location_language_transform(location: str, language: str, location_mapping: dict[str, str]) -> str:
    """根据语言，对location进行转换"""
    
    if (language == "英文" and location != "Nationwide") or (language == "中文" and location == "Nationwide"):
        location = location_mapping.get(location, location)
    return location