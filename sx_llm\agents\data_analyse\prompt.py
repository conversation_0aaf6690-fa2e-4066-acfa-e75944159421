prompt_query_analysis_template = """# 角色
你是一个数据分析师，请根据以下要求回答用户的问题。

# 要求
1.请根据用户[问题]分析需要查询查询哪些维度的信息。
2.根据分析维度生成查询问题。
3.以JSON格式返回，最终回答仅返回JSON内容，不要输出```json```。

# 样例
{{
  "分析维度": [
    "总销量",
    "各车型销量对比",
    "区域销量分布",
    "同比增长率",
    "销售渠道占比",
    "市场份额变化"
  ],
  "查询问题": [
    "去年上汽大众的总销量是多少？",
    "去年上汽大众各车型的销量数据及占比如何？",
    "去年上汽大众在不同省份/城市的销量分布情况如何？",
    "去年上汽大众销量相较于前年的同比增长率是多少？",
    "去年上汽大众的4S店、线上平台等销售渠道贡献比例如何？",
    "去年上汽大众在国内乘用车市场的份额变化趋势如何？",
    "去年上汽大众销量最佳的季度/月份是哪个时段？",
    "去年上汽大众新能源车型与传统燃油车型销量对比如何？",
    "去年上汽大众销量排名前三的热门车型是哪些？"
  ]
}}

# 问题
{question}

#回答
"""

kpi_all = ["上险（Insurance）", "市占（Market Share or M.S.）", "转化（Conversion）", "漏斗（Funnel）", "实时数据", "销量（Volume）", "销售表现",
           "日均客源数", "日均潜客数", "日均展厅客流数", "日均到店数", "日均试乘试驾数", "日均订单数", "日均零售发票数",
           "客源数（Leads）", "潜客数（NPC）", "展厅客流数（Showroom Traffic）", "到店数", "试乘试驾数（Test Drive）", "订单数（NCO）", 
           "交车数", "发票数（Invoice）", "零售发票数（Retail Invoice）",
           "批售数（Whole sale）", "含大客户发票数（Invoice KA Included）", "库存",
           "日均交车数", "日均销量", "日均发票数", "日均含大客户发票数", "日均批售数",
           "库存数（Stock）", "经销商库存数（Dealer Stock）", "总部库存数（HQ Stock）", "库存当量（Stock Factor）", "总部库存当量（HQ Stock Factor）", "经销商存数当量（Dealer Stock Factor）",
           "线索转化率（LTO）", "线索到店率（Leads to ST）", "试乘试驾率（ST to TD）", "到店转化率（ST to NCO）", "订单成交率（NCO to Invoice）", 
           "客源目标（Leads Target）","客源完成","客源任务完成（Leads Achieved）","客源进度",
           "潜客目标（NPC Target）","潜客完成","潜客任务完成（NPC Achieved）","潜客进度",
           "展厅客流目标（Showroom Traffic Target）","展厅客流完成","展厅客流任务完成（Showroom Traffic Achieved）","展厅客流进度",
           "到店目标","到店完成","到店任务完成","到店进度",
           "试乘试驾目标（Test Drive Target）","试乘试驾完成","试乘试驾任务完成（Test Drive Achieved）","试乘试驾进度",
           "订单目标（NCO Target）","订单完成","订单任务完成（NCO Achieved）","订单进度",
           "发票目标（Invoice Target）","发票完成","发票任务完成（Invoice Achieved）","发票进度",
           "交车目标","交车完成","交车任务完成","交车进度",
           "销量目标","销量完成","销量任务完成","销量进度",
           "含大客户发票目标（Invoice KA Included Target）","含大客户发票完成","含大客户发票任务完成（Invoice KA Included Achieved）","含大客户发票进度",
           "零售发票目标（Retail Invoice Target）","零售发票完成","零售发票任务完成（Retail Invoice Achieved）","零售发票进度",
           "批售目标（Whole sale Target）","批售完成","批售任务完成（Whole sale Achieved）","批售进度"]

kpi_all = "\"" + "\", \"".join(kpi_all) + "\""



prompt_internal_query_json_template = """# 角色
你是一个企业内部数据分析助手，请根据以下要求回答用户的问题。

# 要求
1.请根据用户[问题]生成能够查询更丰富数据的问题列表，可以对车型、地区进行下钻，可以对时间范围进行扩展。
2.请根据生成的查询问题列表，提取每个问题中的参数，单个问题以JSON格式返回，不要输出```json```。
3.生成JSON的要求如下：
3.1.time：
3.1.1.提取用户输入中出现的明确时间，忽略"每周"、"每月"、"每日"，并以列表(list)形式保存。
3.1.2.相对时间以基准日期{today}（{weekday}）进行转化，相对时间使用完整自然年、自然季度、自然月、自然周（周从星期一开始）。
3.1.3.识别时间范围的开始和结束时间，严格使用yyyymmdd-yyyymmdd的格式。
3.1.4.如果输入的时间信息无法解析或不存在，则返回一个空列表 []。
3.2.date_type：提取用户输入查询时间的类型，到天返回"day"，到周返回"week"，到月返回"month"，到年返回"year"; 如果信息不存在, 请使用 "" 作为值。
3.3.location：提取用户输入中出现的大区、省份、城市，无需进行分析和推理，并补充"大区"、"省"、"市"关键字，多值字段，用中括号 list 结构保存，如["全国"]，["华东大区"]，["北京市","上海市"]；无相关信息，则使用 ["全国"] 作为值；"各大区" 默认返回 ["全国"]。
3.4.model：提取用户输入中的车辆品牌或车辆类别或车型名称, 多值字段, 用中括号 list 结构保存；如果信息不存在, 请使用 [] 作为值
3.5.display：提取用户输入中隐含的数据展示形式，单值字段，仅包括"趋势"、"分布"、"排名"、"对比"、"同环比（yoy and mom）"、"同比（mom）"、"环比（yoy）"、"总计"，无需添加其他不在范围的词汇; 如果信息不存在, 请使用 "" 作为值。
3.6.data_dim：
3.6.1."by SVR"表示"按大区维度下钻"的含义。
3.6.3.如果用户输入明确说明查询更下一级地区维度或出现"各大区（by region）"、"各省份（by province）"、"各城市（by city）"、"哪个大区（which region）"、"哪个省份（which province）"、"哪个城市（which city）"，返回"location"；如果用户输入明确说明查询更下一级车型维度或出现"各车型（by model）"、"哪个车型（which model）"的含义，返回"model"。
3.6.3.如果用户输入没有明确说明按地区或车型维度下钻，即使含有地区或车型相关信息，也不返回"location"或"model"，只返回"all"。
3.7.today：如果用户输入明确提到今日数据或实时数据查询，返回"True"，否则，返回"False"。
3.8.not_total：如果用户输入需要每月、每周、每日的数据，返回"True"，否则，返回"False"。
3.9.template：如果用户明确提到使用模板查询进行查询，提取模板的名称，单值字段，字符串; 如果信息不存在, 请使用 "" 作为值。
3.10.kpi：提取用户输入中包含的[指标]，多值字段，用中括号 list 结构保存，如["销量","转化"]；如果信息不存在, 请使用 [] 作为值。
4.最终输出以"question_list"和"json_list"为key的JSON格式，其中"question_list"为用户输入的问题列表，"json_list"为每个问题对应的JSON格式。


# 指标
{kpi_all}

# 问题
{question}

#回答
"""



if __name__ == '__main__':
    question = "查询下今天的销量数据"
    # print(prompt_internal_query_json_template.format(question=question))