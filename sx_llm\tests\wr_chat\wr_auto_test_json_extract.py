import csv
from tqdm import tqdm
import os
import traceback
import sys
sys.path.append('D:/codes/gpt/warroom_llm/sx_llm/')
from api import *


# llm_intent = llm_dict["wenxin"]
# llm_intent = llm_dict["qwen"]
# llm_json = llm_dict["wenxinspeed"]
# llm_json = llm_dict["qwen"]
llm_json = llm_dict["wenxin"]



import datetime
import calendar
from dateutil.relativedelta import relativedelta
from langchain.chains import LL<PERSON><PERSON><PERSON>

def replace_date_params(text):
    now = datetime.datetime.now()
    this_year = now.year
    this_month = now.month

    # 定义日期替换函数
    def replace_with_format(date_obj, format_str):
        return date_obj.strftime(format_str)


    # 本月、本月第一天、本月最后一天
    first_day_of_current_month = datetime.datetime(this_year, this_month, 1)
    last_day_of_current_month = datetime.datetime(this_year, this_month, calendar.monthrange(this_year, this_month)[1])


    # 上月、上月第一天、上月最后一天
    if this_month == 1:
        last_month_year = this_year - 1
        last_month = 12
    else:
        last_month_year = this_year
        last_month = this_month - 1
    first_day_of_last_month = datetime.datetime(last_month_year, last_month, 1)
    last_day_of_last_month = datetime.datetime(last_month_year, last_month, calendar.monthrange(last_month_year, last_month)[1])


    # 今年、今年第一天、今年最后一天、今年第一个月、今年最后一个月
    first_day_of_this_year = datetime.datetime(this_year, 1, 1)
    last_day_of_this_year = datetime.datetime(this_year, 12, 31)

    
    # 去年、去年第一天、去年最后一天、去年第一个月、去年最后一个月
    first_day_of_last_year = datetime.datetime(this_year - 1, 1, 1)
    last_day_of_last_year = datetime.datetime(this_year - 1, 12, 31)
    last_year_last_month_start = datetime.date(this_year - 1, 12, 1)


    # 确定当前日期所在的周的第一天和最后一天（假设周一为一周的第一天）
    def get_week_start_end(date):
        # 周一是一周的第一天，所以我们需要找到离给定日期最近的周一
        # weekday()函数返回的是0代表周一，6代表周日
        week_start = date - datetime.timedelta(days=date.weekday())
        week_end = week_start + datetime.timedelta(days=6)
        return week_start, week_end

    this_week_start, this_week_end = get_week_start_end(now)
    last_week_start = this_week_start - datetime.timedelta(days=7)
    last_week_end = last_week_start + datetime.timedelta(days=6)
    this_year_q1_start = datetime.date(this_year, 1, 1)
    this_year_q1_end = datetime.date(this_year, 3, 31)

    # 上个季度日期计算
    if now.month <= 3:  # 如果当前是第一季度，则上个季度是去年的第四季度
        last_quarter_start = datetime.date(this_year - 1, 10, 1)
        last_quarter_end = datetime.date(this_year - 1, 12, 31)
    elif now.month <= 6:  # 如果当前是第二季度，则上个季度是今年的第一季度
        last_quarter_start = this_year_q1_start
        last_quarter_end = this_year_q1_end
    elif now.month <= 9:  # 如果当前是第三季度，则上个季度是今年的第二季度
        last_quarter_start = datetime.date(this_year, 4, 1)
        last_quarter_end = datetime.date(this_year, 6, 30)
    else:  # 如果当前是第四季度，则上个季度是今年的第三季度
        last_quarter_start = datetime.date(this_year, 7, 1)
        last_quarter_end = datetime.date(this_year, 9, 30)


    # 去年一季度日期
    last_year_q1_start = datetime.date(this_year - 1, 1, 1)
    last_year_q1_end = datetime.date(this_year - 1, 3, 31)
    
    # 日变量替换
    text = text.replace("todayDate", replace_with_format(now, "%Y%m%d"))
    text = text.replace("yesterdayDate", replace_with_format(now - datetime.timedelta(days=1), "%Y%m%d"))

    text = text.replace("thisMonthFirstDayDate", replace_with_format(first_day_of_current_month, "%Y%m%d"))
    text = text.replace("thisMonthLastDayDate", replace_with_format(last_day_of_current_month, "%Y%m%d"))

    text = text.replace("lastMonthFirstDayDate", replace_with_format(first_day_of_last_month, "%Y%m%d"))
    text = text.replace("lastMonthLastDayDate", replace_with_format(last_day_of_last_month, "%Y%m%d"))

    text = text.replace("thisYearFirstDayDate", replace_with_format(first_day_of_this_year, "%Y%m%d"))
    text = text.replace("thisYearLastDayDate", replace_with_format(last_day_of_this_year, "%Y%m%d"))

    text = text.replace("thisWeekFirstDayDate", this_week_start.strftime("%Y%m%d"))
    text = text.replace("thisWeekLastDayDate", this_week_end.strftime("%Y%m%d"))
    
    text = text.replace("lastWeekFirstDayDate", last_week_start.strftime("%Y%m%d"))
    text = text.replace("lastWeekLastDayDate", last_week_end.strftime("%Y%m%d"))

    text = text.replace("thisYearFirstQuarterFirstDayDate", this_year_q1_start.strftime("%Y%m%d"))
    text = text.replace("thisYearFirstQuarterLastDayDate", this_year_q1_end.strftime("%Y%m%d"))
    text = text.replace("lastYearFirstQuarterFirstDayDate", last_year_q1_start.strftime("%Y%m%d"))
    text = text.replace("lastYearFirstQuarterLastDayDate", last_year_q1_end.strftime("%Y%m%d"))

    text = text.replace("lastQuarterFirstDayDate", last_quarter_start.strftime("%Y%m%d"))
    text = text.replace("lastQuarterLastDayDate", last_quarter_end.strftime("%Y%m%d"))

    text = text.replace("lastYearLastMonthFirstDayDate", replace_with_format(last_year_last_month_start, "%Y%m%d"))
    text = text.replace("lastYearFirstDayDate", replace_with_format(first_day_of_last_year, "%Y%m%d"))
    text = text.replace("lastYearLastDayDate", replace_with_format(last_day_of_last_year, "%Y%m%d"))


    # 周变量替换
    text = text.replace("lastWeek", last_week_start.strftime("%Y") + str(int(last_week_start.strftime("%W"))))
    text = text.replace("thisWeek", now.strftime("%Y") + str(int(now.strftime("%W"))))
    text = text.replace("thisMonthFirstWeek", now.strftime("%Y") + str(int(first_day_of_current_month.strftime("%W"))))
    text = text.replace("thisMonthLastWeek", now.strftime("%Y") + str(int(last_day_of_current_month.strftime("%W"))))


    # 月变量替换
    text = text.replace("lastMonth", f"{last_month_year}{last_month:02d}")
    text = text.replace("thisMonth", replace_with_format(now, "%Y%m"))
    text = text.replace("thisYearFirstQuarterFirstMonth", this_year_q1_start.strftime("%Y%m"))
    text = text.replace("thisYearFirstQuarterLastMonth", this_year_q1_end.strftime("%Y%m"))
    text = text.replace("lastYearFirstQuarterFirstMonth", last_year_q1_start.strftime("%Y%m"))
    text = text.replace("lastYearFirstQuarterLastMonth", last_year_q1_end.strftime("%Y%m"))
    text = text.replace("thisYearFirstMonth", replace_with_format(first_day_of_this_year, "%Y%m"))
    text = text.replace("thisYearLastMonth", replace_with_format(datetime.datetime(this_year, 12, 1), "%Y%m"))
    text = text.replace("lastYearFirstMonth", replace_with_format(first_day_of_last_year, "%Y%m"))
    text = text.replace("lastYearLastMonth", replace_with_format(last_day_of_last_year, "%Y%m"))
    text = text.replace("lastQuarterFirstMonth", last_quarter_start.strftime("%Y%m"))
    text = text.replace("lastQuarterLastMonth", (last_quarter_start + relativedelta(months=2)).strftime("%Y%m"))

    # 年变量替换
    text = text.replace("thisYear", str(this_year))
    text = text.replace("lastYear", str(this_year - 1))


    return text




parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
file = os.path.join(parent_dir, 'data', 'auto_test_json_extract_data.csv')

result = []

errors = []
# 打开CSV文件
with open(file, 'r', encoding='utf-8') as f:
    # 创建CSV读取器
    csv_reader = csv.reader(f, delimiter='\t')

    # 跳过标题行
    # headers = next(csv_reader)
    # print(f'列名: {headers}')

    # 逐行读取CSV内容
    for row in tqdm(csv_reader):
        try:
            if row == ['', ''] or row == []:
                continue
            question,answer,kpi = row
            answer = replace_date_params(answer)
            kpi = kpi.split(',')

            chat_ass = ChatWarroomAssistant()
            chat_ass.questionText = question

            
            # chat_ass.input_process(llm_json)

            chat_ass.language = '中文'
            chat_ass.intention = '数据查询'
            chat_ass.kpi = kpi
            
            # 新增对问题中by SVR的处理，防止无法按照地区维度查询
            chat_ass.questionText = chat_ass.questionText.replace('by SVR','by region').replace('by svr','by region')
            chat_ass.input_text = chat_ass.questionText
            # 转化后的完整问题作为历史问题保存在history_question变量
            chat_ass.history_question = chat_ass.questionText
            # 问题中的 同比/环比 转化为同环比，防止出现 同比/环比时，后续时间提取不准
            chat_ass.input_text = chat_ass.input_text.replace('同比','同环比').replace('环比','同环比').replace('同同','同')



            chain_warroom = LLMChain(llm=llm_json, prompt=prompt_extract_args)
            t1 = time.time()
            response_json = chain_warroom.run(today=chat_ass.date_info['today_date_cn']
                                              , question=chat_ass.questionText,weekday=chat_ass.date_info['today_weekday']
                                              , kpi_all=kpi_all
                                              , this_year_first_day=chat_ass.date_info['this_year_first_day_date_str']
                                              , this_year_last_day=chat_ass.date_info['this_year_last_day_date_str']
                                              )
                                            
            t2 = time.time()
            # 时间差
            time_cost = t2 - t1
            # print('response_json',response_json)

            format_json = extract_json_from_markdown(response_json)

            format_json_list = json_to_list(format_json, chat_ass.kpi)
            print("format_json_list",format_json_list)

            if format_json_list == []:
                result.append([question,answer,format_json,False,['kpi'],time_cost])
                continue
            
            format_json = format_json_list[0]['json_list'][0]['json_clean']

            data1 = json.loads(format_json)
            # print("data1",data1)

            data2 = json.loads(answer)

            tmp = []

            if data1 == {}:
                result.append([question,answer,format_json,False,['all'],time_cost])
                continue

            for k in data1.keys():
                if k == 'time' or k == 'template_id':
                    continue
                v1 = data1[k]
                v2 = data2[k]
                if k in ["model","location","kpi"]:
                    v1.sort()
                    v2.sort()
                
                if v1 != v2:
                    tmp.append(k)


            if tmp == []:
                is_equal = True
            else:
                is_equal = False
            result.append([question,answer,format_json,is_equal,tmp,time_cost])

        

        except Exception as e:
            e_msg = traceback.format_exc()
            print(e_msg)
            errors.append([question,answer,e])

        # time.sleep(20)
        # print(result)
        # break


import pandas as pd 



from datetime import datetime
# 获取当前时间
current_time = datetime.now()
formatted_time = current_time.strftime("%Y%m%d%H%M%S")

llm_type = 'wenxin-speed'

# output_file = os.path.join(parent_dir, 'data', 'auto_test_result_json_extract_'+llm_type+str(formatted_time)+'.xlsx')
output_file = os.path.join(parent_dir, 'data', 'auto_test_result_json_extract_wenxinturbo0628-newprompt-all-0927.xlsx')


# with open(output_file, 'w', newline='', encoding='utf-8') as f:
#     csv_writer = csv.writer(f, quoting=csv.QUOTE_NONE, escapechar='\\')
#     csv_writer.writerow(result)


result_df = pd.DataFrame(result)
result_df.columns = ["问题","标准答案","生成答案","是否相等","不相等的字段", "时间差(s)"]
# print(result_df)
result_df.to_excel(output_file,index=None)

if errors != []:
    errors_df = pd.DataFrame(errors)
    errors_df.columns = ["问题","标准答案","错误信息"]
    errors_df.to_excel(os.path.join(parent_dir, 'data', 'errors_json_extract.xlsx'),index=None)