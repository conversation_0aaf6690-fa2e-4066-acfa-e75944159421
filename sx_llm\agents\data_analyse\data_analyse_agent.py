import time
import json
import asyncio

from utils.config import *
from utils.sx_log import save_process_log
from utils.clean_json import clean_kpi
from utils.sx_request import async_request_with_retries
from utils.env_config import warroom_url
from utils.sx_chart_process import chart_select, content_show
from utils.llm import llm_dict
from utils.sx_date import get_date_info
from agents.data_analyse.prompt import *
from functions.web_search import get_web_contents_batch
from functions.warroom_data_process import format_chart_data, warroom_json_generate, warroom_data_clean, answer_warroom, get_cleaned_json_list

# 使用deepseek V3模型作为问题分解模型
llm = llm_dict["deepseekv3stream"]


class DataAnalyseAgent:
    date_info = get_date_info()
    today = date_info['today_date_cn']
    weekday = date_info['today_weekday']
    
    @staticmethod
    async def get_internal_query_analysis_results(question: str) -> dict:
        prompt = prompt_internal_query_json_template.format(question=question, today=DataAnalyseAgent.today, weekday=DataAnalyseAgent.weekday, kpi_all=kpi_all)
        content = [{"role": "user","content": prompt}]
        response = await llm.stream_response(content)
        return response
    
    @staticmethod
    def get_json_list(raw_json_str: str) -> list:
        print('raw_json_str', raw_json_str)
        raw_json = json.loads(raw_json_str)
        # 限制内部查询问题数量，目前先从问题限制，后续考虑限制查询的JSON数量
        num_queries = 5
        json_list = raw_json['json_list'][0:num_queries]
        return json_list


    @staticmethod
    async def process_single_json(json_item, chart_type, type, tmp_kpi):
        """处理单个 json 请求的异步函数"""
        tmp_json_clean = json_item['json_clean']
        warroom_json = warroom_json_generate(tmp_json_clean)
        
        t1 = time.time()
        response_warroom_no_clean = await async_request_with_retries(warroom_url, warroom_json)
        t2 = time.time()
        save_process_log('沙盘数据查询json--'+warroom_json, response_warroom_no_clean, t2 - t1)

        if not response_warroom_no_clean:
            return []

        response_warroom = warroom_data_clean(response_warroom_no_clean)
        if not response_warroom:
            return []

        json_show = content_show(tmp_json_clean, response_warroom, chart_type, type, '中文')
        if not json_show:
            return []

        qa_answer = answer_warroom(tmp_json_clean, tmp_kpi, '中文')
        text_show = [{"type": "text", "text": {"content": qa_answer}}]
        return text_show + json_show


    @staticmethod
    async def process_format_json(item):
        """处理单个 format_json 项的异步函数"""
        tmp_kpi = item['kpi']
        tmp_display = item['display']
        
        num_limit = 3
        
        # 图表展示选择
        chart_type, type = chart_select(tmp_display)
        
        # 并发处理该项下的所有 json_list
        tasks = [
            DataAnalyseAgent.process_single_json(json_item, chart_type, type, tmp_kpi)
            for json_item in item['json_list'][0:num_limit]
        ]
        results = await asyncio.gather(*tasks)
        
        # 合并该项的所有结果
        return [result for result in results if result]


    @staticmethod
    async def data_query(format_json_list: list) -> list:
        # 创建所有 format_json 项的任务
        tasks = [DataAnalyseAgent.process_format_json(item) for item in format_json_list]
        
        # 并发执行所有任务
        all_results = await asyncio.gather(*tasks)
        
        # 将所有结果扁平化处理成一个列表
        response = []
        for results in all_results:
            for result in results:
                response.extend(result)
                
        return response

    @staticmethod
    async def data_query_new(format_json_list: list) -> list:
        # 创建所有 format_json 项的任务
        tasks = [format_chart_data(item) for item in format_json_list]
        
        # 并发执行所有任务
        all_results = await asyncio.gather(*tasks)
        
        # 将所有结果扁平化处理成一个列表
        response = []
        for result in all_results:
            response.extend(result)
                
        return response


    
    @staticmethod
    async def get_query_analysis_results(question):
        prompt = prompt_query_analysis_template.format(question=question)
        content = [{"role": "user","content": prompt}]
        response = await llm.stream_response(content)
        return response
    
    @staticmethod
    def clean_json(raw_json_str):
        # 去除json中的空格，将字符串解析为json
        json_str = raw_json_str.replace(' ', '')
        print(json_str)
        json_obj = json.loads(json_str)
        return json_obj
    
    @staticmethod
    def get_search_queries(json_obj):
        search_queries = json_obj['查询问题']
        return search_queries


    @classmethod
    async def get_warroom_data_agent(cls, question: str) -> dict:
        t1 = time.time()
        query_results = await DataAnalyseAgent.get_internal_query_analysis_results(question)
        t2 = time.time()
        save_process_log('内部数据查询拆解多个问题', query_results, t2 - t1)
        

        json_list = DataAnalyseAgent.get_json_list(query_results)
        save_process_log('限制数量后的内部查询JSON列表', json_list)
        
        num_limit = 3
        cleaned_json_list = get_cleaned_json_list(json_list)[0:num_limit]
        save_process_log('清洗后限制数量的内部查询JSON列表', cleaned_json_list)
        
        t3 = time.time()
        data_result = await DataAnalyseAgent.data_query(cleaned_json_list)
        t4 = time.time()
        save_process_log('内部数据查询调用接口查询数据', data_result, t4 - t3)
        return data_result
        

    @classmethod
    async def get_web_contents_agent(cls, question: str):
        t1 = time.time()
        # 获取查询分析结果
        analysis_results = await cls.get_query_analysis_results(question)
        t2 = time.time()
        save_process_log('联网搜索拆解多个问题', analysis_results, t2 - t1)
        
        clean_results = cls.clean_json(analysis_results)
        
        # 获取查询列表
        # 限制查询问题的数量
        num_queries = 5
        query_list = cls.get_search_queries(clean_results)[0:num_queries]
        save_process_log('限制数量后的联网搜索问题', query_list)

        # 联网搜索
        t3 = time.time()
        results = await get_web_contents_batch(query_list)
        t4 = time.time()
        save_process_log('联网搜索网页内容', results, t4 - t3)
        
        return results
        
    
if __name__ == '__main__':
    question = '分析去年途昂的销售转化情况'
    # llm_chat(question)
    # result = DataAnalyseAgent.get_query_analysis_results(question)
    async def test_get_query_analysis_results():
        question = "查询本月途观L的销售情况"
        result = await DataAnalyseAgent.get_internal_query_analysis_results(question)
        print(f"结果: {result}")
        # json_list = DataAnalyseAgent.get_json_list(result)
        # json_list = [{'time': ['20250201-20250228'], 'date_type': 'month', 'location': ['全国'], 'model': ['途观L'], 'display': '', 'data_dim': 'model', 'today': 'False', 'not_total': 'True', 'template': '', 'kpi': ['销量']}, {'time': ['20250201-20250228'], 'date_type': 'month', 'location': ['全国'], 'model': ['途观L'], 'display': '', 'data_dim': 'location', 'today': 'False', 'not_total': 'True', 'template': '', 'kpi': ['销售表现']}]
        # print(f"json_list: {json_list}")
        # cleaned_json_list = DataAnalyseAgent.get_cleaned_json_list(json_list)
        # print(f"cleaned_json_list: {cleaned_json_list}")
        # data_result = await DataAnalyseAgent.data_query(cleaned_json_list)
        # data_result = await DataAnalyseAgent.get_warroom_data(question)
        # print(f"data_result: {data_result}")

    # 创建事件循环
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        # 运行测试函数
        loop.run_until_complete(test_get_query_analysis_results())
    finally:
        # 关闭事件循环
        loop.run_until_complete(loop.shutdown_asyncgens())
        loop.close()
    # print('\n---------------------------------------------------')
    # json_obj = DataAnalyseAgent.clean_json(result)
    # print(json_obj)
    # search_queries = DataAnalyseAgent.get_search_queries(json_obj)
    # print(search_queries)
    # search_queries = ['去年途昂的总销量和销售转化率具体数值是多少？', '去年途昂各季度/月份的销售转化率变化趋势如何？']
    # web_contents = DataAnalyseAgent.search_web_contents(search_queries)
    # print(web_contents)
    # cleaned_json_list = [{'kpi': '日均含大客户发票数', 'display': '趋势', 'today': 'False', 'json_list': [{'json_origin': {'date_type': 'quarter', 'location': ['全国'], 'model': ['途观L'], 'display': '趋势', 'data_dim': 'all', 'today': 'False', 'not_total': 'True', 'template': '', 'kpi': ['日均销量', '库存当量'], 'start_time': '20250101', 'end_time': '20250331'}, 'json_clean': '{"time": "202501-202502", "start_time": "202501", "end_time": "202502", "date_type": "month", "model": ["Tiguan"], "location": ["Nationwide"], "kpi": ["日均含大客户发票数", "含大客户发票数日均同比", "含大客户发票数日均环比"], "display": "趋势", "today": "False", "data_dim": "all", "not_total": "True", "template_id": ""}'}]}, {'kpi': '总库存当量', 'display': '趋势', 'today': 'False', 'json_list': [{'json_origin': {'date_type': 'quarter', 'location': ['全国'], 'model': ['途观L'], 'display': '趋势', 'data_dim': 'all', 'today': 'False', 'not_total': 'True', 'template': '', 'kpi': ['日均销量', '库存当量'], 'start_time': '20250101', 'end_time': '20250331'}, 'json_clean': '{"time": "202501-202502", "start_time": "202501", "end_time": "202502", "date_type": "month", "model": ["Tiguan"], "location": ["Nationwide"], "kpi": ["总库存当量", "总库存"], "display": "趋势", "today": "False", "data_dim": "all", "not_total": "True", "template_id": ""}'}]}]
