"""
多智能体客户端 - 使用A2A协议连接多个智能体

这个模块提供了一个客户端类，可以连接和管理多个A2A协议智能体。
它允许发现智能体、发送任务、获取任务状态和结果，并能根据用户输入自动选择合适的智能体。
它支持使用大模型来判断用户意图并选择最合适的智能体。
"""

import asyncio
import uuid
from typing import Dict, List, Optional, Any, Tuple

from agents.insurance.common.client.card_resolver import A2ACardResolver
from agents.insurance.common.client.client import A2AClient
from agents.insurance.common.types import (
    AgentCard,
    Message,
    Task,
    TaskSendParams,
    TextPart,
    TaskState,
)

from agents.insurance.agent_selector import AgentSelector


class MultiAgentClient:
    """
    多智能体客户端类，用于连接和管理多个A2A协议智能体。

    这个类允许：
    - 发现和连接多个智能体
    - 根据用户输入自动选择合适的智能体
    - 使用大模型判断用户意图并选择智能体
    - 向不同智能体发送任务
    - 获取任务状态和结果
    - 简单的任务管理
    """

    def __init__(self):
        """
        初始化多智能体客户端
        """
        self.agents: Dict[str, Tuple[AgentCard, A2AClient]] = {}
        self.tasks: Dict[str, Dict[str, Any]] = {}  # 任务ID -> {agent_name, task}
        self.session_id: str = str(uuid.uuid4())
        self.current_agent: Optional[str] = None  # 当前活跃的智能体名称
        self.conversation_history: List[Dict[str, str]] = []  # 对话历史

        # 初始化智能体选择器
        self.agent_selector = None

        try:
            self.agent_selector = AgentSelector()
            print("已启用大模型智能体选择器")
        except Exception as e:
            print(f"初始化大模型智能体选择器失败: {str(e)}")

    async def discover_agent(self, agent_url: str) -> AgentCard:
        """
        发现并连接一个智能体

        Args:
            agent_url: 智能体的URL地址

        Returns:
            AgentCard: 智能体的卡片信息

        Raises:
            Exception: 如果无法连接到智能体或获取智能体卡片
        """
        try:
            # 使用A2ACardResolver获取智能体卡片
            card_resolver = A2ACardResolver(agent_url)
            agent_card = card_resolver.get_agent_card()

            # 创建A2A客户端
            client = A2AClient(agent_card=agent_card)

            # 存储智能体信息
            self.agents[agent_card.name] = (agent_card, client)

            print(f"已发现并连接智能体: {agent_card.name}")
            return agent_card
        except Exception as e:
            print(f"连接智能体失败: {str(e)}")
            raise

    def list_agents(self) -> List[Dict[str, str]]:
        """
        列出所有已连接的智能体

        Returns:
            List[Dict[str, str]]: 智能体信息列表，每个智能体包含名称和描述
        """
        return [
            {"name": card.name, "description": card.description or "无描述"}
            for card, _ in self.agents.values()
        ]

    def select_agent_for_message(self, message: str) -> Optional[str]:
        """
        根据用户消息自动选择合适的智能体

        这个方法可以使用两种方式选择智能体：
        1. 使用大模型分析用户意图并选择智能体（如果启用）

        Args:
            message: 用户消息

        Returns:
            Optional[str]: 选择的智能体名称，如果无法确定则返回None
        """
        if not self.agents:
            return None

        # # 如果只有一个智能体，直接选择它
        # if len(self.agents) == 1:
        #     return list(self.agents.keys())[0]

        # 使用大模型选择智能体
        if self.agent_selector:
            try:
                # 使用对话历史和当前消息选择智能体
                selected_agent, reasoning = self.agent_selector.select_agent(
                    self.agents,
                    message,
                    self.current_agent,
                    self.conversation_history
                )

                if selected_agent:
                    print(f"大模型选择了智能体: {selected_agent}")
                    print(f"选择理由: {reasoning}")
                    self.current_agent = selected_agent

                    # 更新对话历史
                    self.conversation_history.append({"role": "user", "content": message})

                    return selected_agent
                else:
                    print(f"大模型无法确定合适的智能体: {reasoning}")
            except Exception as e:
                print(f"使用大模型选择智能体时出错: {str(e)}")
                print("回退到规则匹配方式...")


        # 如果有当前活跃的智能体，继续使用它
        if self.current_agent and self.current_agent in self.agents:
            return self.current_agent

        # 如果无法确定，返回None
        return None

    async def send_message(self, message: str) -> Optional[Task]:
        """
        发送消息并自动选择合适的智能体

        Args:
            message: 用户消息

        Returns:
            Optional[Task]: 任务对象，如果发送失败则返回None

        Raises:
            ValueError: 如果无法确定要使用的智能体
        """
        # 自动选择智能体
        agent_name = self.select_agent_for_message(message)

        if not agent_name:
            raise ValueError("无法确定要使用的智能体，请明确指定智能体名称")

        # 发送任务
        return await self.send_task(agent_name, message)

    async def send_task(self, agent_name: str, message: str) -> Optional[Task]:
        """
        向指定智能体发送任务

        Args:
            agent_name: 智能体名称
            message: 要发送的消息

        Returns:
            Optional[Task]: 任务对象，如果发送失败则返回None

        Raises:
            ValueError: 如果指定的智能体不存在
        """
        if agent_name not in self.agents:
            raise ValueError(f"智能体 '{agent_name}' 不存在")

        # 更新当前活跃的智能体
        self.current_agent = agent_name

        _, client = self.agents[agent_name]

        # 创建任务ID
        task_id = str(uuid.uuid4())

        # 准备任务参数
        payload = TaskSendParams(
            id=task_id,
            sessionId=self.session_id,  # 使用会话ID
            message=Message(
                role="user",
                parts=[TextPart(text=message)],
                metadata={"conversation_id": self.session_id}  # 添加元数据
            ),
            metadata={"conversation_id": self.session_id}  # 添加元数据
        )

        try:
            # 发送任务
            result = await client.send_task(payload.model_dump())

            # 存储任务信息
            self.tasks[task_id] = {
                "agent_name": agent_name,
                "task": result.result
            }

            # 更新对话历史（如果尚未添加）
            if not any(item.get("content") == message and item.get("role") == "user"
                      for item in self.conversation_history[-2:]):
                self.conversation_history.append({"role": "user", "content": message})

            # 提取智能体回复并添加到对话历史
            if result.result and result.result.status.message:
                agent_messages = result.result.status.message.parts[0].text
                if agent_messages:
                    self.conversation_history.append({"role": "assistant", "content": agent_messages})

            return result.result
        except Exception as e:
            print(f"发送任务失败: {str(e)}")
            return None

    async def get_task(self, task_id: str) -> Optional[Task]:
        """
        获取任务状态和结果

        Args:
            task_id: 任务ID

        Returns:
            Optional[Task]: 任务对象，如果获取失败则返回None

        Raises:
            ValueError: 如果指定的任务不存在
        """
        if task_id not in self.tasks:
            raise ValueError(f"任务 '{task_id}' 不存在")

        task_info = self.tasks[task_id]
        agent_name = task_info["agent_name"]

        if agent_name not in self.agents:
            raise ValueError(f"智能体 '{agent_name}' 不存在")

        _, client = self.agents[agent_name]

        try:
            # 获取任务状态
            result = await client.get_task({"id": task_id})

            # 更新任务信息
            self.tasks[task_id]["task"] = result.result

            return result.result
        except Exception as e:
            print(f"获取任务失败: {str(e)}")
            return None

    def get_agent_skills(self, agent_name: str) -> List[Dict[str, Any]]:
        """
        获取指定智能体的技能列表

        Args:
            agent_name: 智能体名称

        Returns:
            List[Dict[str, Any]]: 技能列表

        Raises:
            ValueError: 如果指定的智能体不存在
        """
        if agent_name not in self.agents:
            raise ValueError(f"智能体 '{agent_name}' 不存在")

        card, _ = self.agents[agent_name]
        return [skill.model_dump() for skill in card.skills]

    async def wait_for_task_completion(self, task_id: str, timeout: int = 60, interval: int = 1) -> Optional[Task]:
        """
        等待任务完成

        Args:
            task_id: 任务ID
            timeout: 超时时间（秒）
            interval: 轮询间隔（秒）

        Returns:
            Optional[Task]: 完成的任务对象，如果超时或失败则返回None
        """
        start_time = asyncio.get_event_loop().time()

        while asyncio.get_event_loop().time() - start_time < timeout:
            task = await self.get_task(task_id)

            if not task:
                return None

            if task.status.state == TaskState.COMPLETED:
                return task

            if task.status.state in [TaskState.failed, TaskState.canceled]:
                print(f"任务 {task_id} 状态: {task.status.state}")
                return task

            await asyncio.sleep(interval)

        print(f"等待任务 {task_id} 超时")
        return None

    def extract_agent_response(self, task: Task) -> str:
        """
        从任务中提取智能体的回复文本

        Args:
            task: 任务对象

        Returns:
            str: 智能体的回复文本
        """
        if not task or not task.status.message:
            return "无回复"

        # 提取智能体消息
        agent_messages = task.status.message.parts[0].text

        if not agent_messages:
            return "无智能体回复"

        # 获取最新的智能体消息
        latest_message = agent_messages

        # 提取文本部分
        text_parts = [latest_message]

        return " ".join(text_parts) if text_parts else "无文本回复"

    def get_conversation_history(self) -> List[Dict[str, str]]:
        """
        获取对话历史

        Returns:
            List[Dict[str, str]]: 对话历史列表
        """
        return self.conversation_history.copy()

    def clear_conversation_history(self) -> None:
        """清空对话历史"""
        self.conversation_history = []
        if self.agent_selector:
            self.agent_selector.clear_history()

    def set_conversation_history(self, history: List[Dict[str, str]]) -> None:
        """
        设置对话历史

        Args:
            history: 对话历史列表
        """
        self.conversation_history = history.copy()
        if self.agent_selector:
            self.agent_selector.set_history(history)
