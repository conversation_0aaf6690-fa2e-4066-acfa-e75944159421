# from langchain_huggingface import HuggingFaceEmbeddings


# embeddings = HuggingFaceEmbeddings(model_name="D:\models\\nlp_gte_sentence-embedding_chinese-small")






# query = "部门如何进行会务接待礼仪服务申请？会务礼仪申请？"


# print(embeddings.embed_query(query))



# from dataclasses import dataclass
# from typing import Optional  # 需要添加Optional类型

# @dataclass
# class AgentContext:
#     profile_a: Optional[str] = None  # 使用Optional类型并设置默认值
#     profile_b: Optional[str] = None  # 保持字段顺序一致


# class BaseAgent:
#     def __init__(self, context: AgentContext = None):
#         self.ctx = context
#         self.client = self.ctx.client
#         self.user_name = self.ctx.user_name
        
#     def set_context(self, input_json):
#         self.ctx = input_json
#         self.client = self.ctx.get('client', None)
#         self.user_name = self.ctx.get('user_name', None)

# class TestAAgent(BaseAgent):
#     def __init__(self, context: AgentContext = None):
#         super().__init__(context)

# class TestBAgent(BaseAgent):
#     def __init__(self, context: AgentContext = None):
#         super().__init__(context)

# if __name__ == '__main__':
#     agentA = TestAAgent()
#     agentA.set_context({'client': 'test', 'user_name': 'test'})
#     print(agentA.client)
#     agentB = TestBAgent(agentA.ctx)
#     print(agentB.client)




a = 'asdasdasda'
b = a[-len(a):]
print(a==b)
print(b)