from typing import Dict
from cozepy.auth import As<PERSON><PERSON><PERSON><PERSON><PERSON>
from cozepy import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ze<PERSON>IError, COZE_CN_BASE_URL

from utils.config import COZE_PROXY_URL
from utils.config import COZE_JWT_OAUTH_CLIENT_ID, COZE_JWT_OAUTH_PRIVATE_KEY, COZE_JWT_OAUTH_PUBLIC_KEY_ID
from utils.sx_log import save_process_log

class CozeWorkflowExecutor:

    def __init__(self):
        self.coze = None
        self._initialize()
    
    def _initialize(self):
        # 初始化 AsyncJWTOAuthApp
        async_jwt_oauth_app = AsyncJWTOAuthApp(
            client_id=COZE_JWT_OAUTH_CLIENT_ID,
            private_key=bytes(COZE_JWT_OAUTH_PRIVATE_KEY, "utf-8").decode("unicode_escape"),
            public_key_id=COZE_JWT_OAUTH_PUBLIC_KEY_ID,
            base_url=COZE_PROXY_URL,
        )
        async_jwt_oauth_app._api_endpoint = 'api.coze.cn'
        # 初始化Coze client
        self.coze = AsyncCoze(
            auth=AsyncJWTAuth(oauth_app=async_jwt_oauth_app),
            base_url=COZE_PROXY_URL
        )
        
    async def execute_workflow(self, workflow_id: str, parameters: Dict) -> str:
        try:
            result = await self.coze.workflows.runs.create(workflow_id=workflow_id, parameters=parameters, is_async=False)
            return result
        except CozeAPIError as e:
            save_process_log('coze异常', e)
            return None
        
# 使用示例
if __name__ == "__main__":
    executor = CozeWorkflowExecutor()
    import asyncio
    async def main():
        import time
        start_time = time.time()
        result = await executor.execute_workflow("7470708539339489320", {"query": "上汽大众途昂pro", "count": 4})
        print(result)
        end_time = time.time()
        print(f"Execution time: {end_time - start_time} seconds")
    # 使用 asyncio.run() 来运行异步函数
    asyncio.run(main())
    