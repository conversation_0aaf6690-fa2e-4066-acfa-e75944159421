# ================= 服务端代码 websocket_server.py =================
import uvicorn
from fastapi import FastAPI, WebSocket

app = FastAPI()

@app.websocket("/api/V2/chat/ws")
async def new_stream_chat(websocket: WebSocket):
    await websocket.accept()
    try:
        while True:
            input_data = await websocket.receive_text()
            print(f"收到消息: {input_data}")
            
            # 添加响应逻辑
            response = f"已收到你的消息: {input_data}"
            await websocket.send_text(response)
    except Exception as e:
        print(f"连接异常关闭: {e}")
    finally:
        await websocket.close()

if __name__ == "__main__":
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8000,
        ws_ping_interval=15,
        ws_ping_timeout=5,
        log_level="debug"
    )