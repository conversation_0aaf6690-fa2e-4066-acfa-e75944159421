from openai import AzureOpenAI
from langchain.prompts.prompt import PromptTemplate
from tqdm import tqdm

client = AzureOpenAI(
  azure_endpoint = "https://openai-svw-sx-us2.openai.azure.com/", 
  api_key = "********************************",  
  api_version = "2023-07-01-preview"
)

deployment_name="GPT-4-Turbo-SX"



prompt_template = """[要求]
请将问题翻译为英语，并以文本格式输出。

[问题]
{question}

[翻译]"""


prompt = PromptTemplate(template=prompt_template,input_variables=["question"])



import pandas as pd
import os
parent_dir = os.path.dirname(os.path.abspath(__file__))
data_file_path = os.path.join(parent_dir, 'data_chn2eng.csv')

# df = []
# data = pd.read_csv(data_file_path,header=None)
# for i in tqdm(range(len(data))):
question = '你好'
content = prompt.format(question=question)
response = client.chat.completions.create(model = deployment_name, temperature = 0,seed = 123,messages=[{"role": "user", "content": content}])
result = response.choices[0].message.content
print(result)
# df.append([question,result])

# df = pd.DataFrame(df,columns=['question','result'])
# df.to_excel('data_chn2eng_result.xlsx',index=False)


# try:
#   response = client.chat.completions.create(
#       model = deployment_name, 
#       temperature = 0,
#       seed = 123,
#       # response_format = { "type": "json_object" },
#       # response_format = { "type": "json_object" },
#       messages=[
#           # {"role": "system", "content": "你是一个有用的助手，被设计用于输出JSON."},
#           {"role": "user", "content": content}
#       ]
#   )
#   print(response)

# except Exception as e:
#   print(e)















# import os

# from langchain.schema import HumanMessage, SystemMessage, AIMessage
# from langchain_openai import AzureChatOpenAI

# openai_api_version = "2023-07-01-preview"
# azure_endpoint = "https://openai-svw-sx-us2.openai.azure.com/"
# openai_api_key = "********************************"
# deployment_name = "GPT-4-Turbo-SX"

# model = AzureChatOpenAI(openai_api_version=openai_api_version,azure_endpoint=azure_endpoint,openai_api_key=openai_api_key,deployment_name=deployment_name)
# messages =  [
#     # SystemMessage(content="你是SX部门的人工智能助手"),
#     # HumanMessage(content="如果问你是谁，请回答\"我是SX部门的人工智能助手\""),
#     # AIMessage(content="好的"),
#     HumanMessage(content="你是一个人工智能助手，请按顺序执行下面的任务。\n第一步，提取问题中的时间，并以今天（2024年2月5日）为基准，将时间转换成yyyymmdd-yyyymmdd的格式。\n第二步，从问题中识别以下信息，不要自动假设信息：\n- start_time：提取第一步中时间范围的开始时间，日期格式为yyyymmdd; 如果信息不存在, 请使用 \"\" 作为值。\n- end_time：提取第一步中时间范围的结束时间，日期格式为yyyymmdd; 如果信息不存在, 请使用 \"\" 作为值。\n- date_type：提取查询时间的类型，按天返回\"day\"，按周返回\"week\"，按月返回\"month\"，按年返回\"year\"; 如果信息不存在, 请使用 \"\" 作为值。\n- location：提取问题中出现的大区、省份、城市，无需进行分析和推理，并补充\"大区\"、\"省\"、\"市\"关键字，多值字段，用中括号 list 结构保存，如[\"全国\"]，[\"华东大区\"]，[\"北京市\",\"上海市\"]；无相关信息，则使用 [\"全国\"] 作为值。\n- model：提取问题中的车辆品牌或车辆类别或车型名称, 多值字段, 用中括号 list 结构保存;如果信息不存在, 请使用 [] 作为值。\n- display：提取问题中含有的数据展示形式，单值字段，仅包括\"趋势\",\"分布\",\"排名\",\"对比\",\"漏斗\",\"目标\",\"同环比\"，无需添加其他不在范围的词汇; 如果信息不存在, 请使用 \"\" 作为值。\n- data_dim：提取问题中想要进一步下钻细分的维度，下钻的维度包括\"location\"和\"model\"; 如果没有维度需要下钻, 请使用 \"all\" 作为值。\n- today：如果问题仅涉及今日数据或实时数据，返回\"True\"，否则，返回\"False\"。\n第三步，输出 \"start_time\",\"end_time\",\"date_type\",\"location\",\"model\",\"display\",\"data_dim\",\"today\" 为键的 JSON 对象。\n\n下面是正式的问题：\n问题：去年帕萨特的销量同环比\n回答："),
# ]

# response = model(messages=messages)
# print(response)