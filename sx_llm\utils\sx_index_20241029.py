import os
from langchain_community.document_loaders import TextLoader
from langchain_community.vectorstores import FAISS


# from utils.embedding_factory import EmbeddingFactory
# from utils.sx_text_splitter import SXCharacterTextSplitter
# from utils.config import *
# from utils.sx_log import sx_log
from embedding_factory import EmbeddingFactory
from sx_text_splitter import SXCharacterTextSplitter
from config import *
from sx_log import sx_log


embedding_factory = EmbeddingFactory()
embedding_model = embedding_factory.create_embedding_model()
embedding_model_instance = embedding_model.get_model_isntance()


# 读取当前文件父节点路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

def save_vector(file_path,save_file_path):
    file_path = os.path.join(parent_dir, 'data', file_path)
    loader = TextLoader(file_path,encoding='utf-8')
    documents = loader.load()
    text_splitter = SXCharacterTextSplitter(separator = "\n",chunk_size = 10,chunk_overlap = 0,length_function = len,)
    texts = text_splitter.split_documents(documents)
    db = FAISS.from_documents(texts, embedding_model_instance)
    save_file_path = os.path.join(parent_dir, 'vector_store', save_file_path)
    db.save_local(save_file_path)
    return db

# brand_db = save_vector('brand_name_cn.txt',"brand_index")
# model_db = save_vector('model_name_cn.txt',"model_index")
# knowledge_db = save_vector('knowledge_document.txt',"knowledge_index")
# template_db = save_vector('template_name.txt',"template_index")


# 查询问题匹配度前k的文本内容
def vector_index(knowledge_file_name,input_text,top_k=1) -> list:
    """
    从向量库中获取相关的文本
    :param file_path: 向量库路径
    :param input_text: 输入的文本
    :param top_k: 前k个匹配的文本
    :return: 检索器和匹配的文本列表
    """
    file_path = os.path.join(parent_dir, 'vector_store', knowledge_file_name)
    db = FAISS.load_local(file_path, embedding_model_instance, allow_dangerous_deserialization=True)
    docsearch = db.as_retriever(search_kwargs={"k": top_k})
    rel_docs = docsearch.invoke(input=input_text)
    sx_log.debug(rel_docs)
    results = [doc.page_content for doc in rel_docs]
    return docsearch,results

def get_knowledge_context(knowledge_file_name, input_text, top_k=5):
    """
    从知识库中获取相关的上下文
    :param knowledge_file_name: 知识库文件夹名
    :param input_text: 输入的文本
    :param top_k: 前k个匹配的文本
    :return: 匹配的文本列表
    """

    file_path = os.path.join(parent_dir, 'vector_store', knowledge_file_name)
    return vector_index(file_path, input_text, top_k)[1]


if __name__=="__main__":
    print(get_knowledge_context('model_index','帕san特'))





