import openai
# from utils.config import *


# 公有云
def chat_by_token(content: list):
    client = openai.OpenAI(
        api_key="MWJhM2Y2ZTQ3NThjNWY2Y2M0Mjk2M2U0YzhhMDE2ZTZiZWY0MjZmMQ==",
        base_url="http://1680606300477185.cn-shanghai.pai-eas.aliyuncs.com/api/predict/deepseek_svw_1_a.deepseek_svw_1/v1"
    )
    response = client.chat.completions.create(
        model="DeepSeek-R1",
        messages=content,
        stream=True
    )
    return response



# def chat_by_token(content:list):
#     client = openai.OpenAI(
#             api_key='d467668f-e6b2-4f6b-9e72-10dc85f426d7', 
#             base_url="https://ark.cn-beijing.volces.com/api/v3",
#             )
#     response = client.chat.completions.create(
#                 model="ep-20250205173717-f5xvc",
#                 messages=content,
#                 stream=True,
#             )
#     return response


def llm_chat(prompt, question):
    prompt_query_analysis = prompt.format(question=question)
    content = [{"role": "user","content": prompt_query_analysis}]
    qa_result = chat_by_token(content)

    ### deepseek模型流式输出
    content_num = 0
    result = ''
    for chunk in qa_result:
        if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
            tmp_result_value = chunk.choices[0].delta.reasoning_content
        else:
            if content_num == 0:
                print("⭐最终回答⭐")
                content_num += 1
            tmp_result_value = chunk.choices[0].delta.content
            result += tmp_result_value
        return result



if __name__ == '__main__':
    question = [{"role": "user","content": '解一元二次方程：x^2-5x+6=0'}]
    response = chat_by_token(question)
    result = ''
    is_thinking = True
    for chunk in response:
        content = chunk.choices[0].delta.content
        if content == '<think>':
            print("⭐思考中⭐")
            continue
        else:
            if content == '</think>':
                is_thinking = False
                print("⭐最终回答⭐")
                continue
            else:
                if not is_thinking:
                    result += content
        print(content, end='')