


from utils.chart_processors.base_chart_processor import BaseChartProcessor
from utils.sx_dict import sx_kpi_name, sx_mixed_chart_name, sx_kpi_compare_dict, sx_display_name





class DistributionChartProcessor(BaseChartProcessor):
    """Distribution chart processor"""
    
    def __init__(self, format_json) -> None:
        super().__init__(format_json)
    
    def chart_process(self, data, language):
        # 对比类图的type定义
        type = ["bar","line",'pie']
        # 根据问题语言对图形类型名称进行转换
        tab_list = [{'type': tmp_type,"name": sx_mixed_chart_name[tmp_type][language]} for tmp_type in type]
        
        # 某一维度下钻时，另一个维度如果存在多值，需要将该维度拆分为多个图
        if self.json_data_dim == "location":
            dim_tmp = "model"
        elif self.json_data_dim == "model":
            dim_tmp = "location"
        else:
            dim_tmp = "location"
        dim_list = list(set([i[dim_tmp] for i in data]))
        
        # 需下钻的维度
        if dim_tmp == "location":
            dim_parent = "brand"
        else:
            dim_parent = "parentLocation"
        # 转化指标的brand为空，默认赋值为'SVW-VW'
        dim_parent_list = list(set([i[dim_parent] if i[dim_parent] else 'SVW-VW' for i in data]))


        chart_list = []
        for kpi_temp in self.json_kpi:
            is_percentage = sx_kpi_compare_dict[kpi_temp]["percentage"]
            # 如果需要显示百分比，则过滤掉饼图
            if is_percentage == True:
                tmp_tab_list = [tmp for tmp in tab_list if tmp['type'] != 'pie']
            else:
                tmp_tab_list = tab_list
            kpi_temp_value = sx_kpi_compare_dict[kpi_temp]["value"]
            name_kpi = sx_kpi_name[kpi_temp_value][language]

            for j in dim_list:
                for k in dim_parent_list:
                    output_dict = {i[self.json_data_dim]:i["kpiValues"][kpi_temp_value] for i in data if i[dim_tmp] == j and i['time'] == 'total' 
                                    and kpi_temp_value in i["kpiValues"].keys() and (i[dim_parent] == k or (not i[dim_parent] and k == 'SVW-VW'))
                                and ( ( ( (i["model"].count('-') == 1 and kpi_temp not in ["销量","市占"]) or (kpi_temp in ["销量","市占"] and i['model'] != i['brand'])) and self.json_data_dim == 'model')
                                        or self.json_data_dim != 'model') and i['location'] not in ['总计','Total'] and i['model'] not in ['总计','Total']}
                    series = [{"name": "","percentage": is_percentage,"data": list(output_dict.values())}]
                    xAxis = list(output_dict.keys())
                    title = j + ' ' + k + ' ' + name_kpi + ' ' + sx_display_name["分布"][language]
                    chart_list.append({"type": "chart","title": title,"chart": {"chart_type": "mixed","xAxis":xAxis,"series":series,"tab_list": tmp_tab_list}})
        return chart_list
        