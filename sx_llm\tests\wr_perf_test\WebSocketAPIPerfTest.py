import asyncio
import random
import time
import websockets
import json
from datetime import datetime
import csv
import sys

# 问题列表
questions = ['查询数据', '分析数据', '查询数据']

def format_time(timestamp):
    return datetime.fromtimestamp(float(timestamp)).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

async def test_single_connection(url, num):
    # 记录开始连接时间
    start_connect_time = time.time()
    try:
        # 建立 WebSocket 连接
        async with websockets.connect(url) as websocket:
            # 接收初始回复
            await websocket.recv()

            # 记录连接建立时间
            connect_established_time = time.time()
            connect_duration = connect_established_time - start_connect_time

            # 随机打乱问题顺序
            random_questions = questions.copy()
            random.shuffle(random_questions)

            timings = []

            for question in random_questions:
                # 发送问题
                message = json.dumps({"userId": str(num), "sessionId": str(num), "questionText": question})
                # 记录发送问题时间
                send_time = time.time()
                await websocket.send(message)

                finished = False
                receive_times = []
                response_durations = []
                index = 1
                while not finished:
                    # 接收回复
                    response = await websocket.recv()
                    # 记录接收回复时间
                    receive_time = time.time()

                    try:
                        # 解析回复为 JSON
                        response_json = json.loads(response)
                        finished = response_json.get('finished', False)
                    except json.JSONDecodeError:
                        print(f"Failed to decode JSON response: {response}")
                        finished = True

                    # 计算每次交互的耗时
                    response_duration = receive_time - send_time

                    receive_times.append({f"receive_time_{index}": receive_time})
                    response_durations.append({f"response_duration_{index}": response_duration})
                    index += 1

                timing = {
                    "question": question,
                    "send_time": send_time,
                    "receive_times": receive_times,
                    "response_durations": response_durations
                }
                timings.append(timing)

            # 记录总连接时长
            end_time = time.time()
            total_connection_duration = end_time - start_connect_time

            return {
                "start_connect_time": start_connect_time,
                "connect_duration": connect_duration,
                "timings": timings,
                "total_connection_duration": total_connection_duration
            }
    except Exception as e:
        print(f"Connection error: {e}")
        return None

async def run_stress_test(url, num_connections):
    tasks = [test_single_connection(url, num) for num in range(num_connections)]
    results = await asyncio.gather(*tasks)
    return results

if __name__ == "__main__":
    url = "ws://127.0.0.1:8000/api/V1/chat/ws"
    # 检查是否提供了命令行参数
    if len(sys.argv) > 1:
        try:
            # 尝试将第一个参数转换为整数
            num_connections = int(sys.argv[1])
        except ValueError:
            print("错误：输入的参数必须是一个整数。")
            sys.exit(1)
    else:
        # 如果没有提供命令行参数，使用默认值
        num_connections = 10

    start_stress_time = time.time()
    results = asyncio.run(run_stress_test(url, num_connections))
    end_stress_time = time.time()

    # 输出结果
    for i, result in enumerate(results):
        if result:
            print(f"Connection {i + 1}:")
            print(f"  Start connect time: {format_time(result['start_connect_time'])}")
            print(f"  Connect duration: {result['connect_duration']} seconds")
            for timing in result["timings"]:
                print(f"  Question: {timing['question']}")
                print(f"    Send time: {format_time(timing['send_time'])}")
                for receive_time in timing["receive_times"]:
                    key = list(receive_time.keys())[0]
                    print(f"    {key}: {format_time(receive_time[key])}")
                for response_duration in timing["response_durations"]:
                    key = list(response_duration.keys())[0]
                    print(f"    {key}: {response_duration[key]} seconds")
            print(f"  Total connection duration: {result['total_connection_duration']} seconds")
        else:
            print(f"Connection {i + 1} failed.")

    print(f"Stress test completed in {end_stress_time - start_stress_time} seconds.")

    # 定义 CSV 文件的表头
    headers = ['Connection ID', 'Start connect time', 'Connect duration', 'Total connection duration']
    # 为每个问题交互添加表头
    max_responses = 0
    for result in results:
        if result:
            for timing in result["timings"]:
                max_responses = max(max_responses, len(timing["receive_times"]))

    for i in range(len(questions)):
        for j in range(1, max_responses + 1):
            headers.extend([
                f'Question {i + 1}',
                f'Send time {i + 1}',
                f'Receive time {i + 1}_{j}',
                f'Response duration {i + 1}_{j}'
            ])

    # 准备数据写入 CSV 文件
    rows = []
    for i, result in enumerate(results):
        if result:
            row = [
                i + 1,
                format_time(result['start_connect_time']),
                result['connect_duration'],
                result['total_connection_duration']
            ]
            for timing in result["timings"]:
                row.extend([timing['question'], format_time(timing['send_time'])])
                for receive_time, response_duration in zip(timing["receive_times"], timing["response_durations"]):
                    receive_key = list(receive_time.keys())[0]
                    response_key = list(response_duration.keys())[0]
                    row.extend([format_time(receive_time[receive_key]), response_duration[response_key]])
                # 填充空值
                for _ in range(max_responses - len(timing["receive_times"])):
                    row.extend([None, None])
            rows.append(row)
    
    # 获取当前 Unix 时间戳
    unix_time = int(time.time())
    # 构建带有 Unix 时间戳的 CSV 文件名
    csv_filename = f'connections_data_{unix_time}.csv'

    # 写入 CSV 文件
    with open(csv_filename, mode='w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(headers)
        writer.writerows(rows)