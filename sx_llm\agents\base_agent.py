import uuid, time, json, asyncio
from fastapi import Request


from database.redis_db import RedisDB
from utils.llm import llm_dict
from utils.config import MACHINE_IP
from utils.sx_date import get_date_info
from utils.sx_log import process_log, sx_log
from functions.web_search import get_web_contents_batch, format_web_contents_batch, format_output_web_contents_batch



class BaseAgent:
    def __init__(self):
        self.client = None
        self.access_token = None
        self.final_answer = ''
        self.thinking_content = ''
        self.charts = []
        self.input_json = None
        self.user_id = None
        self.conversation_id = None
        self.question = None
        self.is_internet_search = False
        self.language = '中文'
        self.input_time = time.time()
        self.history_message = []
        self.history_question = []
        self.conversation_all = []
        self.analyse_llm = llm_dict['deepseekr1stream']
        self.process_llm = llm_dict['deepseekv3stream']
        self.process_llm_0324 = llm_dict['qwen3']
        self.decompose_questions = None
        self.formatted_web_contents = ''
        self.date_info = get_date_info()
        self.today = self.date_info['today_date_cn']
        self.weekday = self.date_info['today_weekday']
        self.json_list = None
        self.warroom_data = None
        self.is_data_question = None
        self.analysis_result = None
        self.message_id = None
        
    async def get_input(self, request: Request):
        self.input_json = await request.json()
        self.access_token = request.headers.get("accesstoken")
        self.save_log('用户请求内容',self.input_json)
        self.message_id = str(uuid.uuid4())
        self.input_time = time.time()
        self.user_id = self.input_json["user_id"]
        self.conversation_id = self.input_json["conversation_id"]
        self.question = self.input_json["question"]
        self.is_internet_search = self.input_json["is_internet_search"]
        self.is_deep_thinking = self.input_json["is_deep_thinking"]
        await self.get_conversation_history()
        await self.update_user_history()
        self.save_log('输入',self.question)    
    
    
    async def get_conversation_history(self):
        """根据conversation_id获取对话历史"""
        
        if self.conversation_id:
            redis_db = RedisDB()
            await redis_db.connect()
            try:
                self.conversation_all = await redis_db.get_history_all(f"conversation:{self.conversation_id}")
                self.history_message = [{k: v for k, v in json.loads(item).items() if k in ['role', 'content']} for item in self.conversation_all]
                self.history_question = [item['content'] for item in self.history_message if item['role'] == 'user']
            finally:
                await redis_db.close()
        else:
            self.conversation_id = str(uuid.uuid4())
    
    
    async def update_user_history(self):
        redis_db = RedisDB()
        await redis_db.connect()
        try:
            user_history = await redis_db.get_history_all(f"user:{self.user_id}")
            if user_history and any(json.loads(item)["conversation_id"] == self.conversation_id for item in user_history):
                return 
            user_history_message = {"user_id": self.user_id, "conversation_id": self.conversation_id, "content": self.question, "time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), "order": len(user_history)+1}
            await redis_db.insert_conversation_message(f'user:{self.user_id}', json.dumps(user_history_message,ensure_ascii=False))
        except Exception as e:
            print(e)
        finally:
            await redis_db.close()


    async def update_conversation_history(self):
        redis_db = RedisDB()
        await redis_db.connect()
        try:
            
            user_message = {"user_id": self.user_id, "conversation_id": self.conversation_id, "role": "user", "content": self.question, "time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), "order": len(self.conversation_all)+1}
            assistant_message = {"user_id": self.user_id, "conversation_id": self.conversation_id, "message_id": self.message_id, "role": "assistant", "thinking_content": self.thinking_content, "content": self.final_answer, "charts": self.charts, "thinking_enabled": self.is_deep_thinking, "time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), "order": len(self.conversation_all)+2}
            await redis_db.insert_conversation_message(f'conversation:{self.conversation_id}', json.dumps(user_message,ensure_ascii=False))
            await redis_db.insert_conversation_message(f'conversation:{self.conversation_id}', json.dumps(assistant_message,ensure_ascii=False))
            self.history_question.append(self.question)
        except Exception as e:
            print('更新对话历史异常捕获', e)
        finally:
            await redis_db.close()
    
    
    def save_log(self, log_type,log_content,time_cost=0):
        """记录日志

        Args:
            log_type (String): 日志类型
            log_content (Any): 日志记录内容
        """
        log_content = process_log(log_content)
        sx_log.debug(f"ip：{MACHINE_IP} - client：{self.client} - 用户userId：{self.user_id} - 问题：{process_log(self.question)} - 日志类型：{log_type} - 日志内容：{log_content} - 处理时间：{time_cost}")
    
    
    async def format_sse_message(self, content, content_type, ended=False):
        """给前端返回的内容

        Args:
            content (string): 返回内容
            content_type (string): 返回类型
            ended (bool): 是否结束流式返回
        """
        return {"conversation_id": self.conversation_id, "message_id": self.message_id, "content": content, "type": content_type, "ended": ended}
    

    async def ordered_stream(self, task_functions):
        # 创建动态任务列表和队列
        tasks = []
        queues = []
        for task_func in task_functions:
            q = asyncio.Queue()
            tasks.append(asyncio.create_task(task_func(q)))
            queues.append(q)

        # 统一处理所有队列
        for queue in queues:
            while True:
                chunk = await queue.get()
                if chunk is None:  # 检测到任务结束
                    break
                yield chunk
                self.thinking_content += chunk['content']

        # 确保所有任务完成
        for task in tasks:
            await task


    async def get_external_information(self, queue: asyncio.Queue):
        if self.is_deep_thinking:
            self.thinking_content += '\n\n**联网搜索**\n'
            await queue.put(await self.format_sse_message('\n\n**联网搜索**\n', content_type="thinking"))
        web_contents = await get_web_contents_batch(self.decompose_questions, count=1)
        self.formatted_web_contents = format_web_contents_batch(web_contents)
        web_contents_return = format_output_web_contents_batch(web_contents[0:3])
        
        self.save_log('新流程-查询到的网页内容', web_contents)
        if self.is_deep_thinking:
            self.thinking_content += f"{web_contents_return}\n"
            await queue.put(await self.format_sse_message(f"{web_contents_return}\n", content_type="thinking"))
        await queue.put(None)  # 结束标记
        
