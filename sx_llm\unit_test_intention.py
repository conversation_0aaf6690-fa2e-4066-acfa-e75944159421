import csv
from tqdm import tqdm
import os
import json
import re
import sys
import time
import requests
sys.path.append('D:/code/gpt/warroom_llm/sx_llm/')
from http import HTTPStatus
import dashscope

parent_dir = os.path.dirname(os.path.abspath(__file__))
file = os.path.join(parent_dir, 'tests', 'data', 'auto_test_intention_data.csv')


def get_access_token():
    """
    使用 API Key，Secret Key 获取access_token，替换下列示例中的应用API Key、应用Secret Key
    """
        
    # url = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=4UeHb4PaemCIaEQGFUthzgGE&client_secret=ppDaYf0WYZjAUlBjoA3L8ebEs8Khqpqw"
    url = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=fW1PBg9AVBXSUdFigdNdsdtt&client_secret=UCy0s691b118gKGdZoGL9cwEDEPyK0nv"

    payload = json.dumps("")
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    
    response = requests.request("POST", url, headers=headers, data=payload)
    return response.json().get("access_token")


def wenxin_request(prompt):
        
    # url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro?access_token=" + get_access_token()
    url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-4.0-8k-0329?access_token=" + get_access_token()
    # url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-4.0-8k-0104?access_token=" + get_access_token()
    
    payload = json.dumps({
        "messages": [{"role": "user", "content": prompt}],
        "stream": False,
        "temperature": 0.01,
    })
    headers = {
        'Content-Type': 'application/json'
    }
    t1 = time.time()
    response = requests.request("POST", url, headers=headers, data=payload, stream=False)

    response_json = json.loads(response.text)['result']
    return response_json



def call_with_messages(content):
    messages = [{'role': 'user', 'content': content}]
    response = dashscope.Generation.call(
        # model='llama3-70b-instruct',
        model='qwen1.5-110b-chat',
        temperature = 0,
        messages=messages,
        result_format='text',  
        api_key = 'sk-tDdROccVi2'
    )
    if response.status_code == HTTPStatus.OK:
        return response['output']['text']
    else:
        print('Request id: %s, Status code: %s, error code: %s, error message: %s' % (
            response.request_id, response.status_code,
            response.code, response.message
        ))

def extract_json_from_markdown(input_str):
    # 替换中文引号或其他非ASCII引号为英文双引号
    input_str = input_str.replace("“", "\"").replace("”", "\"")

    # 替换字符 \xa0 为普通空格
    input_str = input_str.replace("\xa0", " ")

    # 替换换行符为空
    input_str = input_str.replace("\n", "")
    # 替换布尔值为字符串
    if "\"False\"" not in input_str and "\"True\"" not in input_str:
        input_str = input_str.replace("False", "\"False\"")
        input_str = input_str.replace("True", "\"True\"")

    # 正则表达式匹配Markdown中的JSON部分
    pattern = r'```json(.*?)```'
    match = re.search(pattern, input_str, re.IGNORECASE | re.DOTALL)
    
    if match is not None:
        # 提取JSON字符串
        json_str = match.group(1)

        try:
            json_obj = json.loads(json_str)
            return json_obj
        except json.JSONDecodeError:
            pass
    
    input_str = input_str.replace("\"{", "{")
    input_str = input_str.replace("}\"", "}")
    input_str = input_str.replace("\\", "")


    # 尝试直接解析整个输入字符串为JSON
    try:
        json_obj = json.loads(input_str)
        if isinstance(json_obj, dict):
            return json_obj
        if isinstance(json_obj, str):
            # 如果是字符串尝试再解析一次
            json_obj = json.loads(json_obj)
            return json_obj
    except json.JSONDecodeError:
        pass

    # 正则表达式匹配大括号里的内容并尝试解析为JSON
    pattern = r'{.*?}'
    matches = re.findall(pattern, input_str)
    for match in matches:
        try:
            json_obj = json.loads(match)
            return json_obj
        except json.JSONDecodeError:
            pass

    return None



kpi_all = ["上险（Insurance）", "市占（Market Share or M.S.）", "转化（Conversion）", "漏斗（Funnel）", "实时数据", "销量（Volume）", "销售表现",
           "客源数（Leads）", "潜客数（NPC）", "展厅客流数（Showroom Traffic）", "到店数", "试乘试驾数（Test Drive）", "订单数（NCO）", "交车数", "发票数（Invoice）", "零售发票数（Retail Invoice）",
           "批售数（Whole sale）", "含大客户发票数（Invoice KA Included）", "库存",
           "库存数（Stock）", "经销商库存数（Dealer Stock）", "总部库存数（HQ Stock）", "库存当量（Stock Factor）", "总部库存当量（HQ Stock Factor）", "经销商存数当量（Dealer Stock Factor）",
           "线索转化率（LTO）", "线索到店率（Leads to ST）", "试乘试驾率（ST to TD）", "到店转化率（ST to NCO）", "订单成交率（NCO to Invoice）", 
           "客源目标（Leads Target）","客源任务完成（Leads Achieved）","客源进度",
           "潜客目标（NPC Target）","潜客任务完成（NPC Achieved）","潜客进度",
           "展厅客流目标（Showroom Traffic Target）","展厅客流任务完成（Showroom Traffic Achieved）","展厅客流进度","到店目标","到店任务完成","到店进度",
           "试乘试驾目标（Test Drive Target）","试乘试驾任务完成（Test Drive Achieved）","试乘试驾进度",
           "订单目标（NCO Target）","订单任务完成（NCO Achieved）","订单进度",
           "发票目标（Invoice Target）","发票任务完成（Invoice Achieved）","发票进度",
           "交车目标","交车任务完成","交车进度",
           "销量目标","销量任务完成","销量进度",
           "含大客户发票目标（Invoice KA Included Target）","含大客户发票任务完成（Invoice KA Included Achieved）","含大客户发票进度",
           "零售发票目标（Retail Invoice Target）","零售发票任务完成（Retail Invoice Achieved）","零售发票进度",
           "批售目标（Whole sale Target）","批售任务完成（Whole sale Achieved）","批售进度"]

kpi_all = "\"" + "\", \"".join(kpi_all) + "\""


# todo：修改数据查询
prompt = """[要求]
1.以文本格式返回下方内容，结果不包含分析过程或解释或说明。
2.[指标]：{kpi_all}
3.文本的内容按如下方式生成：
3.1.[语言]：识别用户输入的语言，包括"英文"和"中文"。
3.2.[意图]：识别用户输入的意图，包括：
3.2.1.数据查询：如果用户输入涉及指标结果查询，请返回"数据查询"。
3.2.2.数据分析：如果涉及数据结果原因分析或对数据的进一步提问，包括哪一个(which)，哪些(which ones)，谁(who)，什么时间(when)，请返回"数据分析"。
3.2.3.页面交互：如果用户输入明确需要进行"时间筛选"、"页面返回"、"页面关闭"、"页面退出"，请返回"页面交互"。
3.2.4.如果数据查询不包含指标，或不是上述三种意图的其中一种，请返回"未知意图"。
3.3.[查询指标]：如果意图为"数据查询"或"数据分析"，识别查询的指标，并返回指标完整名称，多值字段, 用中括号 list 结构保存，例如["订单数（NCO）"],["销量（Volume）","市占（Market Share or M.S.）"]; 如果信息不存在, 请使用 [] 作为值。

[用户输入]：{current_question}

[语言]：
[意图]：
[查询指标]："""








if __name__ == '__main__':
    results = []
    # 打开CSV文件
    with open(file, 'r', encoding='utf-8') as f:
        # 创建CSV读取器
        csv_reader = csv.reader(f, delimiter='\t')
        # 逐行读取CSV内容
        for row in tqdm(csv_reader):
            print(row)
            try:
                if row == ['', ''] or row == []:
                    continue
                question,answer = row

                # today = 'April 26, 2024'
                # today = '2024年5月7日'
                # content = prompt.format(question=question, today=today)
                content = prompt.format(kpi_all=kpi_all, current_question=question)
                print(content)
                # llama3调用
                # response_json = call_with_messages(content)
                # 文心调用
                response_json = wenxin_request(content)
                format_json = extract_json_from_markdown(response_json)

                data1 = format_json
                data2 = json.loads(answer)
                inconsistent_keys = []
                for k in data1.keys():
                    v1 = data1[k]
                    v2 = data2[k]
                    if k in ["model","location"]:
                        v1.sort()
                        v2.sort()
                    
                    if v1 != v2:
                        inconsistent_keys.append(k)

                if inconsistent_keys == []:
                    is_equal = True
                else:
                    is_equal = False
                results.append([question,answer,format_json,is_equal,inconsistent_keys])
            except Exception as e:
                print(row)
            
            # time.sleep(60)
    print(results) 
    answer_type = 'intention'
    output_file = os.path.join(parent_dir, 'tests', 'data', 'unit_test_'+ answer_type +'_result_'+'wenxin0329_原prompt'+'-20240507'+'V1'+'.xlsx')
    import pandas as pd 
    result_df = pd.DataFrame(results)
    result_df.columns = ["问题","标准答案","生成答案","是否相等","不相等的字段"]
    print(result_df)
    result_df.to_excel(output_file,index=None)



