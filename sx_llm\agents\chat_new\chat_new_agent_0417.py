import time
import json
import asyncio
import traceback

from utils.llm import llm_dict
from utils.sx_dict import *
from utils.sx_prompt import kpi_all
from utils.sx_date import get_date_info
from utils.clean_json import extract_json_new
from functions.warroom_data_process import get_cleaned_json_list, format_cleaned_json_list, remove_empty_from_list, filter_warroom_data_for_llm
from functions.web_search import get_web_contents_batch, format_web_contents_batch, format_output_web_contents_batch
from agents.base_agent import BaseAgent
from agents.chat_new.prompt import *
from agents.data_analyse.data_analyse_agent import DataAnalyseAgent



class ChatNewAgent(BaseAgent):
    def __init__(self, agent: BaseAgent = None):
        super().__init__()  # 调用父类初始化
        self.agent = agent
        self.input_json = None
        self.user_id = None
        self.conversation_id = None
        self.question = None
        self.is_internet_search = False
        self.language = '中文'
        self.intention = None
        self.kpi = None
        self.input_time = time.time()
        self.history_message = []
        self.history_question = []
        self.conversation_all = []
        self.analyse_llm = llm_dict['deepseekr1stream']
        self.process_llm = llm_dict['deepseekv3stream']
        self.process_llm_0324 = llm_dict['deepseekv3stream0324']
        self.decompose_questions = None
        self.answer_src = sx_answer_source[self.language]["LLM"]
        self.formatted_web_contents = None
        self.date_info = get_date_info()
        self.today = self.date_info['today_date_cn']
        self.weekday = self.date_info['today_weekday']
        self.json_list = None
        self.warroom_data = None
        self.is_data_question = None
        self.analysis_result = None
        self.message_id = None
                

    async def classify_question(self):
        # 问题分类
        prompt = prompt_question_classification_template.format(current_question = self.question, history_question = self.history_question)
        content = [{"role": "user","content": prompt}]
        response = await self.process_llm.async_chat_by_token(content)
        result = ''
        async for chunk in response:
            content = chunk.choices[0].delta.content
            result += content
        self.is_data_question = result
        self.save_log('新流程-问题分类结果', self.is_data_question)

    
    async def decompose_question(self):
        if not self.is_deep_thinking:
            self.decompose_questions = [self.question]
            return
        # 分解用户输入的问题
        history_question = '\n'.join([str(index)+'.'+q for index, q in enumerate(self.history_question, start=1)])
        prompt = prompt_query_decomposition_add_history_template.format(current_question = self.question, history_question = history_question)
        content = [{"role": "user","content": prompt}]
        response = await self.process_llm.async_chat_by_token(content)
        
        is_thinking = True
        
        result = ''
        all_result = ''
        
        t1 = time.time()
        is_delete = False
        
        async for chunk in response:

            content = chunk.choices[0].delta.content
            content = content.replace('\n\n','\n')
            all_result += content
            
            
            if '<' in content:
                content_prefix = content.split('<')[0]
                is_delete = True
                self.thinking_content += content_prefix
                content_suffix = '<' + content.split('<')[-1]
                yield await self.format_sse_message(content=content_prefix, content_type="thinking")
                yield await self.format_sse_message(content='\n<delete>\n', content_type="thinking")
                yield await self.format_sse_message(content=content_suffix, content_type="thinking")
            elif '>' in content:
                is_thinking = False
                content_suffix = content.split(">")[-1]
                self.thinking_content += content_suffix
                result += content_suffix
                yield await self.format_sse_message(content=content, content_type="thinking")
            else:
                if not is_delete:
                    self.thinking_content += content
                yield await self.format_sse_message(content=content, content_type="thinking")
                if not is_thinking:
                    result += content
        
        t2 = time.time()
        self.save_log('新流程- 分解问题回答',result,time_cost=t2-t1)
          
        yield await self.format_sse_message(content='\n</delete>\n', content_type="thinking")
        self.thinking_content += '\n\n**问题分解**\n'
        yield await self.format_sse_message(content='\n\n**问题分解**\n', content_type="thinking")
        
        self.decompose_questions = extract_json_new(result)['查询问题']
        # 增加原始问题到分解问题列表中
        self.decompose_questions.append(self.question)
        questions_return = '\n'.join(self.decompose_questions)
        yield await self.format_sse_message(content=f'{questions_return}\n', content_type="thinking")
        self.thinking_content += f'{questions_return}\n'
        
        self.save_log('新流程- 添加了原始问题后的问题列表',self.decompose_questions,time_cost=t2-t1)
        
        self.save_log('新流程- 分解问题- 完整回答',all_result,time_cost=t2-t1)


    async def get_question_json_list(self, queue: asyncio.Queue):
        prompt = prompt_internal_query_json_template.format(question_list=json.dumps(self.decompose_questions,ensure_ascii=False), today=self.today, weekday=self.weekday, kpi_all=kpi_all)
        self.save_log('新流程- 内部查询问题转化为json的prompt',prompt)
        content = [{"role": "user","content": prompt}]
        response = await self.process_llm.async_chat_by_token(content)
        await queue.put('<delete>')
        result = ''
        async for chunk in response:
            content = chunk.choices[0].delta.content
            await queue.put(content)
            result += content
        await queue.put('</delete>')
        self.save_log('新流程- 内部查询问题转化为json的结果',result)
        if 'json_list' in result:
            self.json_list = json.loads(result)['json_list']
            self.json_list = remove_empty_from_list(self.json_list)


    async def get_question_json_list_non_queue(self):
        prompt = prompt_internal_query_json_template.format(question_list=json.dumps(self.decompose_questions,ensure_ascii=False), today=self.today, weekday=self.weekday, kpi_all=kpi_all)
        self.save_log('新流程- 内部查询问题转化为json的prompt',prompt)
        content = [{"role": "user","content": prompt}]
        response = await self.process_llm.async_chat_by_token(content)
        yield await self.format_sse_message(content='<delete>', content_type="thinking")
        result = ''
        async for chunk in response:
            content = chunk.choices[0].delta.content
            yield await self.format_sse_message(content=content, content_type="thinking")
            result += content
        yield await self.format_sse_message(content='</delete>', content_type="thinking")
        self.save_log('新流程- 内部查询问题转化为json的结果',result)
        if 'json_list' in result:
            self.json_list = json.loads(result)['json_list']
            self.json_list = remove_empty_from_list(self.json_list)


    async def get_internal_information(self, queue: asyncio.Queue):
        await queue.put('\n\n**内部数据检索**\n')
        await queue.put('销售数据查询：\n')
        await self.get_question_json_list(queue)
        
        if self.json_list:
        
            self.save_log('新流程- 从问题列表提取到的JSON列表', self.json_list)
            
            cleaned_json_list = get_cleaned_json_list(self.json_list)
            self.save_log('新流程- 清洗后的json列表', cleaned_json_list)
            
            content = format_cleaned_json_list(cleaned_json_list)
            
            await queue.put(f"{content}\n")
            self.thinking_content += f"{content}\n"
            
            t3 = time.time()
            self.warroom_data = await DataAnalyseAgent.data_query_new(cleaned_json_list)
            t4 = time.time()
            
            
            self.save_log('新流程-清洗后限制数量的内部查询JSON列表', cleaned_json_list)
            self.save_log('新流程-内部数据查询调用接口查询数据', self.warroom_data, t4 - t3)
            
            await queue.put(None)  # 结束标记


    async def get_internal_information_non_queue(self):
        if self.is_deep_thinking:
            yield await self.format_sse_message('\n\n**内部数据检索**\n', content_type="thinking")
            self.thinking_content += '\n\n**内部数据检索**\n'
            yield await self.format_sse_message('销售数据查询：\n', content_type="thinking")
            self.thinking_content += '销售数据查询：\n'
        async for chunk in self.get_question_json_list_non_queue():
            if self.is_deep_thinking:
                yield chunk
        
        self.save_log('新流程- 从问题列表提取到的JSON列表', self.json_list)
        
        if self.json_list:
        
            cleaned_json_list = get_cleaned_json_list(self.json_list)
            self.save_log('新流程- 清洗后的json列表', cleaned_json_list)
            
            content = format_cleaned_json_list(cleaned_json_list)
            if self.is_deep_thinking:
                yield await self.format_sse_message(f"{content}\n", content_type="thinking")
                self.thinking_content += f"{content}\n"

            self.save_log('新流程-清洗后限制数量的内部查询JSON列表', cleaned_json_list)
            
            t3 = time.time()
            self.warroom_data = await DataAnalyseAgent.data_query_new(cleaned_json_list)
            t4 = time.time()
            self.save_log('新流程-内部数据查询调用接口查询数据', self.warroom_data, t4 - t3)


    async def get_external_information(self, queue: asyncio.Queue):
        await queue.put('\n\n**联网搜索**\n')
        web_contents = await get_web_contents_batch(self.decompose_questions, count=1)
        self.formatted_web_contents = format_web_contents_batch(web_contents)
        web_contents_return = format_output_web_contents_batch(web_contents[0:3])
        
        self.save_log('新流程-查询到的网页内容', web_contents)
        await queue.put(f"{web_contents_return}\n")

        await queue.put(None)  # 结束标记


    async def get_external_information_non_queue(self):
        if self.is_deep_thinking:
            self.thinking_content += '\n\n**联网搜索**\n'
            yield await self.format_sse_message('\n\n**联网搜索**\n', content_type="thinking")
        web_contents = await get_web_contents_batch(self.decompose_questions, count=1)
        self.formatted_web_contents = format_web_contents_batch(web_contents)
        web_contents_return = format_output_web_contents_batch(web_contents[0:3])
        
        self.save_log('新流程-查询到的网页内容', web_contents)
        if self.is_deep_thinking:
            self.thinking_content += f"{web_contents_return}\n"
            yield await self.format_sse_message(f"{web_contents_return}\n", content_type="thinking")


    # 流式生成器：按顺序发送A→B的结果
    async def ordered_stream(self):
        queue_a, queue_b = asyncio.Queue(), asyncio.Queue()
        
        # 并行启动两个任务
        task_a = asyncio.create_task(self.get_external_information(queue_a))
        task_b = asyncio.create_task(self.get_internal_information(queue_b))
        
        
        # 第一阶段：发送任务A的结果
        while True:
            chunk = await queue_a.get()
            if chunk is None:  # 检测到任务A结束
                break
            if self.is_deep_thinking:
                self.thinking_content += chunk
                yield await self.format_sse_message(content=chunk, content_type="thinking")
                
        
        # 第二阶段：发送任务B的结果
        while True:
            chunk = await queue_b.get()
            if chunk is None:  # 检测到任务B结束
                break
            if self.is_deep_thinking:
                self.thinking_content += chunk
                yield await self.format_sse_message(content=chunk, content_type="thinking")
        
        # 确保任务完成（异常处理）
        await task_a
        await task_b


    async def get_analysis_result(self):
        if self.is_deep_thinking:
            self.thinking_content += '\n\n**整合分析**\n'
            yield await self.format_sse_message(content='\n\n**整合分析**\n', content_type="thinking")
        if self.warroom_data:
            filtered_warroom_data = filter_warroom_data_for_llm(self.warroom_data)
        else:
            filtered_warroom_data = None
        prompt = prompt_data_analysis_template.format(question=self.question, warroom_data=filtered_warroom_data, web_contents=self.formatted_web_contents, today=self.today)
        self.save_log('新流程-数据分析prompt',prompt)
        content = {"role": "user","content": prompt}
        self.history_message.append(content)
        if self.is_deep_thinking:
            response = await self.analyse_llm.async_chat_by_token(self.history_message)
            is_thinking = True
            async for chunk in response:
                # 火山云流式输出
                if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
                    content = chunk.choices[0].delta.reasoning_content
                    content = content.replace('\n\n', '\n')
                    self.thinking_content += content
                    yield await self.format_sse_message(content, content_type="thinking")
                else:
                    if is_thinking:
                        is_thinking = False
                    content = chunk.choices[0].delta.content
                    content = content.replace('\n\n', '\n')
                    self.final_answer += content
                    yield await self.format_sse_message(content, content_type="text")
        else:
            response = await self.process_llm_0324.async_chat_by_token(self.history_message)
            result = ''
            async for chunk in response:
                content = chunk.choices[0].delta.content
                yield await self.format_sse_message(content=content, content_type="text")
                result += content
                self.final_answer += content
        if self.is_data_question == '是':
            # yield await self.format_sse_message('\n', content_type="text")
            # self.final_answer += '\n'
            # filtered_warroom_data = filter_warroom_data_for_llm(self.warroom_data)
            # prompt = prompt_mermaid_template.format(data=filtered_warroom_data)
            # self.save_log('新流程-生成mermaid语法prompt',prompt)
            # content = [{"role": "user","content": prompt}]
            # response = await self.process_llm_0324.async_chat_by_token(content)
            # mermaid_result = ''
            # async for chunk in response:
            #     content = chunk.choices[0].delta.content
            #     yield await self.format_sse_message(content=content, content_type="text")
            #     self.final_answer += content
            #     mermaid_result += content
            # self.save_log('新流程-mermaid图生成结果',mermaid_result)
            if self.warroom_data:
                for data in self.warroom_data:
                    if data['type'] == 'chart' or data['type'] == 'table':
                        if data not in self.charts:
                            yield await self.format_sse_message(data, content_type="chart", ended=False)
                            self.charts.append(data)
                self.save_log('新流程-沙盘图生成结果', self.charts)
        
        yield await self.format_sse_message('', content_type="text", ended=True)
        

    async def event_stream(self):
        try:
            await self.classify_question()
            async for item in self.decompose_question():
                yield f'data: {json.dumps(item,ensure_ascii=False)}\n\n'
            if self.is_data_question == '是':
                if not self.is_deep_thinking:
                    item = await self.format_sse_message(content="相关数据检索中... ...\n", content_type="text")
                    yield f'data: {json.dumps(item,ensure_ascii=False)}\n\n'
                if self.is_internet_search:
                    async for item in self.ordered_stream():
                        yield f'data: {json.dumps(item,ensure_ascii=False)}\n\n'
                else:
                    async for item in self.get_internal_information_non_queue():
                        yield f'data: {json.dumps(item,ensure_ascii=False)}\n\n'
                async for item in self.get_analysis_result():
                    yield f'data: {json.dumps(item,ensure_ascii=False)}\n\n'
            else:
                if self.is_internet_search:
                    async for item in self.get_external_information_non_queue():
                        yield f'data: {json.dumps(item,ensure_ascii=False)}\n\n'
                async for item in self.get_analysis_result():
                    yield f'data: {json.dumps(item,ensure_ascii=False)}\n\n'
            await self.update_conversation_history()
        except asyncio.CancelledError:
            print("捕获到 CancelledError，开始清理")
            try:
                # 使用 shield 防止清理被取消
                await asyncio.shield(self.update_conversation_history())
            except Exception as e:
                e_msg = traceback.format_exc()
                self.save_log('异常捕获',e_msg)
                print('发生异常：',e_msg )
            print("客户端已断开连接")
            raise  # 重新抛出以便框架处理
        except Exception as e:
            e_msg = traceback.format_exc()
            self.save_log('异常捕获',e_msg)
            yield await self.format_sse_message('对不起，由于问题过于复杂，我暂时无法回答', content_type="text", ended=True)
            print('发生异常：',e_msg )
        finally:
            print("SSE返回已结束")



if __name__ == '__main__':
    agent = ChatNewAgent()
    agent.question = '去年上汽大众的总销量是多少？'
    asyncio.run(agent.decompose_question())
    