import json
import datetime


insurance = ["销量", "同比", "环比", "市占"]
conversion = ["客源", "潜客", "到店", "试乘试驾", "订单", "发票", "线索到店转化率", "试乘试驾转化率", "到店转化率", "LTO转化率", "订单成交转化率"]

def input_format(formatted_json):
    formatted_json = json.loads(formatted_json)
    realtime = formatted_json["realtime"]
    if realtime == "True":
        formatted_json["intention"] = ['转化']
        intention = '转化'
    else:
        intention = formatted_json["intention"]
        if [i for i in intention if i in insurance] == []:
            formatted_json["intention"] = ['转化']
            intention = '转化'
        else:
            formatted_json["intention"] = ['上险']
            intention = '上险'
    if formatted_json["start_time"] == "":
        today = datetime.date.today()
        if intention == '转化':
            month = today.strftime("%Y%m")
        elif intention == '上险':
            month = today.replace(month=today.month-1)
            month = month.strftime("%Y%m")
        formatted_json["start_time"] = month
        formatted_json["end_time"] = month

    return json.dumps(formatted_json, ensure_ascii=False),intention


if __name__ == "__main__":
    formatted_json = '{"start_time": "", "end_time": "", "model_name_cn": [], "rssc_cn": [], "intention": ["客源"], "realtime": "True"}'
    print(input_format(formatted_json))
    formatted_json = '{"start_time": "", "end_time": "", "model_name_cn": [], "rssc_cn": [], "intention": ["销量"], "realtime": "False"}'
    print(input_format(formatted_json))
