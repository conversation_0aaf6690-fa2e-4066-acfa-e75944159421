import re
import json
import calendar
from abc import ABC, abstractmethod

# from sx_index import vector_index
# from config import *
# from sx_date import *
# from sx_dict import *
from sx_data_load import *
from utils.sx_data_load import *
from utils.sx_dict import *
from utils.config import *
from utils.sx_index import vector_index
from utils.sx_date import *
from utils.wrappers import *


# 读取该文件的父节点路径
parent_dir = '..'


class JsonCleaner:
    @staticmethod
    def create_json_cleaner(intention):
        if intention in ['data query', 'data analysis']:
            return DataQueryJsonCleaner()
        # elif intention == 'UI':
        #     return UIJsonCleaner()
        else:
            raise ValueError("Invalid intention")

class JsonCleaner(ABC):
    @abstractmethod
    def clean_json(self, json_obj: str, kpi: list) -> dict:
        pass
    
    @abstractmethod
    def _clean_each_para(self, json_obj: dict) -> None:
        pass
    
    @abstractmethod
    def _clean_all_paras(self, kpi: list) -> dict:
        pass


class DataQueryJsonCleaner(JsonCleaner):

    def __init__(self):
        self.date_info = get_date_info()
        self.this_month = date_info['this_month']
        self.today_date_str = date_info['today_date_str']
        self.last_month = date_info['last_month']
        self.cleaned_json = {}


    def clean_json(self, json_obj: str, kpi: list) -> dict:
        self._clean_each_para(json_obj)
        return self._clean_all_paras(kpi)

        
    def _clean_each_para(self, json_obj: dict) -> str:
        self.cleaned_json['today'] = clean_today(json_obj.get("today", ""))
        self.cleaned_json['display'] = clean_display(json_obj.get("display", ""))
        self.cleaned_json['data_dim'] = clean_data_dim(json_obj.get("data_dim", ""))
        self.cleaned_json['model'] = clean_model(json_obj.get("model", ""))
        self.cleaned_json['location'] = clean_location(json_obj.get("location", ""))
        self.cleaned_json['start_time'] = clean_time(json_obj.get("start_time", ""))
        self.cleaned_json['end_time'] = clean_time(json_obj.get("end_time", ""))
        self.cleaned_json['date_type'] = clean_date_type(json_obj.get("date_type", ""))
        self.cleaned_json['not_total'] = clean_not_total(json_obj.get("not_total", ""))
        self.cleaned_json['template'] = clean_template(json_obj.get("template", ""))
        

    def _clean_all_paras(self, kpi):
        # 1.提升查询效率，如果时间范围不对，进行相应转化：日期不大于当前月份，上险小于当前月份；如有需要可以改成交互式
        # 2.上险查询：周和月；转化查询：日和月
        
        # 不同关键词的联合清洗
        today = self.cleaned_json['today']
        display = self.cleaned_json['display']
        data_dim = self.cleaned_json['data_dim']
        model = self.cleaned_json['model']
        location = self.cleaned_json['location']
        start_time = self.cleaned_json['start_time']
        end_time = self.cleaned_json['end_time']
        date_type = self.cleaned_json['date_type']
        not_total = self.cleaned_json['not_total']
        template = self.cleaned_json['template']
        # start_time和end_time的联合清洗
        if end_time < start_time:
            end_time = start_time
        if len(start_time) < 6:
            start_time = min(start_time[0:4] + '01', self.this_month)
        if len(end_time) < 6:
            end_time = min(end_time[0:4] + '12',self.this_month)
        # 如果按维度查询，则为分布查询
        if display == '趋势' and data_dim != 'all':
            display = '分布'
        # 如果维度为all，则为趋势
        elif display == '分布' and data_dim == 'all':
            display = '趋势'
        # 如果model、location都只有一个值，data_dim也为all，则排名转为趋势
        elif display == '排名' and data_dim == 'all' and len(model) == 1 and len(location) == 1:
            display = '趋势'
        
        # 如果车型中包含竞品车型，则将kpi转为销量
        if len(set(model) - set(SVW_VW_model_list)) > 0:
            kpi = list(set(['销量' if tmp_kpi not in ['销量','市占'] else tmp_kpi for tmp_kpi in kpi]))
        if template != '':
            kpi = list(set(['销量' if tmp_kpi not in ['销量','市占'] else tmp_kpi for tmp_kpi in kpi]))
            model = [template_id_to_name.get(template, '')]

        if date_type == 'month' and start_time[0:6] == end_time[0:6]:
            not_total = 'False'

        if today == "True":
            start_time = self.this_month
            end_time = self.this_month
            if kpi == ["转化"]:
                display = "漏斗"
                if data_dim == 'all':
                    date_type = "day"
                else:
                    date_type = "month"
            else:
                kpi = [today_kpi_name_dict[i] for i in kpi if i in today_kpi_name_dict.keys()]
                display = "目标"
                date_type = "month"
        else:
            if "市占" in kpi or "销量" in kpi:
                # 以查询导向为基础，根据查询意图，查询相应内容
                latest_date = get_warroom_latest_date()
                if start_time[0:6] == self.this_month:
                    if date_type == 'month':
                        # 查询本月月度数据，均使用沙盘最新日期作为end_time
                        if latest_date['lastDataDateByDate'] < self.this_month + "01":
                            start_time = self.last_month + '01'
                        else:
                            start_time = start_time[0:6] + "01"
                        if display == "趋势":
                            date_type = "week"
                        else:
                            date_type = "day"
                    else:
                        date_type = "week"
                        start_time = min(start_time,latest_date['lastDataDateByDate'])
                    end_time = min(end_time,latest_date['lastDataDateByDate'])
                    if week_cal(start_time) == week_cal(end_time) and date_type == "week" and display == "趋势":
                        display = '同环比'
                else:
                    if date_type == 'month':
                        end_time = min(end_time,latest_date['lastDataDateByMonth'])
                        start_time = min(start_time,latest_date['lastDataDateByMonth'])
                    else:
                        # 上险日度数据查询转为周度
                        date_type = 'week'
                        end_time = min(end_time,latest_date['lastDataDateByDate'])
                        start_time = min(start_time,latest_date['lastDataDateByDate'])
                    # 如果是单月或者单周查询，转为表格查询
                    if (end_time[0:6] == start_time[0:6] and date_type == "month") or (week_cal(start_time) == week_cal(end_time) and date_type == "week"):
                        if display == "趋势":
                            display = '同环比'
            elif "库存" in kpi[0]:
                start_time = start_time[0:6]
                end_time = end_time[0:6]
                date_type = "month"
                data_dim = 'all'
                if display == '同环比':
                    display = '趋势'
                if start_time == end_time and display == '趋势':
                    display = '同环比'

            elif "转化" in kpi:
                display = '漏斗'
            elif '目标' in kpi[0]:
                display = '目标'
            else:
                # 查询单月转化数据，返回每天数据
                if start_time[0:6] == end_time[0:6] and date_type == 'month' and display == "趋势" and data_dim == 'all':
                    start_time = start_time[0:6]
                    end_time = end_time[0:6]
                    date_type = 'day'
                
            start_time,end_time = date_format(date_type,start_time,end_time)
            if display in ['同环比','趋势']:
                kpi_temp = []
                for i in kpi:
                    kpi_temp = kpi_temp + kpi_mappings[i]
                kpi = list(set(kpi_temp))

        
        if kpi == []:
            cleaned_json = {}
        else:
            cleaned_json = {
                "time": start_time + '-' + end_time,
                "start_time": start_time,
                "end_time": end_time,
                "date_type": date_type,
                "model": model,
                "location": location,
                "kpi": kpi,
                "display": display,
                "today":today,
                "data_dim":data_dim,
                "not_total":not_total,
                "template_id":template
                }
        return json.dumps(cleaned_json, ensure_ascii=False)








def clean_time_list(time_list):
    if time_list == []:
        return [' - ']
    else:
        time_list = [t + '-' + t if '-' not in t else t for t in time_list]
        return time_list


def string_to_json(json_string, max_attempts=2):
    result = json_string
    for _ in range(max_attempts):
        try:
            result = json.loads(result)
            if not isinstance(result, str):
                return result
        except json.JSONDecodeError:
            break
    raise ValueError("Invalid JSON string provided or conversion failed")


def single_kpi_json_process(kpi, format_json, json_sets):
    """对单个kpi的json进行处理，返回处理后的json列表
    """
    single_kpi_json_list = []
    for t in format_json['time']:
        format_json['start_time'],format_json['end_time'] = t.split('-')
        format_json_clean = clean_json(format_json,[kpi])
        if format_json_clean == '{}' or format_json_clean in json_sets:
            continue
        json_sets.add(format_json_clean)
        single_kpi_json_list.append({'json_origin':format_json,'json_clean':format_json_clean})
    
    if not single_kpi_json_list:
        return None
        
    first_json = json.loads(single_kpi_json_list[0]['json_clean'])
    return {
        'kpi': kpi,
        'display': first_json['display'],
        'today': first_json['today'],
        'json_list': single_kpi_json_list
    }


@print_input_output
@time_logger
def extract_json_from_markdown(input_str):
    """清洗字符串json

    Args:
        input_str (String): llm识别到的json

    Returns:
        String: 清洗后的json，字符串类型
    """

    # 移除字符串最外层的双引号
    input_str = input_str.strip('"')

    # 替换中文引号或其他非ASCII引号为英文双引号
    input_str = input_str.replace("“", "\"").replace("”", "\"")

    # 替换字符 \xa0 为普通空格
    input_str = input_str.replace("\xa0", " ")

    # 替换换行符为空
    input_str = input_str.replace("\n", "")
    # 替换布尔值为字符串
    if "\"False\"" not in input_str and "\"True\"" not in input_str and "\'False\'" not in input_str and "\'True\'" not in input_str:
        input_str = input_str.replace("False", "\"False\"")
        input_str = input_str.replace("True", "\"True\"")

    # 正则表达式匹配Markdown中的JSON部分
    pattern = r'```json(.*?)```'
    match = re.search(pattern, input_str, re.IGNORECASE | re.DOTALL)
    
    if match is not None:
        # 提取JSON字符串
        json_str = match.group(1)

        try:
            json_obj = json.loads(json_str)
            return json_obj
        except json.JSONDecodeError:
            pass
    
    input_str = input_str.replace("\"{", "{")
    input_str = input_str.replace("}\"", "}")
    input_str = input_str.replace("\\", "")
    input_str = input_str.replace("'", "\"")


    # 尝试直接解析整个输入字符串为JSON
    try:
        json_obj = json.loads(input_str)
        if isinstance(json_obj, dict):
            return json_obj
        if isinstance(json_obj, str):
            # 如果是字符串尝试再解析一次
            json_obj = json.loads(json_obj)
            return json_obj
    except json.JSONDecodeError:
        pass

    # 正则表达式匹配大括号里的内容并尝试解析为JSON
    pattern = r'{.*?}'
    matches = re.findall(pattern, input_str)
    for match in matches:
        try:
            json_obj = json.loads(match)
            return json_obj
        except json.JSONDecodeError:
            pass

    return None


def extract_digits(text: str) -> str:
    """提取字符串中的数值部分

    Args:
        text (String): 需要提取数值的字符串

    Returns:
        String: 字符串中的数值部分
    """
    if not text:
        return ''
    digits = re.findall(r'\d+', text)
    return digits[0] if digits else ''



@print_input_output
def is_valid_date(date_string):
    """判断字符串是否为日期

    Args:
        date_string (String): 待验证是否为日期的字符串

    Returns:
        Bool: 判断结果
    """
    formats = ['%Y', '%Y%m', '%Y%m%d']  
    for format in formats:  
        try:  
            datetime.datetime.strptime(date_string, format)  
            return True  
        except ValueError:  
            pass  
    return False 


@print_input_output
def clean_time(t: str) -> str:
    date_info = get_date_info()
    today_date_str = date_info['today_date_str']
    t_digits = extract_digits(t)
    if not t_digits or not is_valid_date(t_digits) or not t_digits.isdigit() or t_digits > today_date_str:
        t_digits = today_date_str
    return t_digits



# 地理位置清洗，判断地理位置维表是否存在于location中
# 依次从城市、省份、大区的顺序进行匹配，匹配不到，则匹配下一级；否则停止匹配
# 全国、各大区、各省份、各城市等关键词需要进行转换
# 默认为Nationwide（全国）
@print_input_output
def clean_location(location):
    if location == [''] or location == []:
        return ["Nationwide"]
    
    location_map = set()
    
    for i in location:
        if '全国' in location or 'Nationwide' in location:
            location_map.add('Nationwide')
            continue
        tmp_city = [tmp for tmp in data_city if tmp.replace('市','') in i.replace('吉林省','')]
        if len(tmp_city) > 1:
            tmp_city.remove('吉林市')
        if len(tmp_city) > 0:
            location_map.update(tmp_city)
            continue
        tmp_province = [tmp for tmp in data_province if tmp.replace('省','') in i]
        if len(tmp_province) > 0:
            location_map.update(tmp_province)
            continue
        tmp_rssc = [tmp.replace('大区','') for tmp in data_rssc if tmp.replace('大区','') in i]
        if len(tmp_rssc) > 0:
            location_map.update(tmp_rssc)
            continue
    
    location_map = list(location_map)
    if location_map == []:
        return ["Nationwide"]
    return location_map




@print_input_output
def clean_model(model: list) -> list:
    if isinstance(model, list):
        if not model:
            return ["SVW-VW"]
        model = [i.replace('各车型','') for i in model if i.replace('各车型','') != '']
        if model == []:
            return ["SVW-VW"]
        model = [vector_index(model_vector_file_path,i,1)[1][0] for i in model]
        if model == []:
            return ["SVW-VW"]
        return list(set([model_en_to_cn.get(i, i) for i in model]))
    elif isinstance(model, str):
        model = [vector_index(model_vector_file_path,model,1)[1][0]]
        if model == []:
                return ["SVW-VW"]
        return list(set([model_en_to_cn.get(i, i) for i in model]))
    else:
        return ["SVW-VW"]



@print_input_output
def clean_kpi(kpi):
    if isinstance(kpi, list):
        cleaned_kpi = []
        if '销售表现' in kpi:
            cleaned_kpi = ['转化','销量']
        for i in kpi:
            kpi_temp = i.split('（')[0]
            if kpi_temp in kpi_name_dict.keys():
                cleaned_kpi.append(kpi_name_dict[kpi_temp])
        cleaned_kpi = list(set(cleaned_kpi))
    else:
        cleaned_kpi = []
    return cleaned_kpi


@print_input_output
def clean_display(display: str) -> str:

    # 20241101 TODO：如果为其他类型，是否需要特殊处理
    if not isinstance(display, str) or not display:
        return "趋势"
    else:
        matched_values = (valid_display_dict[key] for key in valid_display_dict if key in display.lower())
    
        # 如果有匹配的值，返回第一个匹配的值，否则返回None
        display = next(matched_values, None)
        return display if display else "分布"


@print_input_output
def clean_today(today: str) -> str:

    return today if today == "True" else "False"



@print_input_output
def clean_date_type(date_type: str) -> str:
    """清洗时间类型

    Args:
        date_type (String): 问题中提取的时间类型

    Returns:
        String: 清洗后的时间类型
    """
    # 目前，除week和day，其余均为month
    if date_type != "week" and date_type != "day":
        date_type = "month"
    return date_type


@print_input_output
def clean_data_dim(data_dim: str) -> str:

    # 20241101 TODO：如果为其他类型，是否需要特殊处理
    if isinstance(data_dim, str):
        if data_dim in ["SVR","location","district","province","city"]:
            return "location"
        if data_dim == "model":
            return "model"
    return "all"


@print_input_output
def clean_not_total(not_total: str) -> str:
    """清洗查询汇总值还是分月、周、日

    Args:
        not_total (String): 是否查询非汇总值

    Returns:
        String: 清洗后的结果
    """
    if not isinstance(not_total, str) or not_total != 'True':
        not_total = "False"
    return not_total


def week_cal(date_str):
    """将日期转换为周（含年）

    Args:
        date_str (String): 日期：202312, 20231201

    Returns:
        date_week (String): 周，例如：202311（2023年11周）
    """
    if len(date_str) == 4:
        date_object = datetime.datetime.strptime(date_str, "%Y")
    elif len(date_str) == 6:
        date_object = datetime.datetime.strptime(date_str, "%Y%m")
    elif len(date_str) == 8:
        date_object = datetime.datetime.strptime(date_str, "%Y%m%d")
    else:
        date_info = get_date_info()
        date_object = datetime.datetime.strptime(date_info['this_month'], "%Y%m")

    year = date_object.isocalendar()[0]
    week_number = date_object.isocalendar()[1]

    if week_number < 10:
        week_number = '0' + str(week_number)

    date_week = str(year) + str(week_number)
    return date_week


def date_format(date_type: str, start_time: str, end_time: str) -> list[str, str]:
    """根据要查询的时间粒度，转化开始和结束时间

    Args:
        date_type (String): 查询的时间粒度
        start_time (String): 开始时间
        end_time (String): 结束时间
    """
    date_info = get_date_info()
    today_date_str = date_info['today_date_str']

    if date_type == "day":
        if len(start_time) == 6:
            start_time = start_time + "01"
        elif len(start_time) == 4:
            start_time = start_time + "0101"
        if len(end_time) == 6:
            first_weekday, num_days = calendar.monthrange(int(end_time[0:4]), int(end_time[4:6]))
            end_time = min(end_time + str(num_days),today_date_str)
        elif len(end_time) == 4:
            end_time = min(end_time + "1231",today_date_str)
    elif date_type == "week":
        start_time = week_cal(start_time)
        end_time = week_cal(end_time)
    else:
        start_time = start_time[0:6]
        end_time = end_time[0:6]
    return start_time,end_time







@print_input_output
def clean_template(template: str) -> str:
    if isinstance(template,str):
        template = template.replace('模板','')
        if template:
            template = vector_index(template_vector_file_path,template,1)[1][0]
            template = template_name_to_id.get(template, '')
    else:
        template = ''
    return template



def clean_json_ui(json_obj):
    start_time = clean_time(json_obj.get("start_time", ""))[0:6]
    end_time = clean_time(json_obj.get("end_time", ""))[0:6]
    if end_time == "" or end_time < start_time:
        end_time = start_time
    action = json_obj.get("action", "")

    # 存在start_time
    if action == "返回":
        cleaned_json = {"start_time": "","end_time": "","action": action,}
    elif action == "筛选":
        if start_time != "":
            cleaned_json = {"start_time": start_time,"end_time": end_time,"action": action,}
        else:
            start_time = time.strftime('%Y',time.localtime())+'01'
            end_time = time.strftime('%Y%m',time.localtime())
            cleaned_json = {"start_time": start_time,"end_time": end_time,"action": action,}
    else:
        cleaned_json = {}
    return json.dumps(cleaned_json, ensure_ascii=False)


def extract_and_clean_json_ui(input_str):
    json_obj = extract_json_from_markdown(input_str)
    if isinstance(json_obj, dict):
        return clean_json_ui(json_obj)

    return json.dumps({}, ensure_ascii=False)

if __name__ == "__main__":
    # 输入的包含Markdown格式的字符串
    input_str = '"{\'time\': [], \'date_type\': \'\', \'model\': [], \'location\': [\'华东大区\'], \'display\': \'总计\', \'today\': \'False\', \'data_dim\': \'all\', \'not_total\': \'False\', \'template\': \'\'}"'
    # input_str = '```json\n{\n    "start_time": "202301",\n    "end_time": "202303",\n    "location": [],\n    "model": ["上汽大众大众品牌"],\n    "display": "",\n    "data_dim": "",\n    "graph": "折线图",\n    "today": False\n}\n```'
    kpi = ['含大客户发票数']
    json_obj = extract_json_from_markdown(input_str)
    # json_obj = clean_json_ui(json_obj)
    json_obj = extract_and_clean_json(input_str,kpi)
    
    print(json_obj)
    # location = ["吉林省四平市","辽宁省沈阳市"]
    # clean_location(location)
    # print(data_rssc)
    # print(clean_kpi(kpi))
    # kpi_temp = []
    # for i in kpi:
    #     kpi_temp = kpi_temp + kpi_mappings[i]
    #     print(kpi_temp)
    # kpi = kpi_temp
    # model = ["蔚来","上汽大众大众"]
    # model = clean_model(model)
    # print(model)
    # model = ['大众']
    # print(clean_model(model))
