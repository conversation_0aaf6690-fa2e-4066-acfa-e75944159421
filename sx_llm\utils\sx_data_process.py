

# from utils.sx_language_process import *
# from utils.sx_data_load import *
from sx_language_process import *
from sx_data_load import *


class WarroomDataProcessor:
    """沙盘数据处理：
    - 未知数据清洗
    - model处理
    - location处理
    - parentLocation处理
    """

    def __init__(self) -> None:
        pass

    def process_data(self, language, warroom_data):

        # 数据处理逻辑
        processed_data = [{**d,
                        "model": self._process_model_name(d["model"]),
                        'location':location_language_transform(d['location'], language, location_cn_to_en),
                        'parentLocation':location_language_transform(d['parentLocation'], language, location_cn_to_en)
                        } for d in warroom_data
                        if self._is_valid_data(d)]
        return processed_data

    def _is_valid_data(self, d):
            """检查数据是否有效"""
            return d["location"] != "未知" and d["model"] != "未知" and d["model"] is not None

    def _process_model_name(self, model):
        """车型名称处理，去除前缀

        Args:
            model (String): 车型名称

        Returns:
            String: 去除前缀后的车型名称
        """
        prefix_list = ["MAIN-","HIGH-","ID-","SK-"]
        for prefix in prefix_list:
            if model.startswith(prefix):
                model = model[len(prefix):]
                break
        return model
    
    def _process_compare_data(self, data, cleaned_json):
         """处理需要对比的数据，只保留时间范围的总计值"""
         single_request_data = []
         cleaned_json = json.loads(cleaned_json)
         for tmp_data in data:
            if tmp_data['time'] == 'total':
                if cleaned_json['start_time'] == cleaned_json['end_time']:
                    tmp_data['time'] = cleaned_json['start_time']
                else:
                    tmp_data['time'] = cleaned_json['start_time'] + '-' + cleaned_json['end_time']
                single_request_data.append(tmp_data)

         return single_request_data

# 沙盘数据只保留汇总值
for tmp_data in response_warroom:
    if tmp_data['time'] == 'total':
        if json.loads(tmp_json_clean)['start_time'] == json.loads(tmp_json_clean)['end_time']:
            tmp_data['time'] = json.loads(tmp_json_clean)['start_time']
        else:
            tmp_data['time'] = json.loads(tmp_json_clean)['start_time'] + '-' + json.loads(tmp_json_clean)['end_time']
        tmp_time_list = [json.loads(tmp_json_clean)['start_time'],json.loads(tmp_json_clean)['end_time'],json.loads(tmp_json_clean)['date_type']]
        if tmp_time_list not in time_list:
            time_list.append(tmp_time_list)
        warroom_data_list.append(tmp_data)




if __name__ == "__main__":
    from sx_request import *
    warroom_url = "https://sandbox-openapi.svw-volkswagen.com/robot/v3/open/adapter/vw/listCompetitionAnalyse"
    warroom_json = '{"start_time": "202408", "end_time": "202408", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["客源数"], "display": "分布", "today": "False", "data_dim": "model", "not_total": "False", "template_id": ""}'
    data = request_with_retries(warroom_url, warroom_json)
    processed_data = WarroomDataProcessor().process_data("中文", data)

    print(data)
    print('\n')
    print(processed_data)