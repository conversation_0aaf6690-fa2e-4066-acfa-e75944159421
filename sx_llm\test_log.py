async def get_warroom_data(i):

    language = '中文'
    tmp_kpi = i['kpi']
    tmp_display = i['display']
    tmp_today = i['today']

    response_kpi = []

    # 图表展示选择
    chart_type,type = chart_select(tmp_display)
    save_process_log('数据的图表类型chart_type和type',chart_type + ' ' + ' '.join(type))

    # 对比图放在一张图展示
    if tmp_display in ['对比','排名'] or (tmp_display == '漏斗' and tmp_today != "True"):
        save_process_log('多图展示类型','一次性展示')
        json_show = []
        warroom_data_list = []
        time_list = []
        for j in i['json_list']:
            tmp_json_clean = j['json_clean']
            tmp_kpi = json.loads(tmp_json_clean)['kpi'][0]

            # 沙盘查询json处理
            t1 = time.time()
            # 对比情况不需要转化，可以去掉？
            warroom_json = warroom_json_generate(tmp_json_clean)
            t2= time.time()
            save_process_log('调用数据查询接口的warroom_json',warroom_json,t2-t1)

            # 沙盘数据查询和处理
            t1 = time.time()
            response_warroom_no_clean = await async_request_with_retries(warroom_url, warroom_json)
            t2 = time.time()
            save_process_log('数据查询接口返回response_warroom_no_clean',response_warroom_no_clean,t2-t1)

            if response_warroom_no_clean == []:
                continue
            # 清洗沙盘接口查询的数据
            t1 = time.time()
            response_warroom = warroom_data_clean(response_warroom_no_clean)
            t2 = time.time()
            save_process_log('清洗后的数据查询接口返回response_warroom',response_warroom,t2-t1)

            tmp_json_clean_obj = json.loads(tmp_json_clean)
            start_time = tmp_json_clean_obj['start_time']
            end_time = tmp_json_clean_obj['end_time']
            date_type = tmp_json_clean_obj['date_type']
            
            # 沙盘数据只保留汇总值
            for tmp_data in response_warroom:
                if tmp_data['time'] == 'total':
                    if start_time == end_time:
                        tmp_data['time'] = start_time
                    else:
                        tmp_data['time'] = start_time + '-' + end_time
                    tmp_time_list = [start_time,end_time,date_type]
                    if tmp_time_list not in time_list:
                        time_list.append(tmp_time_list)
                    warroom_data_list.append(tmp_data)
        
        
        save_process_log('汇总后的数据查询结果warroom_data_list',warroom_data_list)
        if warroom_data_list == []:
            qa_answer = sx_warroom_response_reply[language].format(sx_kpi_name[tmp_kpi][language])
            text_show = [{"type": "text","text": {"content": qa_answer}}]
        else:
            # 输出数据格式转化
            json_show = content_show(tmp_json_clean,warroom_data_list,chart_type,type,language)
            save_process_log('json_show',json_show)

            if json_show == []:
                qa_answer = sx_warroom_response_reply[language].format(sx_kpi_name[tmp_kpi][language])
                text_show = [{"type": "text","text": {"content": qa_answer}}]
            else:
                qa_answer = answer_warroom_compare(time_list,tmp_kpi,language)
                text_show = [{"type": "text","text": {"content": qa_answer}}]
                response = response + text_show + json_show

    else:
        save_process_log('多图展示类型','分多次展示')
        for j in i['json_list']:
            tmp_json_clean = j['json_clean']
            tmp_json_clean_obj = json.loads(tmp_json_clean)
            # 如果存在多个指标，只保留数值类指标作为返回话术的指标显示
            if len(tmp_json_clean_obj['kpi']) > 1:
                tmp_kpi = [tmp for tmp in tmp_json_clean_obj['kpi'] if tmp not in not_main_kpi][0]
            else:
                tmp_kpi = tmp_json_clean_obj['kpi'][0]
            t1 = time.time()
            warroom_json = warroom_json_generate(tmp_json_clean)
            t2= time.time()
            save_process_log('调用数据查询接口的warroom_json',warroom_json,t2-t1)

            # 沙盘数据查询和处理
            t1 = time.time()
            response_warroom_no_clean = await async_request_with_retries(warroom_url, warroom_json)
            t2 = time.time()
            save_process_log('数据查询接口返回response_warroom_no_clean',response_warroom_no_clean,t2-t1)

            if response_warroom_no_clean == []:
                continue
            else:
                # 清洗沙盘接口查询的数据
                t1 = time.time()
                response_warroom = warroom_data_clean(response_warroom_no_clean)
                t2 = time.time()
                save_process_log('清洗后的数据查询接口返回response_warroom',response_warroom,t2-t1)
                if response_warroom == []:
                    continue

                # 输出数据格式转化
                json_show = content_show(tmp_json_clean,response_warroom,chart_type,type,language)
                # 如果查询数据为空
                if json_show == []:
                    continue
                # 如果查询不为空
                else:
                    qa_answer = answer_warroom(tmp_json_clean,tmp_kpi,language)
                    text_show = [{"type": "text","text": {"content": qa_answer}}]
                    response_kpi = response_kpi + text_show + json_show
                    response = response + text_show + json_show

    return response