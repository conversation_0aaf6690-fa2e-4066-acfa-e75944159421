import ast
import copy
import json
import asyncio
import aiohttp  # 推荐使用aiohttp替代requests

# from utils.config import *
from prompt import *
from api import *




import openai
# 阿里云私有化部署
async def chat_by_token(content: list):
    client = openai.AsyncOpenAI(
        api_key="MWJhM2Y2ZTQ3NThjNWY2Y2M0Mjk2M2U0YzhhMDE2ZTZiZWY0MjZmMQ==",
        base_url="http://1680606300477185.cn-shanghai.pai-eas.aliyuncs.com/api/predict/deepseek_svw_1_a.deepseek_svw_1/v1"
    )
    response = await client.chat.completions.create(
        model="DeepSeek-R1",
        messages=content,
        stream=True
    )
    return response


async def llm_chat(prompt, **kwargs):
    prompt_query_analysis = prompt.format(**kwargs)
    content = [{"role": "user","content": prompt_query_analysis}]
    response = await chat_by_token(content)

    ### deepseek模型流式输出
    result = ''
    is_thinking = True
    async for chunk in response:
        content = chunk.choices[0].delta.content
        if content == '<think>':
            print("⭐思考中⭐")
            continue
        else:
            if content == '</think>':
                is_thinking = False
                print("⭐最终回答⭐")
                continue
            else:
                if not is_thinking:
                    result += content
        print(content, end='')
    return result




coze_workflow_url = 'https://api.coze.cn/v1/workflow/run?='
# coze_api_key = COZE_API_KEY
workflow_id = '7470708539339489320'  # WEB_SEARCH工作流的workflow_id






async def get_web_contents(query: str, count: int = 3) -> dict:
    payload = json.dumps({
        "workflow_id": workflow_id,
        "parameters": {"query": query, "count": count}
    })
    
    headers = {
        "Authorization": f"Bearer {coze_api_key}",
        "Content-Type": "application/json"
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(coze_workflow_url, headers=headers, data=payload) as response:
            response.raise_for_status()
            result = await response.json()
            return json.loads(result['data'])['output']



class DataAnalyseAgent:
    
    @staticmethod
    async def get_internal_query_analysis_results(question: str) -> dict:
        return llm_chat(prompt_internal_query_json_template, question=question, today=today, weekday=weekday, kpi_all=kpi_all)
    
    def get_json_list(raw_json_str: str) -> list:
        raw_json = json.loads(raw_json_str)
        json_list = raw_json['json_list']
        return json_list

    def get_cleaned_json_list(json_list: list) -> list:
        cleaned_json_list = []
        for json_obj in json_list:
            kpi = json_obj['kpi']
            format_json_list = json_to_list(json_list, kpi)
            cleaned_json_list += format_json_list
        return cleaned_json_list




    
    @staticmethod
    async def get_query_analysis_results(question):
        return await llm_chat(prompt_query_analysis_template, question=question)
    
    @staticmethod
    def clean_json(raw_json_str):
        # 去除json中的空格，将字符串解析为json
        json_str = raw_json_str.replace(' ', '')
        print(json_str)
        json_obj = json.loads(json_str)
        return json_obj
    
    @staticmethod
    def get_search_queries(json_obj):
        search_queries = json_obj['查询问题']
        return search_queries
    
    @staticmethod
    def search_web_contents(search_queries):
        web_contents = []
        for query in search_queries:
            web_contents += get_web_contents(query)
        return web_contents
    
    @classmethod
    async def execute_full_flow(cls, question):
        # 获取查询分析结果
        analysis_results = await cls.get_query_analysis_results(question)
        
        # 清理并解析JSON
        clean_results = cls.clean_json(analysis_results)
        
        # 获取搜索查询
        search_queries = cls.get_search_queries(clean_results)
        
        
        # 创建异步任务列表（直接调用异步函数）
        tasks = [get_web_contents(q) for q in search_queries]
        
        # 并行执行所有异步请求
        results = await asyncio.gather(*tasks)
        
        # 扁平化结果列表
        return [item for sublist in results for item in sublist]
        
        
        # # 并行化网络请求
        # tasks = [asyncio.to_thread(get_web_contents, q) for q in search_queries]
        # results = await asyncio.gather(*tasks)
        # return [item for sublist in results for item in sublist]
        

        
        # # 搜索网络内容
        # web_contents = cls.search_web_contents(search_queries)
        
        # return web_contents
    
if __name__ == '__main__':
    # question = '分析去年途昂的销售转化情况'
    # result = DataAnalyseAgent.get_query_analysis_results(question)
    async def test_get_query_analysis_results():
        question = "查询本月途观L的销售情况"
        result = await DataAnalyseAgent.get_internal_query_analysis_results(question)
        print(f"结果: {result}")
        json_list = DataAnalyseAgent.get_json_list(result)
        print(f"json_list: {json_list}")
        cleaned_json_list = DataAnalyseAgent.get_cleaned_json_list(json_list)
        print(f"cleaned_json_list: {cleaned_json_list}")

    # 创建事件循环
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        # 运行测试函数
        loop.run_until_complete(test_get_query_analysis_results())
    finally:
        # 关闭事件循环
        loop.run_until_complete(loop.shutdown_asyncgens())
        loop.close()
    # print('\n---------------------------------------------------')
    # json_obj = DataAnalyseAgent.clean_json(result)
    # print(json_obj)
    # search_queries = DataAnalyseAgent.get_search_queries(json_obj)
    # print(search_queries)
    # search_queries = ['去年途昂的总销量和销售转化率具体数值是多少？', '去年途昂各季度/月份的销售转化率变化趋势如何？']
    # web_contents = DataAnalyseAgent.search_web_contents(search_queries)
    # print(web_contents)
    