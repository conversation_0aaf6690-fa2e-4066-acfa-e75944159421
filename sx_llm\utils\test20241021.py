import time
import base64
import hashlib

class KeyExpiredError(Exception):
    pass

class LicenseExpiredError(Exception):
    pass

class LicenseManager:
    def __init__(self, license_key, expiry_duration):
        self._license_key = license_key
        self._expiry_time = time.time() + expiry_duration
        self._check_license()

    def _check_license(self):
        if time.time() > self._expiry_time:
            raise LicenseExpiredError("License has expired.")

class KeyManager:
    def __init__(self, key_duration=3600):
        self._key_duration = key_duration
        self._key = None
        self._key_expiry_time = 0

    def _request_key(self):
        # 模拟密钥请求 (应该替换为实际的密钥获取实现)
        raw_key = hashlib.sha256(str(time.time()).encode()).digest()
        self._key = base64.urlsafe_b64encode(raw_key).decode()
        self._key_expiry_time = time.time() + self._key_duration

        # 清理内存中的原始密钥
        
        raw_key = b'\x00' * len(raw_key)

    def _get_key(self):
        if self._key is None or time.time() > self._key_expiry_time:
            self._request_key()
        return self._key

    # 禁止通过调试或打印输出获取密钥
    def __repr__(self):
        return "<KeyManager: Sensitive data>"

    def __str__(self):
        return "<KeyManager: Sensitive data>"

    def get_key_for_encryption(self):
        """获取加密用密钥，不返回实际密钥"""
        self._get_key()
        return "<encrypted_key>"  # 返回一个表示密钥存在的占位符，避免暴露实际密钥

class CryptoTool:
    def __init__(self, license_key, license_duration, key_duration=3600):
        self._license_manager = LicenseManager(license_key, license_duration)
        self._key_manager = KeyManager(key_duration)

    def encrypt(self, text):
        # 调用内部方法获取密钥，但不打印或返回密钥
        self._key_manager._get_key()
        # 在这里实现加密逻辑
        encrypted = f"encrypted({text})"
        return encrypted

    def decrypt(self, encrypted_text):
        # 调用内部方法获取密钥，但不打印或返回密钥
        self._key_manager._get_key()
        # 在这里实现解密逻辑
        decrypted = f"decrypted({encrypted_text})"
        return decrypted

    # 禁止调试时打印敏感信息
    def __repr__(self):
        return "<CryptoTool: Sensitive data>"

    def __str__(self):
        return "<CryptoTool: Sensitive data>"

# 示例用法
if __name__ == "__main__":
    try:
        license_key = "valid_license_key"  # 替换为实际的许可证密钥
        license_duration = 86400  # 许可证有效期1天
        crypto_tool = CryptoTool(license_key, license_duration)

        text = "Hello, World!"
        encrypted = crypto_tool.encrypt(text)
        print("Encrypted:", encrypted)

        decrypted = crypto_tool.decrypt(encrypted)
        print("Decrypted:", decrypted)

    except LicenseExpiredError as e:
        print(e)
