
from utils.sx_dict import sx_funnel_name, sx_kpi_name
from utils.chart_processors.base_chart_processor import BaseChartProcessor

class FunnelChartProcessor(BaseChartProcessor):
    """Funnel chart processor"""
    
    def __init__(self, format_json) -> None:
        super().__init__(format_json)
    
    def chart_process(self, data, language):
        if self.json_kpi != ["转化"]:
            return []
        if self.json_today == "True" and self.json_data_dim != "all":
            output_data_list = []
            for i in data:
                kpi_values = i['kpiValues']
                title_location = i["location"]
                title_model =  i["model"]
                title_kpi = sx_kpi_name["funnel"][language]
                title = title_location + " " + title_model + " " + title_kpi
                output_data =  {"leadsNotDuplicateCnt": kpi_values["todayLeadsNotDuplicateCnt"],
                                "leadsNotDuplicateCntName": sx_funnel_name["leadsNotDuplicateCnt"][language],
                                "deliveryEinvoiceNotSvkCnt": kpi_values["todayDeliveryEinvoiceNotSvkCnt"],
                                "deliveryEinvoiceNotSvkCntName": sx_funnel_name["deliveryEinvoiceNotSvkCnt"][language],
                                "orderCnt": kpi_values["todayOrderCnt"],
                                "orderCntName": sx_funnel_name["orderCnt"][language],
                                "oppTestdriveCnt": kpi_values["todayOppTestdriveCnt"],
                                "oppTestdriveCntName": sx_funnel_name["oppTestdriveCnt"][language],
                                "oppWalkinCnt": kpi_values["todayOppWalkinCnt"],
                                "oppWalkinCntName": sx_funnel_name["oppWalkinCnt"][language],
                                "oppCnt": kpi_values["todayOppCnt"],
                                "oppCntName": sx_funnel_name["oppCnt"][language],
                                "chartTitle": title
                                }
                output_data_list.append(output_data)
            return [{"type": "chart","chart": {"chart_type": "funnel","list": output_data_list}}]
        else:
            output_data_list = []
            for i in data:
                kpi_values = i['kpiValues']
                if all(int(value) == 0 for key, value in kpi_values.items() if 'target' not in key):
                    continue
                if self.json_today == "True" and i["time"] == 'total':
                    continue
                
                title_time = '' if self.json_today == "True" else i["time"]
                title_location = i["location"]
                title_model =  i["model"]
                title_kpi = sx_kpi_name["funnel"][language]
                title = title_time + " " + title_location + " " + title_model + " " + title_kpi
                output_data =  {"leadsNotDuplicateCnt": kpi_values["leadsNotDuplicateCnt"],
                                "leadsNotDuplicateCntName": sx_funnel_name["leadsNotDuplicateCnt"][language],
                                "deliveryEinvoiceNotSvkCnt": kpi_values["deliveryEinvoiceNotSvkCnt"],
                                "deliveryEinvoiceNotSvkCntName": sx_funnel_name["deliveryEinvoiceNotSvkCnt"][language],
                                "orderCnt": kpi_values["orderCnt"],
                                "orderCntName": sx_funnel_name["orderCnt"][language],
                                "oppTestdriveCnt": kpi_values["oppTestdriveCnt"],
                                "oppTestdriveCntName": sx_funnel_name["oppTestdriveCnt"][language],
                                "oppWalkinCnt": kpi_values["oppWalkinCnt"],
                                "oppWalkinCntName": sx_funnel_name["oppWalkinCnt"][language],
                                "oppCnt": kpi_values["oppCnt"],
                                "oppCntName": sx_funnel_name["oppCnt"][language],
                                "leadsTransferRate": kpi_values["leadsTransferRate"],
                                "leadsTransferRateName": sx_funnel_name["leadsTransferRate"][language],
                                "leadsArrivalRate": kpi_values["leadsArrivalRate"],
                                "leadsArrivalRateName": sx_funnel_name["leadsArrivalRate"][language],
                                "testdriveRate": kpi_values["testdriveRate"],
                                "testdriveRateName": sx_funnel_name["testdriveRate"][language],
                                "oppWalkinTransferRate": kpi_values["oppWalkinTransferRate"],
                                "oppWalkinTransferRateName": sx_funnel_name["oppWalkinTransferRate"][language],
                                "orderDeliveryNotSvkRate": kpi_values["orderDeliveryNotSvkRate"],
                                "orderDeliveryNotSvkRateName": sx_funnel_name["orderDeliveryNotSvkRate"][language],
                                "leadsNotDuplicateCntTarget": kpi_values["targetLeads"]["target"],
                                "leadsNotDuplicateCompleteRate": round(kpi_values["targetLeads"]["completionRate"] / 100, 4),
                                "oppCntTarget": kpi_values["targetOpportunity"]["target"],
                                "oppCompleteRate": round(kpi_values["targetOpportunity"]["completionRate"] / 100, 4),
                                "oppWalkinCntTarget": kpi_values["targetOppWalkin"]["target"],
                                "oppWalkinCompleteRate": round(kpi_values["targetOppWalkin"]["completionRate"] / 100, 4),
                                "oppTestdriveCntTarget": kpi_values["targetOppTestdrive"]["target"],
                                "oppTestdriveCompleteRate": round(kpi_values["targetOppTestdrive"]["completionRate"] / 100, 4),
                                "orderCntTarget": kpi_values["targetOrder"]["target"],
                                "orderCompleteRate": round(kpi_values["targetOrder"]["completionRate"] / 100, 4),
                                "deliveryEinvoiceNotSvkCntTarget": kpi_values["targetInvoice"]["target"],
                                "deliveryEinvoiceNotSvkCompleteRate": round(kpi_values["targetInvoice"]["completionRate"] / 100, 4),
                                "timeSchedule": round(kpi_values["targetLeads"]["timeSchedule"] / 100, 4),
                                "finishLegendName": sx_kpi_name["completion"][language],
                                "targetLegendName": sx_kpi_name["target"][language],
                                "timeLineLegendName": sx_kpi_name["time_schedule"][language],
                                "chartTitle": title
                                }
                output_data_list.append(output_data)
            return [{"type": "chart","chart": {"chart_type": "funnel","list": output_data_list}}]