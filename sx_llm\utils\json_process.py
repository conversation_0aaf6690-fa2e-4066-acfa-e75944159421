import re
import os
import json
from sx_index import vector_index
from config import *
from sx_date import *
from sx_dict import *
from wrappers import *
# from utils.sx_dict import *
# from utils.config import *
# from utils.sx_index import vector_index
# from utils.sx_date import *
# from utils.wrappers import *
import pandas as pd
import calendar


# 读取该文件的父节点路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

def string_to_json(json_string, max_attempts=2):
    result = json_string
    for _ in range(max_attempts):
        try:
            result = json.loads(result)
            if not isinstance(result, str):
                return result
        except json.JSONDecodeError:
            break
    raise ValueError("Invalid JSON string provided or conversion failed")




def single_kpi_json_process(kpi, format_json, json_sets):
    """对单个kpi的json进行处理，返回处理后的json列表
    """
    single_kpi_json_list = []
    for t in format_json['time']:
        format_json['start_time'],format_json['end_time'] = t.split('-')
        format_json_clean = clean_json(format_json,[kpi])
        if format_json_clean == '{}' or format_json_clean in json_sets:
            continue
        json_sets.add(format_json_clean)
        single_kpi_json_list.append({'json_origin':format_json,'json_clean':format_json_clean})
    
    if not single_kpi_json_list:
        return None
        
    first_json = json.loads(single_kpi_json_list[0]['json_clean'])
    return {
        'kpi': kpi,
        'display': first_json['display'],
        'today': first_json['today'],
        'json_list': single_kpi_json_list
    }



def clean_time_list(time_list):
    if time_list == []:
        return [' - ']
    else:
        time_list = [t + '-' + t if '-' not in t else t for t in time_list]
        return time_list





@print_input_output
def extract_json_from_markdown(input_str):
    """清洗字符串json

    Args:
        input_str (String): llm识别到的json

    Returns:
        String: 清洗后的json，字符串类型
    """


    # 移除字符串最外层的双引号
    input_str = input_str.strip('"')

    # 替换中文引号或其他非ASCII引号为英文双引号
    input_str = input_str.replace("“", "\"").replace("”", "\"")

    # 替换字符 \xa0 为普通空格
    input_str = input_str.replace("\xa0", " ")

    # 替换换行符为空
    input_str = input_str.replace("\n", "")
    # 替换布尔值为字符串
    if "\"False\"" not in input_str and "\"True\"" not in input_str and "\'False\'" not in input_str and "\'True\'" not in input_str:
        input_str = input_str.replace("False", "\"False\"")
        input_str = input_str.replace("True", "\"True\"")

    # 正则表达式匹配Markdown中的JSON部分
    pattern = r'```json(.*?)```'
    match = re.search(pattern, input_str, re.IGNORECASE | re.DOTALL)
    
    if match is not None:
        # 提取JSON字符串
        json_str = match.group(1)

        try:
            json_obj = string_to_json(json_str)
            json_obj = json.loads(json_str)
            return json_obj
        except json.JSONDecodeError:
            pass
    
    input_str = input_str.replace("\"{", "{")
    input_str = input_str.replace("}\"", "}")
    input_str = input_str.replace("\\", "")
    input_str = input_str.replace("'", "\"")


    # 尝试直接解析整个输入字符串为JSON
    try:
        json_obj = json.loads(input_str)
        if isinstance(json_obj, dict):
            return json_obj
        if isinstance(json_obj, str):
            # 如果是字符串尝试再解析一次
            json_obj = json.loads(json_obj)
            return json_obj
    except json.JSONDecodeError:
        pass

    # 正则表达式匹配大括号里的内容并尝试解析为JSON
    pattern = r'{.*?}'
    matches = re.findall(pattern, input_str)
    for match in matches:
        try:
            json_obj = json.loads(match)
            return json_obj
        except json.JSONDecodeError:
            pass

    return None


def extract_digits(text):
    """提取字符串中的数值部分

    Args:
        text (String): 需要提取数值的字符串

    Returns:
        String: 字符串中的数值部分
    """
    if not text:
        return ''
    digits = re.findall(r'\d+', text)
    return digits[0] if digits else ''



@print_input_output
def is_valid_date(date_string):
    """判断字符串是否为日期

    Args:
        date_string (String): 待验证是否为日期的字符串

    Returns:
        Bool: 判断结果
    """
    formats = ['%Y', '%Y%m', '%Y%m%d']  
    for format in formats:  
        try:  
            datetime.datetime.strptime(date_string, format)  
            return True  
        except ValueError:  
            pass  
    return False 


# 时间清洗
@print_input_output
def clean_time(t):
    """清洗单个时间

    Args:
        t (String): 待清洗的时间，默认为yyyymmdd格式

    Returns:
        String: 清洗后的时间，为yyyymmdd格式
    """
    date_info = get_date_info()
    today_date_str = date_info['today_date_str']
    t_digits = extract_digits(t)
    if not t_digits or not is_valid_date(t_digits) or not t_digits.isdigit():
        t_digits = today_date_str

    if t_digits > today_date_str:
        t_digits = today_date_str

    return t_digits

# 加载大区、省份、城市的维表
file_path_city = os.path.join(parent_dir, 'data', 'location_city_name.txt')
file_path_province = os.path.join(parent_dir, 'data', 'location_province_name.txt')
file_path_rssc = os.path.join(parent_dir, 'data', 'location_rssc_name.txt')
data_city = pd.read_csv(file_path_city,header=None)[0].tolist()
data_province = pd.read_csv(file_path_province,header=None)[0].tolist()
data_rssc = pd.read_csv(file_path_rssc,header=None,sep='\t')[0].tolist()
# mappings_rssc = {j[0]:j[1] for i,j in data_rssc.iterrows()}
# mappings_province = {j[0]:j[1] for i,j in data_province.iterrows()}
# mappings_city = {j[0]:j[1] for i,j in data_city.iterrows()}



# 地理位置清洗，判断地理位置维表是否存在于location中
# 依次从城市、省份、大区的顺序进行匹配，匹配不到，则匹配下一级；否则停止匹配
# 全国、各大区、各省份、各城市等关键词需要进行转换
# 默认为Nationwide（全国）
@print_input_output
def clean_location(location):
    if location == [''] or location == []:
        return ["Nationwide"]
    else:
        location_map = []
        for i in location:
            if '全国' in location or 'Nationwide' in location:
                location_map.append('Nationwide')
                continue
            tmp_city = [tmp for tmp in data_city if tmp.replace('市','') in i.replace('吉林省','')]
            if len(tmp_city) > 1:
                tmp_city.remove('吉林市')
            if len(tmp_city) > 0:
                location_map = location_map + tmp_city
                continue
            tmp_province = [tmp for tmp in data_province if tmp.replace('省','') in i]
            if len(tmp_province) > 0:
                location_map = location_map + tmp_province
                continue
            tmp_rssc = [tmp.replace('大区','') for tmp in data_rssc if tmp.replace('大区','') in i]
            if len(tmp_rssc) > 0:
                location_map = location_map + tmp_rssc
                continue
        if location_map == []:
            location_map = ["Nationwide"]
        return list(set(location_map))


# 名称映射
def name_mappings(file_path):
    mappings = {}
    with open(file_path, 'r', encoding='utf-8') as file:
        for line in file:
            parts = line.strip().split('\t')
            if len(parts) == 2:
                mappings[parts[1]] = parts[0]
    return mappings

# 车辆品牌清洗
# @print_input_output
# def clean_brand(brand):
#     if brand == [''] or brand == "" or brand == []:
#         return ["SVW-VW"]
#     parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
#     data_file_path = os.path.join(parent_dir, 'data', 'brand_name_cn.tsv')
#     mappings = name_mappings(data_file_path)
#     file_path = os.path.join(parent_dir, 'vector_store', 'brand_index')
#     brand_name_index = [vector_index(file_path,i)[1][0] for i in brand]
#     if brand_name_index == []:
#         return ["SVW-VW"]
#     return [mappings.get(i, i) for i in brand_name_index]


file_path_model_name = os.path.join(parent_dir, 'data', 'model_name_cn.tsv')
file_path_model_index = os.path.join(parent_dir, 'vector_store', 'model_index')
model_mappings = name_mappings(file_path_model_name)

@print_input_output
def clean_model(model):
    """车型清洗

    Args:
        model (List): 问题提取的车型

    Returns:
        List: 清洗后的车型
    """
    if isinstance(model, list):
        if not model:
            return ["SVW-VW"]
        else:
            model = [i.replace('各车型','') for i in model if i.replace('各车型','') != '']
            if model == []:
                return ["SVW-VW"]
            model = [vector_index(file_path_model_index,i,1)[1][0] for i in model]
            if model == []:
                return ["SVW-VW"]
            return list(set([model_mappings.get(i, i) for i in model]))
    elif isinstance(model, str):
        model = [vector_index(file_path_model_index,model,1)[1][0]]
        if model == []:
                return ["SVW-VW"]
        return list(set([model_mappings.get(i, i) for i in model]))
    else:
        return ["SVW-VW"]




kpi_name_dict = {"上险":"销量","市占":"市占",
                 "日均客源数":"本期日均客源数", "日均潜客数":"本期日均潜客数", "日均展厅客流数":"本期日均展厅客流数", "日均到店数":"本期日均展厅客流数", 
                 "日均试乘试驾数":"本期日均试乘试驾数", "日均订单数":"本期日均订单数", "日均零售发票数":"本期日均发票数",
                 "客源数":"客源数","潜客数":"潜客数","展厅客流数":"展厅客流数","到店数":"展厅客流数","试乘试驾数":"试乘试驾数","订单数":"订单数",
                 "零售发票数":"发票数",
                 "发票数":"含大客户发票数","交车数":"含大客户发票数","销量":"含大客户发票数","含大客户发票数":"含大客户发票数",
                 "库存":"总库存","库存数":"总库存","经销商库存数":"总库存","库存当量":"总库存当量","经销商库存当量":"总库存当量",
                 "总部库存数":"总部库存","总部库存当量":"总部库存当量",
                 "批售数":"批售数", "日均批售数":"日均批售数",
                 "日均交车数":"日均含大客户发票数", "日均销量":"日均含大客户发票数", "日均发票数":"日均含大客户发票数", "日均含大客户发票数":"日均含大客户发票数", 
                 "线索转化率":"线索转化率","线索到店率":"线索到店率","试乘试驾率":"试乘试驾率","到店转化率":"到店转化率","订单成交率":"订单成交率",
                 "客源目标":"客源目标","客源完成":"客源目标","客源任务完成":"客源目标","客源进度":"客源目标",
                 "潜客目标":"潜客目标","潜客完成":"潜客目标","潜客任务完成":"潜客目标","潜客进度":"潜客目标",
                 "展厅客流目标":"展厅客流目标","展厅客流完成":"展厅客流目标","展厅客流任务完成":"展厅客流目标","展厅客流进度":"展厅客流目标",
                 "到店目标":"展厅客流目标","到店完成":"展厅客流目标","到店任务完成":"展厅客流目标","到店进度":"展厅客流目标",
                 "试乘试驾目标":"试乘试驾目标","试乘试驾完成":"试乘试驾目标","试乘试驾任务完成":"试乘试驾目标","试乘试驾进度":"试乘试驾目标",
                 "订单目标":"订单目标","订单完成":"订单目标","订单任务完成":"订单目标","订单进度":"订单目标",
                 "发票目标":"含大客户发票目标","发票完成":"含大客户发票目标","发票任务完成":"含大客户发票目标","发票进度":"含大客户发票目标",
                 "交车目标":"含大客户发票目标","交车完成":"含大客户发票目标","交车任务完成":"含大客户发票目标","交车进度":"含大客户发票目标",
                 "销量目标":"含大客户发票目标","销量完成":"含大客户发票目标","销量任务完成":"含大客户发票目标","销量进度":"含大客户发票目标",
                 "含大客户发票目标":"含大客户发票目标","含大客户发票完成":"含大客户发票目标","含大客户发票任务完成":"含大客户发票目标","含大客户发票进度":"含大客户发票目标",
                 "零售发票目标":"发票目标","零售发票完成":"发票目标","零售发票任务完成":"发票目标","零售发票进度":"发票目标",
                 "批售目标":"批售目标","批售完成":"批售目标","批售任务完成":"批售目标","批售进度":"批售目标",
                 "转化":"转化","实时指标":"转化","转化漏斗":"转化","实时数据":"转化","漏斗":"转化"}


today_kpi_name_dict = {"客源数":"今日新增客源数","潜客数":"今日新增潜客数","展厅客流数":"今日新增到店数","试乘试驾数":"今日新增试乘试驾数",
                       "订单数":"今日新增订单数","含大客户发票数":"今日交车","发票数":"今日新增发票数"}


@print_input_output
def clean_kpi(kpi):
    """指标清洗

    Args:
        kpi (List): LLM提取到的指标

    Returns:
        List: 清洗后的指标
    """
    if isinstance(kpi, list):
        cleaned_kpi = []
        if '销售表现' in kpi:
            cleaned_kpi = ['转化','销量']
        for i in kpi:
            kpi_temp = i.split('（')[0]
            if kpi_temp in kpi_name_dict.keys():
                cleaned_kpi.append(kpi_name_dict[kpi_temp])
        cleaned_kpi = list(set(cleaned_kpi))
    else:
        cleaned_kpi = []
    return cleaned_kpi


@print_input_output
def clean_display(display):
    """清洗数据展示形式

    Args:
        display (String): 问题中的数据展示形式

    Returns:
        String: 清洗后的数据展示形式
    """
    if isinstance(display, str):
        if not display:
            display = "趋势"
        else:
            valid_dict = {"distribution":"分布","分布":"分布","趋势":"趋势","trend":"趋势","排名":"排名","rank":"排名",
                        "对比":"对比","comparison":"对比","漏斗":"漏斗","funnel":"漏斗","目标":"目标","target":"目标",
                        "进度":"目标","完成":"目标","schedule":"目标","completion":"目标",
                        "同比":"同环比","环比":"同环比","同环比":"同环比","mom":"同环比","yoy":"同环比",
                        "总计":"同环比"}
            display = "".join(list(set([valid_dict[val] for val in valid_dict.keys() if val in display.lower()])))
            if display == "":
                display = "分布"
    # 如果为list，是否需要单独处理
    else:
        display = "趋势"
    return display

# @print_input_output
# def clean_graph(graph):
#     valid_values = ["折线图","饼图","柱状图","表格","漏斗图"]
#     if not graph:
#         cleaned_graph = ""

#     cleaned_graph = "".join([val for val in valid_values if val in graph])
#     if cleaned_graph == "":
#         cleaned_graph = ""
#     return cleaned_graph

# 清洗today变量
@print_input_output
def clean_today(today):
    if today != "True":
        today = "False"
    return today


@print_input_output
def clean_date_type(date_type):
    """清洗时间类型

    Args:
        date_type (String): 问题中提取的时间类型

    Returns:
        String: 清洗后的时间类型
    """
    # 目前，除week和day，其余均为month
    if date_type != "week" and date_type != "day":
        date_type = "month"
    return date_type


@print_input_output
def clean_data_dim(data_dim):
    """清洗数据拆分维度

    Args:
        data_dim (String): 问题中的数据下钻维度

    Returns:
        String: 清洗后的数据下钻维度
    """
    if isinstance(data_dim, str):
        if data_dim in ["SVR","location","district","province","city"]:
            data_dim = "location"
        elif data_dim == "model":
            data_dim = "model"
        else:
            data_dim = "all"
    # 如果为list，是否需要单独处理
    else:
        data_dim = "all"
    return data_dim


@print_input_output
def clean_not_total(not_total):
    """清洗查询汇总值还是分月、周、日

    Args:
        not_total (String): 是否查询非汇总值

    Returns:
        String: 清洗后的结果
    """
    if isinstance(not_total, str):
        if not_total != 'True':
            not_total = "False"
    else:
        not_total = "False"
    return not_total




def week_cal(date_str):
    """将日期转换为周（含年）

    Args:
        date_str (String): 日期：202312, 20231201

    Returns:
        date_week (String): 周，例如：202311（2023年11周）
    """
    if len(date_str) == 4:
        date_object = datetime.datetime.strptime(date_str, "%Y")
    elif len(date_str) == 6:
        date_object = datetime.datetime.strptime(date_str, "%Y%m")
    elif len(date_str) == 8:
        date_object = datetime.datetime.strptime(date_str, "%Y%m%d")
    else:
        date_info = get_date_info()
        date_object = datetime.datetime.strptime(date_info['this_month'], "%Y%m")

    year = date_object.isocalendar()[0]
    week_number = date_object.isocalendar()[1]

    if week_number < 10:
        week_number = '0' + str(week_number)

    date_week = str(year) + str(week_number)
    return date_week


def date_process(date_type,start_time,end_time):
    """根据要查询的时间粒度，转化开始和结束时间

    Args:
        date_type (String): 查询的时间粒度
        start_time (String): 开始时间
        end_time (String): 结束时间
    """
    date_info = get_date_info()
    this_month_last_day = date_info['this_month_last_day']
    today_date_str = date_info['today_date_str']

    if date_type == "day":
        if len(start_time) == 6:
            start_time = start_time + "01"
        elif len(start_time) == 4:
            start_time = start_time + "0101"
        if len(end_time) == 6:
            first_weekday, num_days = calendar.monthrange(int(end_time[0:4]), int(end_time[4:6]))
            end_time = min(end_time + str(num_days),today_date_str)
        elif len(end_time) == 4:
            end_time = min(end_time + "1231",today_date_str)
    elif date_type == "week":
        # 如果周识别不准，扩大为对应月
        # start_time = start_time[0:6] + '01'
        # end_time = end_time[0:6] + str(calendar.monthrange(int(end_time[0:4]), int(end_time[4:6]))[1])
        start_time = week_cal(start_time)
        end_time = week_cal(end_time)
    elif date_type == "month":
        start_time = start_time[0:6]
        end_time = end_time[0:6]
    elif date_type == "year":
        start_time = start_time[0:4]
        end_time = end_time[0:4]
    else:
        start_time = start_time[0:4]
        end_time = end_time[0:4]
    return start_time,end_time


def template_name_to_id_mappings(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        mappings = {line.strip().split('\t')[1]:line.strip().split('\t')[0] for line in file}
    return mappings

def template_id_to_name_mappings(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        mappings = {line.strip().split('\t')[0]:line.strip().split('\t')[1] for line in file}
    return mappings

parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
template_file_path = os.path.join(parent_dir, 'data', 'template_name_id.tsv')
template_name_to_id = template_name_to_id_mappings(template_file_path)
template_id_to_name = template_id_to_name_mappings(template_file_path)
file_path_template_index = os.path.join(parent_dir, 'vector_store', 'template_index')

@print_input_output
def clean_template(template):
    if isinstance(template,str):
        template = template.replace('模板','')
        if template != '':
            template = vector_index(file_path_template_index,template,1)[1][0]
            template = template_name_to_id.get(template, '')
    else:
        template = ''
    return template


SVW_VW_model_list = ['Tiguan','Polo HB','Lavida','Lamando','Teramont','T-Cross','Phideon','ID.3','ID.4 X',
                     'ID.6 X','Tharu','Passat','Viloran','Touran','SVW-VW','ID series','KAROQ','KODIAQ','SUPERB',
                     'ID','HIGH','MAIN','SK','KAMIQ','OCTAVIA','RAPID']


# 1.提升查询效率，如果时间范围不对，进行相应转化：日期不大于当前月份，上险小于当前月份；如有需要可以改成交互式
# 2.上险查询：周和月；转化查询：日和月
def clean_json(json_obj,kpi):
    """对json的内容进行清洗

    Args:
        json_obj (Json): llm识别出的json
        kpi (List): 数据查询的指标

    Returns:
        String: 清洗内容后的json字符串
    """
    date_info = get_date_info()
    this_month = date_info['this_month']
    today_date_str = date_info['today_date_str']
    last_month = date_info['last_month']

    today = clean_today(json_obj.get("today", ""))
    display = clean_display(json_obj.get("display", ""))
    data_dim = clean_data_dim(json_obj.get("data_dim", ""))
    # kpi = list(set([clean_kpi(i) for i in kpi]))
    model = clean_model(json_obj.get("model", ""))
    location = clean_location(json_obj.get("location", ""))
    start_time = clean_time(json_obj.get("start_time", ""))
    end_time = clean_time(json_obj.get("end_time", ""))
    date_type = clean_date_type(json_obj.get("date_type", ""))
    not_total = clean_not_total(json_obj.get("not_total", ""))
    template = clean_template(json_obj.get("template", ""))

    # 不同关键词的联合清洗
    # start_time和end_time的联合清洗
    if end_time < start_time:
        end_time = start_time
    if len(start_time) < 6:
        start_time = min(start_time[0:4] + '01',this_month)
    if len(end_time) < 6:
        end_time = min(end_time[0:4] + '12',this_month)
    # 如果按维度查询，则为分布查询
    if display == '趋势' and data_dim != 'all':
        display = '分布'
    # 如果维度为all，则为趋势
    elif display == '分布' and data_dim == 'all':
        display = '趋势'
    # 如果model、location都只有一个值，data_dim也为all，则排名转为趋势
    elif display == '排名' and data_dim == 'all' and len(model) == 1 and len(location) == 1:
        display = '趋势'
    
    # 如果车型中包含竞品车型，则将kpi转为销量
    if len(set(model) - set(SVW_VW_model_list)) > 0:
        kpi = list(set(['销量' if tmp_kpi not in ['销量','市占'] else tmp_kpi for tmp_kpi in kpi]))
    if template != '':
        kpi = list(set(['销量' if tmp_kpi not in ['销量','市占'] else tmp_kpi for tmp_kpi in kpi]))
        model = [template_id_to_name.get(template, '')]

    if date_type == 'month' and start_time[0:6] == end_time[0:6]:
        not_total = 'False'

    if today == "True":
        start_time = this_month
        end_time = this_month
        if kpi == ["转化"]:
            display = "漏斗"
            if data_dim == 'all':
                date_type = "day"
            else:
                date_type = "month"
        else:
            kpi = [today_kpi_name_dict[i] for i in kpi if i in today_kpi_name_dict.keys()]
            display = "目标"
            date_type = "month"
    else:
        if "市占" in kpi or "销量" in kpi:
            # 以查询导向为基础，根据查询意图，查询相应内容
            latest_date = get_warroom_latest_date()
            print(latest_date)
            if start_time[0:6] == this_month:
                if date_type == 'month':
                    # 查询本月月度数据，均使用沙盘最新日期作为end_time
                    if latest_date['lastDataDateByDate'] < this_month + "01":
                        start_time = last_month + '01'
                    else:
                        start_time = start_time[0:6] + "01"
                    if display == "趋势":
                        date_type = "week"
                    else:
                        date_type = "day"
                else:
                    date_type = "week"
                    start_time = min(start_time,latest_date['lastDataDateByDate'])
                end_time = min(end_time,latest_date['lastDataDateByDate'])
                if week_cal(start_time) == week_cal(end_time) and date_type == "week" and display == "趋势":
                    display = '同环比'
            else:
                if date_type == 'month':
                    end_time = min(end_time,latest_date['lastDataDateByMonth'])
                    start_time = min(start_time,latest_date['lastDataDateByMonth'])
                else:
                    # 上险日度数据查询转为周度
                    date_type = 'week'
                    end_time = min(end_time,latest_date['lastDataDateByDate'])
                    start_time = min(start_time,latest_date['lastDataDateByDate'])
                # 如果是单月或者单周查询，转为表格查询
                if (end_time[0:6] == start_time[0:6] and date_type == "month") or (week_cal(start_time) == week_cal(end_time) and date_type == "week"):
                    if display == "趋势":
                        display = '同环比'
        elif "库存" in kpi[0]:
            start_time = start_time[0:6]
            end_time = end_time[0:6]
            date_type = "month"
            data_dim = 'all'
            if display == '同环比':
                display = '趋势'
            if start_time == end_time and display == '趋势':
                display = '同环比'

        elif "转化" in kpi:
            display = '漏斗'
        elif '目标' in kpi[0]:
            display = '目标'
        else:
            # 查询单月转化数据，返回每天数据
            if start_time[0:6] == end_time[0:6] and date_type == 'month' and display == "趋势" and data_dim == 'all':
                start_time = start_time[0:6]
                end_time = end_time[0:6]
                date_type = 'day'
            
        start_time,end_time = date_process(date_type,start_time,end_time)
        if display in ['同环比','趋势']:
            kpi_temp = []
            for i in kpi:
                kpi_temp = kpi_temp + kpi_mappings[i]
            kpi = list(set(kpi_temp))

    
    if kpi == []:
        cleaned_json = {}
    else:
        cleaned_json = {
            "time": start_time + '-' + end_time,
            "start_time": start_time,
            "end_time": end_time,
            "date_type": date_type,
            "model": model,
            "location": location,
            "kpi": kpi,
            "display": display,
            "today":today,
            "data_dim":data_dim,
            "not_total":not_total,
            "template_id":template
            }
    return json.dumps(cleaned_json, ensure_ascii=False)

def clean_json_ui(json_obj):
    start_time = clean_time(json_obj.get("start_time", ""))[0:6]
    end_time = clean_time(json_obj.get("end_time", ""))[0:6]
    if end_time == "" or end_time < start_time:
        end_time = start_time
    action = json_obj.get("action", "")

    # 存在start_time
    if action == "返回":
        cleaned_json = {"start_time": "","end_time": "","action": action,}
    elif action == "筛选":
        if start_time != "":
            cleaned_json = {"start_time": start_time,"end_time": end_time,"action": action,}
        else:
            start_time = time.strftime('%Y',time.localtime())+'01'
            end_time = time.strftime('%Y%m',time.localtime())
            cleaned_json = {"start_time": start_time,"end_time": end_time,"action": action,}
    else:
        cleaned_json = {}
    return json.dumps(cleaned_json, ensure_ascii=False)

# 清洗json提取关键词
def extract_and_clean_json(input_str,kpi):
    json_obj = extract_json_from_markdown(input_str)
    if isinstance(json_obj, dict):
        return clean_json(json_obj,kpi)

    return json.dumps({}, ensure_ascii=False)

def extract_and_clean_json_ui(input_str):
    json_obj = extract_json_from_markdown(input_str)
    if isinstance(json_obj, dict):
        return clean_json_ui(json_obj)

    return json.dumps({}, ensure_ascii=False)









if __name__ == "__main__":
    kpi = '销量'
    format_json = {'time': ['20240101-20241231'], 'date_type': 'year', 'model': [], 'location': ['华北大区'], 'display': '分布', 'today': 'False', 'data_dim': 'location', 'not_total': 'False', 'template': ''}
    json_sets = set()

    print(single_kpi_json_process(kpi, format_json, json_sets))