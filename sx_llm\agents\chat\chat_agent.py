import time
import json
import async<PERSON>
from fastapi import WebSocket

from utils.llm import llm_dict
from utils.sx_dict import *
from utils.sx_prompt import kpi_all
from utils.sx_date import get_date_info
from utils.sx_log import save_process_log, process_log, sx_log
from functions.warroom_data_process import get_cleaned_json_list
from functions.web_search import get_web_contents_batch, format_web_contents_batch, format_output_web_contents_batch
from agents.chat.prompt import *
from agents.data_analyse.data_analyse_agent import DataAnalyseAgent






class ChatAgent:
    def __init__(self, websocket: WebSocket):
        self.websocket = websocket
        self.input_json = None
        self.userId = None
        self.sessionId = None
        self.questionText = None
        self.category = None
        self.client = None
        self.language = '中文'
        self.input_text = None
        self.intention = None
        self.kpi = None
        self.input_time = time.time()
        self.history_json = None
        self.analyse_llm = llm_dict['deepseekr1stream']
        self.process_llm = llm_dict['deepseekv3stream']
        self.decompose_questions = None
        self.answer_src = sx_answer_source[self.language]["LLM"]
        self.formatted_web_contents = None
        self.date_info = get_date_info()
        self.today = self.date_info['today_date_cn']
        self.weekday = self.date_info['today_weekday']
        self.json_list = None
        self.warroom_data = None
        self.is_data_question = None
        
    async def get_input(self):
        self.input_json = await self.websocket.receive_json()
        self.input_time = time.time()
        self.userId = self.input_json["userId"]
        self.sessionId = self.input_json["sessionId"]
        self.questionText = self.input_json["questionText"]
        self.category = self.input_json.get("category", "")
        self.client = self.input_json.get("client", "")
        self.save_log('输入',self.questionText)


    async def classify_question(self):
        # 问题分类
        prompt = prompt_question_classification_template.format(question = self.questionText)
        content = [{"role": "user","content": prompt}]
        response = await self.process_llm.async_chat_by_token(content)
        result = ''
        async for chunk in response:
            content = chunk.choices[0].delta.content
            result += content
        self.is_data_question = result

    
    async def decompose_question(self):
        # 分解用户输入的问题
        prompt = prompt_query_decomposition_template.format(question = self.questionText)
        content = [{"role": "user","content": prompt}]
        response = await self.process_llm.async_chat_by_token(content)
        
        is_thinking = True
        
        result = ''
        all_result = ''
        
        t1 = time.time()
        
        is_print = True
        
        async for chunk in response:
            try:
                received_message = await asyncio.wait_for(self.websocket.receive_json(), timeout=0.1)
                if received_message["longTextStopFlag"]:
                    break
            except asyncio.TimeoutError:
                content = chunk.choices[0].delta.content
                content = content.replace('\n\n','\n')
                all_result += content
                if '>' in content:
                    is_thinking = False
                    content = content.split(">")[-1]
                if not is_thinking:
                    result += content
                if is_print:
                    if '<' in content:
                        is_print = False
                    content = content.split("<")[0]
                    content = content.replace('}','').replace('[','').replace(']','').replace('"','')
                    await self.ws_return_content(content=content, content_type="thinking")
                else:
                    if '{' in content:
                        is_print = True
                        await self.ws_return_content(content='\n\n**问题分解**\n', content_type="thinking")
                        content = content.split("{")[-1].replace('"','')
                        await self.ws_return_content(content=content, content_type="thinking")
                        
        # await self.ws_return_content(content='<h3 style="color: #8b8b8b;">问题分解</h3>\n\n', content_type="thinking")
        
        t2 = time.time()

        self.save_log('新流程- 分解问题',result,time_cost=t2-t1)
        
        self.decompose_questions = json.loads(result.strip())['查询问题']
        
        # questions_return = '\n'.join(self.decompose_questions)
        
        # await self.ws_return_content(content=f'{questions_return}\n', content_type="thinking")
        
        self.save_log('新流程- 分解问题- 完整回答',all_result,time_cost=t2-t1)


    async def get_question_json_list(self) -> list:
        prompt = prompt_internal_query_json_template.format(question_list=json.dumps(self.decompose_questions), today=self.today, weekday=self.weekday, kpi_all=kpi_all)
        content = [{"role": "user","content": prompt}]
        response = await self.process_llm.stream_response(content)
        self.json_list = json.loads(response)['json_list']


    async def get_internal_information(self, queue: asyncio.Queue):
        await queue.put('\n\n**内部数据检索**\n')
        await queue.put('销售数据查询：\n')
        await self.get_question_json_list()
        
        self.save_log('新流程- 从问题列表提取到的JSON列表', self.json_list)
        
        content = "\n".join(
            [
                f"{','.join(item['time'])} {','.join(item['location'])} {','.join(item['model'])} {','.join(item['kpi'])} {item['display']}"
                for item in self.json_list
            ]
        )
        
        await queue.put(f"{content}\n")
        
        cleaned_json_list = get_cleaned_json_list(self.json_list)
        
        self.save_log('新流程-清洗后限制数量的内部查询JSON列表', cleaned_json_list)
        
        t3 = time.time()
        self.warroom_data = await DataAnalyseAgent.data_query(cleaned_json_list)
        t4 = time.time()
        self.save_log('新流程-内部数据查询调用接口查询数据', self.warroom_data, t4 - t3)
        
        await queue.put(None)  # 结束标记


    async def get_external_information(self, queue: asyncio.Queue):
        await queue.put('\n\n**联网搜索**\n')
        web_contents = await get_web_contents_batch(self.decompose_questions)
        self.formatted_web_contents = format_web_contents_batch(web_contents)
        web_contents_return = format_output_web_contents_batch(web_contents[0:2])
        
        self.save_log('新流程-查询到的网页内容', web_contents)
        await queue.put(f"{web_contents_return}\n")

        await queue.put(None)  # 结束标记


    async def get_external_information_non_queue(self):
        await self.ws_return_content('\n\n**联网搜索**\n', content_type="thinking")
        web_contents = await get_web_contents_batch(self.decompose_questions)
        self.formatted_web_contents = format_web_contents_batch(web_contents)
        web_contents_return = format_output_web_contents_batch(web_contents[0:2])
        
        self.save_log('新流程-查询到的网页内容', web_contents)
        await self.ws_return_content(f"{web_contents_return}\n", content_type="thinking")


    # 流式生成器：按顺序发送A→B的结果
    async def ordered_stream(self):
        queue_a, queue_b = asyncio.Queue(), asyncio.Queue()
        
        # 并行启动两个任务
        task_a = asyncio.create_task(self.get_external_information(queue_a))
        task_b = asyncio.create_task(self.get_internal_information(queue_b))
        
        
        # 第一阶段：发送任务A的结果
        while True:
            chunk = await queue_a.get()
            if chunk is None:  # 检测到任务A结束
                break
            await self.ws_return_content(content=chunk, content_type="thinking")
        
        # 第二阶段：发送任务B的结果
        while True:
            chunk = await queue_b.get()
            if chunk is None:  # 检测到任务B结束
                break
            await self.ws_return_content(content=chunk, content_type="thinking")
        
        # 确保任务完成（异常处理）
        await task_a
        await task_b


    async def get_analysis_result(self):
        await self.ws_return_content(content='\n\n**整合分析**\n', content_type="thinking")
        prompt = prompt_data_analysis_template.format(question=self.questionText, warroom_data=self.warroom_data, web_contents=self.formatted_web_contents, today=self.today)
        self.save_log('新流程-数据分析prompt',prompt)
        content = [{"role": "user","content": prompt}]
        response = await self.analyse_llm.async_chat_by_token(content)
        is_thinking = True
        async for chunk in response:
            try:
                received_message = await asyncio.wait_for(self.websocket.receive_json(), timeout=0.1)
                if received_message["longTextStopFlag"]:
                    break
            except asyncio.TimeoutError:
                # 火山云流式输出
                if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
                    content = chunk.choices[0].delta.reasoning_content
                    content = content.replace('\n\n', '\n')
                    await self.ws_return_content(content, content_type="thinking")
                else:
                    if is_thinking:
                        is_thinking = False
                    content = chunk.choices[0].delta.content
                    content = content.replace('\n\n', '\n')
                    await self.ws_return_content(content, content_type="text")
        await self.ws_return_content('', content_type="text", ended=True)
        

    
    async def ws_return_content(self, content, content_type, ended=False):
        """给前端返回的内容

        Args:
            content (string): 返回内容
            content_type (string): 返回类型
            ended (bool): 是否结束流式返回
        """
        await self.websocket.send_json({"content": content, "type": content_type, "ended": ended})


    def save_log(self, log_type,log_content,time_cost=0):
        """记录日志

        Args:
            log_type (String): 日志类型
            log_content (Any): 日志记录内容
        """
        log_content = process_log(log_content)
        sx_log.debug(f"client：{self.client} - 用户userId：{self.userId} - 问题：{process_log(self.questionText)} - 日志类型：{log_type} - 日志内容：{log_content} - 处理时间：{time_cost}")


if __name__ == '__main__':
    agent = ChatAgent()
    agent.questionText = '去年上汽大众的总销量是多少？'
    asyncio.run(agent.decompose_question())
    