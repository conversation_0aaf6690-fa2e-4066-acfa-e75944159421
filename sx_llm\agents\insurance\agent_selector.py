"""
智能体选择器 - 使用大模型判断用户意图并选择合适的智能体

这个模块提供了一个智能体选择器类，使用大模型来分析用户消息，
理解用户意图，并选择最合适的智能体来处理请求。
支持使用OpenAI模型和对话历史。
"""


import json
from typing import Dict, List, Optional, Any, Tuple

import openai
from agents.insurance.common.types import AgentCard

api_key = 'f9dc9e22-a0ef-4b8d-939d-ce9ed0a8bbe4'
base_url = 'https://ark.cn-beijing.volces.com/api/v3'
model = 'ep-20250331151844-7qvsb'

class AgentSelector:
    """
    智能体选择器类，使用大模型来判断用户意图并选择合适的智能体。

    这个类使用OpenAI API来分析用户消息，理解用户意图，
    并从可用的智能体中选择最合适的一个来处理请求。
    支持使用对话历史来提供上下文。
    """

    def __init__(self):
        """
        初始化智能体选择器
        """

        # 初始化OpenAI客户端
        self.client = openai.OpenAI(
            api_key=api_key,
            base_url=base_url
        )

        # 系统提示模板
        self.system_prompt_template = """
            你是一个专业的任务分配专家，负责将用户请求分配给最合适的智能体。
            你需要分析用户的消息，理解用户的意图，并从可用的智能体中选择最合适的一个。

            可用的智能体:
            {agents_info}

            当前活跃的智能体: {current_agent}

            请根据以下规则选择智能体:
            1. 如果用户消息中明确提到了某个智能体的名称，选择该智能体
            2. 如果有当前活跃的智能体，并且用户消息是对之前对话的延续，继续使用该智能体
            3. 根据智能体的技能和描述，选择最匹配用户意图的智能体
            4. 如果只有一个智能体，选择该智能体
            5. 如果无法确定，返回"无法确定"

            请考虑对话历史（如果有）和当前用户消息，以便更好地理解用户意图。

            请以JSON格式返回你的决策:
            {{
            "selected_agent": "智能体名称或'无法确定'",
            "reasoning": "选择该智能体的理由"
            }}
            
            # 要求
            1. 返回的JSON不要带有```json```
            """

        # 初始化对话历史
        self.conversation_history = []

    def select_agent(
        self,
        agents: Dict[str, Tuple[AgentCard, Any]],
        user_message: str,
        current_agent: Optional[str] = None,
        conversation_history: Optional[List[Dict[str, str]]] = None
    ) -> Tuple[Optional[str], str]:
        """
        使用大模型选择最合适的智能体

        Args:
            agents: 可用的智能体字典，键为智能体名称，值为(AgentCard, Client)元组
            user_message: 用户消息
            current_agent: 当前活跃的智能体名称
            conversation_history: 对话历史，格式为[{"role": "user", "content": "..."}, {"role": "assistant", "content": "..."}]

        Returns:
            Tuple[Optional[str], str]: (选择的智能体名称, 选择理由)，如果无法确定则返回(None, 理由)
        """
        # 如果没有可用的智能体，返回None
        if not agents:
            return None, "没有可用的智能体"

        # # 如果只有一个智能体，直接返回
        # if len(agents) == 1:
        #     agent_name = list(agents.keys())[0]
        #     return agent_name, f"只有一个可用的智能体: {agent_name}"

        # 使用提供的对话历史或实例的对话历史
        history = conversation_history or self.conversation_history

        # 准备智能体信息
        agents_info = []
        for name, (card, _) in agents.items():
            agent_info = {
                "name": name,
                "description": card.description or "无描述",
                "skills": []
            }

            # 添加技能信息
            for skill in card.skills:
                skill_info = {
                    "name": skill.name,
                    "description": skill.description or "无描述",
                    "examples": skill.examples or []
                }
                agent_info["skills"].append(skill_info)

            agents_info.append(agent_info)

        # 准备消息列表
        messages = []

        # 添加系统消息
        system_content = self.system_prompt_template.format(
            agents_info=json.dumps(agents_info, ensure_ascii=False, indent=2),
            current_agent=current_agent or "无",
            user_message=user_message
        )
        messages.append({"role": "system", "content": system_content})

        # 添加对话历史
        if history:
            messages.extend(history)

        # 添加当前用户消息
        messages.append({"role": "user", "content": user_message})

        # 调用OpenAI API
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=0.1
            )

            print(f"OpenAI API响应: {response}")  # 打印API响应
            
            # 获取响应内容
            response_content = response.choices[0].message.content

            # 解析JSON响应
            result = json.loads(response_content)
            selected_agent = result.get("selected_agent")
            reasoning = result.get("reasoning", "无理由提供")

            # 更新对话历史
            self.conversation_history.append({"role": "user", "content": user_message})
            self.conversation_history.append({"role": "assistant", "content": response_content})

            # 验证选择的智能体是否存在
            if selected_agent and selected_agent != "无法确定" and selected_agent in agents:
                return selected_agent, reasoning
            elif selected_agent == "无法确定":
                return None, reasoning
            else:
                return None, f"大模型选择的智能体 '{selected_agent}' 不存在"

        except Exception as e:
            return None, f"调用大模型时出错: {str(e)}"

    def add_to_history(self, role: str, content: str) -> None:
        """
        添加消息到对话历史

        Args:
            role: 消息角色，"user"或"assistant"
            content: 消息内容
        """
        self.conversation_history.append({"role": role, "content": content})

    def clear_history(self) -> None:
        """清空对话历史"""
        self.conversation_history = []

    def get_history(self) -> List[Dict[str, str]]:
        """
        获取对话历史

        Returns:
            List[Dict[str, str]]: 对话历史列表
        """
        return self.conversation_history.copy()

    def set_history(self, history: List[Dict[str, str]]) -> None:
        """
        设置对话历史

        Args:
            history: 对话历史列表
        """
        self.conversation_history = history.copy()


if __name__ == "__main__":
    # 示例用法
    selector = AgentSelector()
    # 模拟可用的智能体
    agents = {'Echo Agent': (AgentCard(name='Echo Agent', description='This agent echos the input given', url='http://localhost:10002/', provider=None, version='0.1.0', documentationUrl=None, defaultOutputModes=['text']))}
    # 模拟用户消息
    user_message = "你好"
    # 模拟当前活跃的智能体
    current_agent = None
    # 模拟对话历史
    conversation_history = []
    # 选择智能体
    selected_agent, reasoning = selector.select_agent(agents, user_message, current_agent, conversation_history)
    print(f"选择的智能体: {selected_agent}")