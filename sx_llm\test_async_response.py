import asyncio
import uvicorn
from fastapi import FastAPI
from datetime import datetime
from fastapi.responses import StreamingResponse

app = FastAPI()

# 任务A：生成3个数据块，间隔1秒
async def async_task_a(queue: asyncio.Queue):
    for i in range(3):
        await asyncio.sleep(5)
        await queue.put(f"Task A: Chunk {i}\n")
    await queue.put(None)  # 结束标记

# 任务B：生成2个数据块，间隔0.8秒（更快）
async def async_task_b(queue: asyncio.Queue):
    for i in range(2):
        await asyncio.sleep(0.8)
        await queue.put(f"Task B: Chunk {i}\n")
    await queue.put(None)  # 结束标记

# 流式生成器：按顺序发送A→B的结果
async def ordered_stream():
    queue_a, queue_b = asyncio.Queue(), asyncio.Queue()
    
    # 并行启动两个任务
    task_a = asyncio.create_task(async_task_a(queue_a))
    task_b = asyncio.create_task(async_task_b(queue_b))
    
    # 记录任务开始时间
    start_time = datetime.now()
    
    # 第一阶段：发送任务A的结果
    while True:
        chunk = await queue_a.get()
        if chunk is None:  # 检测到任务A结束
            break
        yield chunk
    
    # 第二阶段：发送任务B的结果
    while True:
        chunk = await queue_b.get()
        if chunk is None:  # 检测到任务B结束
            break
        yield chunk
    
    # 确保任务完成（异常处理）
    await task_a
    await task_b
    
    # 记录任务结束时间并计算总时间
    end_time = datetime.now()
    total_time = (end_time - start_time).total_seconds()
    print(f"Total task completion time: {total_time:.2f} seconds")

@app.get("/parallel_stream")
async def main():
    return StreamingResponse(ordered_stream())


if __name__ == "__main__":
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8000,
        ws_ping_interval=15,
        ws_ping_timeout=5,
        log_level="debug"
    )