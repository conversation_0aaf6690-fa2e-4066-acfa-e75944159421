from exchangelib import Credentials, Account, Message, HTMLBody, Configuration, NTLM
from exchangelib.protocol import BaseProtocol, NoVerifyHTTPAdapter
from requests import Session
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.poolmanager import PoolManager
import ssl
import warnings
warnings.filterwarnings('ignore')
from langchain.chains import <PERSON><PERSON><PERSON><PERSON>
from llm import Wenxin<PERSON><PERSON>
from langchain.prompts.prompt import PromptTemplate
import smtplib
from email.mime.text import MIMEText


def mail_send(message,mail_to = '<EMAIL>') -> str:
    email_address = '<EMAIL>'
    password = 'Lk19941021l'
    server = 'itpoc.csvw.com'

    credentials = Credentials(email_address, password)
    config = Configuration(server=server, credentials=credentials, auth_type=NTLM)
    account = Account(email_address, credentials=credentials, autodiscover=False, config = config)

    subject = '沙盘问题反馈'
    body = HTMLBody('<p>'+message+'</p>')
    to_recipients = [mail_to]

    message = Message(account=account,
                    folder=account.sent,
                    subject=subject,
                    body=body,
                    to_recipients=to_recipients)

    message.send()
    return "邮件发送成功"


def sx_mail_send(message: str, mail_to: str = "<EMAIL>") -> str:
    """提供邮件发送所需的正文内容message，和接收邮件的邮箱mail_to，并发送邮件"""
    
    print(mail_to)
    print(message)
    print("*********")

    smtp_server = 'itpoc.csvw.com'
    smtp_username = '<EMAIL>'
    smtp_password = 'Lk19941021l'

    smtp_client = smtplib.SMTP(smtp_server)
    smtp_client.starttls()
    smtp_client.login(smtp_username, smtp_password)

    msg = MIMEText(message)
    msg['Subject'] = '数据异常通知'
    msg['From'] = '<EMAIL>'
    msg['To'] = mail_to

    smtp_client.sendmail(msg['From'], msg['To'], msg.as_string())
    smtp_client.quit()
    return "邮件发送成功"




prompt_template_agent = """
你是SX人工智能助手，请根据用户输入的问题，进行简洁专业的回答，不允许添加任何虚假的内容，推理过程可以使用下述工具（工具使用三个反引号分隔）：

```{tools}```

问题推理过程可以采取下面的步骤和格式，每个步骤包含关键字"Question，Thought，Action，Action Input，Observation，Final Answer"：

Question: 你需要回答的问题
Thought: 你需要考虑应该做什么
Action: 应该执行的行动，可以使用[{tool_names}]中的一个工具
Action Input: 行动的输入内容
Observation: 行动的输出结果
...(其中 Thought/Action/Action Input/Observation 可以重复很多次)
Thought: 当行动输出结果可以回答原始问题时，停止上述过程，并返回最终答案
Final Answer: 原始问题的最终答案

根据问题，请给出分析的步骤和最终答案。

Question: {question}
"""

prompt_agent = PromptTemplate(template=prompt_template_agent,input_variables=["question","tools","tool_names"])

tools = {"数据查询":{"description":"这是一个查询销量的工具，如果涉及到销量的问题，请使用此工具进行查询，并返回答案",
                    "func":"aaa"},
        "邮件发送":{"description":"这是一个邮件发送工具，当需要发送邮件时，可以使用此工具进行邮件发送",
                    "func": sx_mail_send}}



if __name__ == '__main__':
    llm = WenxinLLM()
    chain_warroom = LLMChain(llm=llm, prompt=prompt_agent)
    question = "请查询23年帕萨特销量，并给lkz发邮件"
    tool_names = ','.join(tools.keys())
    response_json = chain_warroom.run(tools=tools, question=question, tool_names=tool_names)
    # response_json = 'Thought: 需要查询23年帕萨特销量\nAction: 使用数据查询工具\nAction Input: 帕萨特 23年 销量\nObservation: 23年帕萨特销量为xx万台\nThought: 需要给lkz发送邮件\nAction: 使用邮件发送工具\nAction Input: lkz, 23年帕萨特销量\nObservation: 邮件发送成功\nFinal Answer: 已给lkz发送含有23年帕萨特销量的邮件'
    # print(response_json)

    # tool = sx_mail_send
    # tool(message = "lkz, 23年帕萨特销量为xx万台")

    chain_of_thought = response_json.split('\n')
    for i in range(len(chain_of_thought)):
        if 'Action:' in chain_of_thought[i]:
            last_thought = 'Action'
            continue
        elif 'Action Input:' in chain_of_thought[i] and last_thought == 'Action':
            tool_name = [j for j in tools.keys() if j in chain_of_thought[i-1]][0]
            tool = tools[tool_name]["func"]
            print(chain_of_thought[i+1])
            message = chain_of_thought[i].split('Action Input: ')[1]
            if tool_name == "邮件发送":
                tool(message=message)
            last_thought = ''
            


            

