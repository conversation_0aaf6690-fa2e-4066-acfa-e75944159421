import logging
from logging.handlers import TimedRotatingFileHandler
import os

class log:
    def __init__(self):
        # 创建日志收集器
        self.sx_log = logging.getLogger("api_log")
        # 设置日志等级，设置为DEBUG等级
        self.sx_log.setLevel("DEBUG")
        # 指定日志输出路径，如果没有就创建
        # log_directory = 'utils/logs'
        log_directory = 'D:\\codes\\gpt\\warroom_llm\\sx_llm\\utils\\logs'
        if not os.path.exists(log_directory):
            os.mkdir(log_directory)
        # filename = "utils/logs/api_log"
        filename = "D:\\codes\\gpt\\warroom_llm\\sx_llm\\utils\\logs\\api_log"
        # 创建一个日志输出渠道
        self.handler = TimedRotatingFileHandler(filename=filename,when="midnight",interval=1)
        self.handler.suffix = "%Y-%m-%d.log"
        # 日志输出格式
        ft = "%(asctime)s - %(levelname)s: %(message)s"
        ffmt = logging.Formatter(ft)
        self.handler.setFormatter(ffmt)
        self.sx_log.addHandler(self.handler)

sx_log = log().sx_log


# 处理日志中的'\n'
def process_log(log_content):
    if isinstance(log_content, str):
        log_content = log_content.replace('\n', '\\n')
    elif isinstance(log_content, list):
        log_content = [item.replace('\n', '\\n') if isinstance(item, str) else item for item in log_content]
    elif isinstance(log_content, dict):
        log_content = {key: value.replace('\n', '\\n') if isinstance(value, str) else value for key, value in log_content.items()}
    return log_content


def save_process_log(log_type,log_content,time_cost=0):
    """记录日志

    Args:
        log_type (String): 日志类型
        log_content (Any): 日志记录内容
    """
    log_content = process_log(log_content)
    sx_log.debug(f"日志类型：{log_type} - 日志内容：{log_content} - 处理时间：{time_cost}")



if __name__ == '__main__':
    ss = "一条小日志"
    sx_log.info(ss)
    sx_log.debug(ss)
