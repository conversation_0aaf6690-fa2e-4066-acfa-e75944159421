from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.templating import <PERSON><PERSON><PERSON>Templates
from fastapi.responses import StreamingResponse
import asyncio

app = FastAPI()
templates = Jinja2Templates(directory="templates")

@app.get("/")
async def index(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

async def event_stream(user_id: str, message: str):
    count = 0
    try:
        while True:
            count += 1
            # 添加响应检查
            if await asyncio.sleep(1, result=True) is None:
                break
            yield f"data: {user_id} {message} {count}\n\n"
    except asyncio.CancelledError:
        print("客户端已断开")
    finally:
        print("流已结束")

@app.get("/stream/{user_id}")
async def stream(user_id: str, message: str = "默认消息"):
    return StreamingResponse(
        event_stream(user_id, message),
        media_type="text/event-stream"
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=5000)