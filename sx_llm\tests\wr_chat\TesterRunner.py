
from ChatTester import ChatTester
from TestCaseGenerator import TestCaseGenerator

def print_test_case(test_cases):
    print("*"*50)
    for t in test_cases:
        print(t)
    print("*"*50)

if __name__ == "__main__":
    chat_urls = [
        # 测试环境
        # "http://127.0.0.1:8000/api/V1/chat/embedding/chain",
        # "http://127.0.0.1:8000/api/V1/chat/embedding/doc",
        # "http://127.0.0.1:8000/api/V1/chat/embedding",
        # "http://127.0.0.1:8000/api/V1/chat/embedding/format_json",
        # "http://127.0.0.1:8000/api/V1/chat/embedding/intention_dect",
        # "http://127.0.0.1:8000/api/V1/chat/embedding/format_json_ui",
        # "http://127.0.0.1:8000/api/V1/chat/embedding/V2",
        "http://172.20.242.32:8080/api/V1/chat/embedding/format_json",
        # "http://172.20.242.32:8080/api/V1/chat/embedding/intention_dect",
        # "http://172.20.242.32:8080/api/V1/chat/embedding/format_json_ui",
        # 生产环境
        # "http://172.20.135.16:8000/api/V1/chat/embedding",
    ]

    chat_url_intention = [
        # 测试环境
        # "http://127.0.0.1:8000/api/V1/chat/embedding/intention_dect",
        "http://172.20.242.32:8080/api/V1/chat/embedding/intention_dect",
        # 生产环境
        # "http://172.20.135.16:8000/api/V1/chat/embedding/intention_dect",
    ]

    tester = ChatTester(chat_urls, tsv_files=[], check_type='json', llm="wenxin", prompt="prompt_V7")
    tester_intention = ChatTester(chat_url_intention, tsv_files=[], check_type='json', llm="wenxin", prompt="prompt_V7")

    generator = TestCaseGenerator()
    

    data_range = [('202201', '202308'), ('2022', '2023'), ('20231','20233')]

    # 测试提取关键词接口
    test_cases = []
    test_cases_1 = []
    test_cases_2 = []
    for min_date, max_date in data_range:
        test_cases_1 = generator.generate_type_1(num_cases=100, min_month=min_date, max_month=max_date, model_max=2, model_min=0, rssc_max=2, rssc_min=0)
        test_cases_2 = generator.generate_type_2(num_cases=100, min_month=min_date, max_month=max_date, model_max=2, model_min=0, rssc_max=2, rssc_min=0, intention_max=4)
        test_cases = test_cases + test_cases_1 + test_cases_2

    test_cases_4 = generator.generate_type_4(num_cases=100, file_name="today_data_query.txt")
    test_cases = test_cases + test_cases_4

    # tester.run_tests(round=1, test_cases=test_cases)

    test_cases_mani = []
    for min_date, max_date in data_range:
        test_cases_3 = generator.generate_type_3(num_cases=100, min_month=min_date, max_month=max_date)
        test_cases_mani = test_cases_mani + test_cases_3
    
    # tester.run_tests(round=1, test_cases=test_cases_mani)

    # 测试问题分类接口
    test_cases_intention = []
    test_cases_intention_1 = []
    test_cases_intention_2 = []
    test_cases_intention_3 = []
    for min_date, max_date in data_range:
        test_cases_intention_1 = generator.generate_type_1(num_cases=100, min_month=min_date, max_month=max_date, model_max=2, model_min=0, rssc_max=2, rssc_min=0, answer_type=2)
        test_cases_intention_2 = generator.generate_type_2(num_cases=100, min_month=min_date, max_month=max_date, model_max=2, model_min=0, rssc_max=2, rssc_min=0, intention_max=4, answer_type=2)
        test_cases_intention_3 = generator.generate_type_3(num_cases=100, min_month=min_date, max_month=max_date, answer_type=2)

        test_cases_intention = test_cases_intention + test_cases_intention_1 + test_cases_intention_2 + test_cases_intention_3


    test_cases_intention_4 = generator.generate_type_4(num_cases=100, file_name="index_query.txt", answer_type=2, answer="知识库查询")
    test_cases_intention_5 = generator.generate_type_4(num_cases=100, file_name="today_data_query.txt", answer_type=2, answer="数据查询")
    test_cases_intention_6 = generator.generate_type_4(num_cases=100, file_name="other_query.txt", answer_type=2, answer="未知意图")
    test_cases_intention_7 = generator.generate_type_4(num_cases=100, file_name="common_data_query.txt", answer_type=2, answer="数据查询")

    test_cases_intention = test_cases_intention + test_cases_intention_4 + test_cases_intention_5 + test_cases_intention_6 + test_cases_intention_7

    # tester_intention.run_tests(round=1, test_cases=test_cases_intention)

    # 回归测试

    test_cases_intention_txt = test_cases_intention_4 + test_cases_intention_5 + test_cases_intention_6 + test_cases_intention_7

    # 测试问题分类
    tester_intention.run_tests(round=1, test_cases=test_cases_intention_txt)

    # 测试提取关键词
    test_cases_json_1 = generator.generate_type_4(num_cases=100, file_name="today_data_query.txt", answer_type=1)
    test_cases_json_2 = generator.generate_type_4(num_cases=100, file_name="common_data_query.txt", answer_type=1)

    test_cases_json = test_cases_json_1 + test_cases_json_2
    tester.run_tests(round=1, test_cases=test_cases_json)
