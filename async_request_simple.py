import aiohttp
import asyncio
import json
from urllib.parse import urlencode

async def main():
    # 完全相同的参数
    base_url = 'http://************:8080/report/mobile/wechat/sv/afterSaleJyController/queryJyIndex'
    params = {"yearMonth": "202301", "queryType": "M"}
    auth_token = 'SYAAkjENRNIAFVoQKylHQ51nZRNeX12-'
    
    # 1. 创建URL (标准拼接方式)
    url = f"{base_url}?{urlencode(params)}"
    print(f"请求URL: {url}")
    
    # 2. 设置与同步请求完全相同的请求头
    headers = {
        'User-Agent': 'python-requests/2.32.3',
        'Accept-Encoding': 'gzip, deflate',
        'Accept': '*/*',
        'Connection': 'keep-alive',
        'Authorization': auth_token
    }
    
    # 3. aiohttp的TCP连接器设置
    tcp_connector = aiohttp.TCPConnector(
        ssl=False,
        force_close=True,  # 每次请求后关闭连接
        ttl_dns_cache=300  # DNS缓存时间
    )
    
    try:
        # 4. 创建会话并发送请求
        async with aiohttp.ClientSession(
            connector=tcp_connector,
            headers=headers,  # 基础请求头
            version=aiohttp.HttpVersion11,  # 强制HTTP/1.1
            cookie_jar=aiohttp.CookieJar()  # 启用cookie处理
        ) as session:
            print("发送请求...")
            async with session.get(
                url,
                timeout=30,
                allow_redirects=True,
            ) as response:
                # 5. 打印响应信息
                print(f"状态码: {response.status}")
                print(f"响应头: {response.headers}")
                
                # 6. 读取并打印响应体
                body = await response.read()
                print(f"响应体长度: {len(body)} 字节")
                
                # 7. 尝试解析为文本
                try:
                    text = body.decode('utf-8')
                    print(f"响应内容前100个字符: {text[:100]}")
                    
                    # 8. 尝试解析为JSON
                    if 'application/json' in response.headers.get('Content-Type', ''):
                        data = await response.json()
                        print(f"解析成功的JSON数据: {data}")
                        return data
                    else:
                        # 即使Content-Type不是JSON，也尝试手动解析
                        try:
                            data = json.loads(text)
                            print(f"手动解析的JSON数据: {data}")
                            return data
                        except json.JSONDecodeError as e:
                            print(f"JSON解析错误: {e}")
                            
                            # 如果返回的是HTML，进一步分析
                            if '<html' in text.lower():
                                print("服务器返回了HTML而不是JSON")
                                if 'login' in text.lower():
                                    print("发现登录页面，可能需要重新认证")
                                if 'meta http-equiv="refresh"' in text.lower():
                                    print("页面包含重定向")
                            
                            return None
                except UnicodeDecodeError as e:
                    print(f"解码错误: {e}")
                    return None
    except aiohttp.ClientError as e:
        print(f"客户端错误: {e}")
        return None
    except asyncio.TimeoutError:
        print("请求超时")
        return None
    except Exception as e:
        print(f"未知错误: {type(e).__name__}: {e}")
        return None

if __name__ == "__main__":
    asyncio.run(main()) 