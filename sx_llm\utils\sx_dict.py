sx_guide_reply = {"中文":'1.数据查询：\n1.1.范围：SVW Copilot可查询大众品牌转化、目标、库存及市场竞品上险数据。\n1.2.要素：查询需明确指标、时间（默认本月）和地区（默认全国），可选品牌或车型（默认SVW-VW）。\n1.3.结果：提供文字、图表、同环比等多种形式的查询反馈。\n2.数据分析：在查询基础上加"分析"，SVW Copilot会结合数据给出分析结果。\n3.非数据问题：对于常识等问题，SVW Copilot依据大语言模型的历史语料库进行回答。',
                  "英文":'1. Data query:\n1.1. Scope: SVW Copilot can query SVW-VW brand conversion, targets, stock, and competitor insurance data.\n1.2. Element: The query should specify the indicators, time (default for this month), and region (default for the whole country), and the brand or model is optional (default for SVW-VW).\n1.3. Results: Provide various forms of query feedback such as text, charts, and table.\n2. Data analysis: Add "analysis" to the query sentence, and SVW Copilot will provide analysis results based on the data.\n3. Non data questions: For common sense and other questions, SVW Copilot answers based on the historical corpus of the large language model.'}


sx_intention_reply = {"中文":{"数据查询":"好的，请稍候，正在为您分析",
                              "数据分析":"好的，请稍候，正在为您分析",
                              "知识库查询":"好的，请稍候，正在为您分析",
                              "UI交互":"好的，请稍候，正在为您分析",
                              "未知意图":"好的，请稍候，正在为您查询",},
                      "英文":{"数据查询":"Please hold on, I am currently analyzing for you.",
                              "数据分析":"Please hold on, I am currently analyzing for you.",
                              "知识库查询":"Please hold on, I am currently analyzing for you.",
                              "UI交互":"Please hold on, I am currently analyzing for you.",
                              "未知意图":"Please hold on, I am currently analyzing for you.",}}

sx_warroom_reply = {"实时数据":{"中文": "今日实时转化数据，数据截至{}：",
                               "英文": "Today real time conversion data, as of {}"},
                   "非实时数据":{"中文": "数据：",
                                "英文": " data: "}, 
                               }

error_reply = {"中文": "抱歉，没有听清您的问题，麻烦您再说一次",
               "英文": "Sorry, I didn't hear you, could you please say it again?"}


# sx_warroom_response_reply = {"中文":"抱歉，目前没有查询到相关数据，请问还有什么可以帮您？",
#                              "英文":"Sorry, no relevant data has been found. Anything else I can do for you?"}


sx_warroom_response_reply = {"中文":"抱歉，目前没有查询到{}相关数据，请问还有什么可以帮您？",
                             "英文":"Sorry, no {} data has been found. Anything else I can do for you?"}


# sx_answer_source = {"中文":{"上险":"\n数据来源为，上险数据，其中，市占数据基于整体市场计算得出",
#                             "转化":"\n数据来源为，转化数据",
#                             "知识库":"\n来源：沙盘相关信息",
#                             "LLM":"\n来源：文心一言",},
#                     "英文":{"上险":"\nData source is Insurance Data, of which marketshare data is calculated based on total market",
#                             "转化":"\nData source is Conversion Data",
#                             "知识库":"\nFrom Warroom relevant information",
#                             "LLM":"\nFrom Wenxinyiyan",}}

sx_answer_source = {"中文":{"上险":"上险数据，其中，市占数据基于整体市场计算得出：",
                            "转化":"转化数据：",
                            "知识库":"\n来源：沙盘相关信息",
                            "LLM":"\n【DeepSeek-R1结合互联网信息分析得出】",},
                    "英文":{"上险":" Insurance Data, of which marketshare data is calculated based on total market:",
                            "转化":" Conversion Data:",
                            "知识库":"\nFrom Warroom relevant information",
                            "LLM":"\n[Conclusion based on internet information by DeepSeek-R1]",}}



sx_kpi_name = {"time":{"中文":"时间","英文":"Time"},
                 "location":{"中文":"地区","英文":"Location"},
                 "model":{"中文":"品牌/车型","英文":"Brand/Model"},
                 "volume":{"中文":"上险量","英文":"Insurance"},
                 "marketShare":{"中文":"市占","英文":"Market Share"},
                 "lastVolume":{"中文":"上月上险量","英文":"Last Month Insurance"},
                 "yoymVolume":{"中文":"去年同期上险量","英文":"Yoy Month Insurance"},
                 "volumeMom":{"中文":"上险量环比","英文":"Insurance MoM"},
                 "volumeYoy":{"中文":"上险量同比","英文":"Insurance YoY"},
                 "monthOnMonthChange":{"中文":"市占环比","英文":"Market Share MoM"},
                 "yearOnYearChange":{"中文":"市占同比","英文":"Market Share YoY"},
                 "leadsNotDuplicateCnt":{"中文":"客源","英文":"Leads"},
                 "deliveryEinvoiceNotSvkCnt":{"中文":"零售发票","英文":"Retail Invoice"},
                 "orderCnt":{"中文":"订单","英文":"NCO"},
                 "oppTestdriveCnt":{"中文":"试乘试驾","英文":"Test Drive"},
                 "oppWalkinCnt":{"中文":"展厅客流","英文":"Showroom Traffic"},
                 "oppCnt":{"中文":"潜客","英文":"NPC"},
                 "insurance":{"中文":"上险","英文":"Insurance"},
                 "funnel":{"中文":"转化漏斗","英文":"Funnel"},
                 "targetLeads":{"中文":"客源目标","英文":"Leads Target"},
                 "targetOpportunity":{"中文":"潜客目标","英文":"NPC Target"},
                 "targetOppWalkin":{"中文":"展厅客流目标","英文":"Showroom Traffic Target"},
                 "targetOppTestdrive":{"中文":"试乘试驾目标","英文":"Test Drive Target"},
                 "targetOrder":{"中文":"订单目标","英文":"NCO Target"},
                 "targetInvoice":{"中文":"发票目标","英文":"Retail Invoice Target"},
                 "leadsTransferRate":{"中文":"线索转化率","英文":"LTO"},
                 "leadsArrivalRate":{"中文":"线索到店率","英文":"Leads to ST"},
                 "testdriveRate":{"中文":"试乘试驾率","英文":"ST to TD"},
                 "oppWalkinTransferRate":{"中文":"到店转化率","英文":"ST to NCO"},
                 "orderDeliveryNotSvkRate":{"中文":"订单成交率","英文":"NCO to Invoice"},
                 "conversion":{"中文":"转化","英文":"Conversion"},
                 "completion":{"中文":"完成","英文":"Achieved"},
                 "target":{"中文":"目标","英文":"Target"},
                 "time_schedule":{"中文":"时间进度","英文":"Time Elapsed"},
                 "leadsNotDuplicateCntDayAvgMom":{"中文":"日均客源环比","英文":"Leads MoM (Daily)"},
                 "leadsNotDuplicateCntDayAvgYoy":{"中文":"日均客源同比","英文":"Leads YoY (Daily)"},
                 "oppCntDayAvgMom":{"中文":"日均潜客环比","英文":"NPC MoM (Daily)"},
                 "oppCntDayAvgYoy":{"中文":"日均潜客同比","英文":"NPC YoY (Daily)"},
                 "oppWalkinCntDayAvgMom":{"中文":"日均展厅客流环比","英文":"Showroom Traffic MoM (Daily)"},
                 "oppWalkinCntDayAvgYoy":{"中文":"日均展厅客流同比","英文":"Showroom Traffic YoY (Daily)"},
                 "oppTestdriveCntDayAvgMom":{"中文":"日均试乘试驾环比","英文":"Test Drive MoM (Daily)"},
                 "oppTestdriveCntDayAvgYoy":{"中文":"日均试乘试驾同比","英文":"Test Drive YoY (Daily)"},
                 "orderCntDayAvgMom":{"中文":"日均订单环比","英文":"NCO MoM (Daily)"},
                 "orderCntDayAvgYoy":{"中文":"日均订单同比","英文":"NCO YoY (Daily)"},
                 "deliveryEinvoiceNotSvkCntDayAvgMom":{"中文":"日均零售发票环比","英文":"Retail Invoice MoM (Daily)"},
                 "deliveryEinvoiceNotSvkCntDayAvgYoy":{"中文":"日均零售发票同比","英文":"Retail Invoice YoY (Daily)"},
                 "leadsTransferRateMom":{"中文":"线索转化率环比","英文":"LTO MoM"},
                 "leadsTransferRateYoy":{"中文":"线索转化率同比","英文":"LTO YoY"},
                 "leadsArrivalRateMom":{"中文":"线索到店率环比","英文":"Leads to ST MoM"},
                 "leadsArrivalRateYoy":{"中文":"线索到店率同比","英文":"Leads to ST YoY"},
                 "testdriveRateMom":{"中文":"试乘试驾率环比","英文":"ST to TD MoM"},
                 "testdriveRateYoy":{"中文":"试乘试驾率同比","英文":"ST to TD YoY"},
                 "oppWalkinTransferRateMom":{"中文":"到店转化率环比","英文":"ST to NCO MoM"},
                 "oppWalkinTransferRateYoy":{"中文":"到店转化率同比","英文":"ST to NCO YoY"},
                 "orderDeliveryNotSvkRateMom":{"中文":"订单成交率环比","英文":"NCO to Invoice MoM"},
                 "orderDeliveryNotSvkRateYoy":{"中文":"订单成交率同比","英文":"NCO to Invoice YoY"},
                 "wholesaleCnt":{"中文":"批售","英文":"Whole Sale"},
                 "eInvoiceCnt":{"中文":"发票","英文":"Invoice"},
                 "wholesaleTargetCnt":{"中文":"批售目标","英文":"Whole Sale Target"},
                 "eInvoiceTargetCnt":{"中文":"发票目标","英文":"Invoice Target"},
                 "stockTotalDealerCnt":{"中文":"经销商库存","英文":"Dealer Stock"},
                 "stockTotalHqCnt":{"中文":"总部库存","英文":"HQ Stock"},
                 "stockTotalDealerIndex":{"中文":"经销商库存当量","英文":"Dealer Stock Factor"},
                 "stockTotalHqIndex":{"中文":"总部库存当量","英文":"HQ Stock Factor"},
                 "wholesaleCntMom":{"中文":"批售数环比","英文":"Whole Sale MoM"},
                 "wholesaleCntYoy":{"中文":"批售数同比","英文":"Whole Sale YoY"},
                 "wholesaleCntDayAvgMom":{"中文":"日均批售数环比","英文":"Whole Sale MoM (Daily)"},
                 "wholesaleCntDayAvgYoy":{"中文":"日均批售数同比","英文":"Whole Sale YoY (Daily)"},
                 "eInvoiceCntMom":{"中文":"发票数环比","英文":"Invoice MoM"},
                 "eInvoiceCntYoy":{"中文":"发票数同比","英文":"Invoice YoY"},
                 "eInvoiceCntDayAvgMom":{"中文":"日均发票数环比","英文":"Invoice MoM (Daily)"},
                 "eInvoiceCntDayAvgYoy":{"中文":"日均发票数同比","英文":"Invoice YoY (Daily)"},                 
                 "and":{"中文":"和","英文":" and "},
                 "total":{"中文":"总计","英文":"Total"},
                 "todayLeadsNotDuplicateCnt":{"中文":"客源","英文":"Leads"},
                 "todayOppCnt":{"中文":"潜客","英文":"NPC"},
                 "todayOppWalkinCnt":{"中文":"展厅客流","英文":"Showroom Traffic"},
                 "todayOppTestdriveCnt":{"中文":"试乘试驾","英文":"Test Drive"},
                 "todayOrderCnt":{"中文":"订单","英文":"NCO"},
                 "todayDeliveryEinvoiceCnt":{"中文":"发票","英文":"Invoice"},
                 "todayDeliveryEinvoiceNotSvkCnt":{"中文":"零售发票","英文":"Retail Invoice"},
                 "todayEInvoiceCnt":{"中文":"发票","英文":"Invoice"},
                 "销量":{"中文":"上险","英文":"Insurance"},
                 "市占":{"中文":"市占","英文":"Market Share"},
                 "客源数":{"中文":"客源","英文":"Leads"},
                 "零售发票数":{"中文":"零售发票","英文":"Retail Invoice"},
                 "含大客户发票数":{"中文":"发票","英文":"Invoice"},
                 "订单数":{"中文":"订单","英文":"NCO"},
                 "试乘试驾数":{"中文":"试乘试驾","英文":"Test Drive"},
                 "展厅客流数":{"中文":"展厅客流","英文":"Showroom Traffic"},
                 "潜客数":{"中文":"潜客","英文":"NPC"},
                 "上险":{"中文":"上险","英文":"Insurance"},
                 "漏斗":{"中文":"转化漏斗","英文":"Funnel"},
                 "客源目标":{"中文":"客源目标","英文":"Leads Target"},
                 "潜客目标":{"中文":"潜客目标","英文":"NPC Target"},
                 "展厅客流目标":{"中文":"展厅客流目标","英文":"Showroom Traffic Target"},
                 "试乘试驾目标":{"中文":"试乘试驾目标","英文":"Test Drive Target"},
                 "订单目标":{"中文":"订单目标","英文":"NCO Target"},
                 "发票目标":{"中文":"发票目标","英文":"Retail Invoice Target"},
                 "线索转化率":{"中文":"线索转化率","英文":"LTO"},
                 "线索到店率":{"中文":"线索到店率","英文":"Leads to ST"},
                 "试乘试驾率":{"中文":"试乘试驾率","英文":"ST to TD"},
                 "到店转化率":{"中文":"到店转化率","英文":"ST to NCO"},
                 "订单成交率":{"中文":"订单成交率","英文":"NCO to Invoice"},
                 "转化":{"中文":"转化","英文":"Conversion"},
                 "批售":{"中文":"批售","英文":"Whole Sale"},
                 "发票":{"中文":"发票","英文":"Invoice"},
                 "批售目标":{"中文":"批售目标","英文":"Whole Sale Target"},
                 "含大客户发票目标":{"中文":"发票目标","英文":"Invoice Target"},
                 "发票目标":{"中文":"发票目标","英文":"Invoice Target"},
                 "总库存":{"中文":"经销商库存","英文":"Dealer Stock"},
                 "总部库存":{"中文":"总部库存","英文":"HQ Stock"},
                 "总库存当量":{"中文":"经销商库存当量","英文":"Dealer Stock Factor"},
                 "总部库存当量":{"中文":"总部库存当量","英文":"HQ Stock Factor"},
                 "今日新增客源数":{"中文":"客源","英文":"Leads"},
                 "今日新增潜客数":{"中文":"潜客","英文":"NPC"},
                 "今日新增到店数":{"中文":"展厅客流","英文":"Showroom Traffic"},
                 "今日新增试乘试驾数":{"中文":"试乘试驾","英文":"Test Drive"},
                 "今日新增订单数":{"中文":"订单","英文":"NCO"},
                 "今日新增发票数":{"中文":"零售发票","英文":"Retail Invoice"},
                 "今日交车":{"中文":"发票","英文":"Invoice"},
                 "stockTotalCnt":{"中文":"经销商库存","英文":"Dealer Stock"},
                 "stockTotalIndex":{"中文":"经销商库存当量","英文":"Dealer Stock Factor"},
                 "stockTotalIndexMomChange":{"中文":"经销商库存当量环比","英文":"Dealer Stock Factor MoM"},
                 "stockTotalIndexYoyChange":{"中文":"经销商库存当量同比","英文":"Dealer Stock Factor YoY"},
                 "leadsNotDuplicateCntDayAvg":{"中文":"日均客源","英文":"Leads (Daily)"},
                 "oppCntDayAvg":{"中文":"日均潜客","英文":"NPC (Daily)"},
                 "oppWalkinCntDayAvg":{"中文":"日均展厅客流","英文":"Showroom Traffic (Daily)"},
                 "oppTestdriveCntDayAvg":{"中文":"日均试乘试驾","英文":"Test Drive (Daily)"},
                 "orderCntDayAvg":{"中文":"日均订单","英文":"NCO (Daily)"},
                 "deliveryEinvoiceNotSvkCntDayAvg":{"中文":"日均零售发票","英文":"Retail Invoice (Daily)"},
                 "yoymLeadsNotDuplicateCntDayAvg":{"中文":"去年同期日均客源","英文":"YoY Leads (Daily)"},
                 "yoymOppCntDayAvg":{"中文":"去年同期日均潜客","英文":"YoY NPC (Daily)"},
                 "yoymOppWalkinCntDayAvg":{"中文":"去年同期日均展厅客流","英文":"YoY Showroom Traffic (Daily)"},
                 "yoymOppTestdriveCntDayAvg":{"中文":"去年同期日均试乘试驾","英文":"YoY Test Drive (Daily)"},
                 "yoymOrderCntDayAvg":{"中文":"去年同期日均订单","英文":"YoY NCO (Daily)"},
                 "yoymDeliveryEinvoiceNotSvkCntDayAvg":{"中文":"去年同期日均零售发票","英文":"YoY Retail Invoice (Daily)"},
                 "本期日均客源":{"中文":"客源","英文":"Leads"},
                 "本期日均潜客":{"中文":"潜客","英文":"NPC"},
                 "本期日均展厅客流":{"中文":"展厅客流","英文":"Showroom Traffic"},
                 "本期日均试乘试驾":{"中文":"试乘试驾","英文":"Test Drive"},
                 "本期日均订单":{"中文":"订单","英文":"NCO"},
                 "本期日均零售发票":{"中文":"零售发票","英文":"Retail Invoice"},
                 "eInvoiceCntDayAvg":{"中文":"日均发票","英文":"Invoice (Daily)"},
                 "wholesaleCntDayAvg":{"中文":"日均批售","英文":"Whole Sale (Daily)"},
                 "日均批售数":{"中文":"批售","英文":"Whole Sale"},
                 "日均含大客户发票数":{"中文":"发票","英文":"Invoice"},
                 "yoymEInvoiceCntDayAvg":{"中文":"去年同期日均发票","英文":"YoY Invoice (Daily)"},
                 "yoymWholesaleCntDayAvg":{"中文":"去年同期日均批售","英文":"YoY Whole Sale (Daily)"},
                 }









sx_funnel_name = {"leadsNotDuplicateCnt":{"中文":"客源","英文":"Leads"},
                 "deliveryEinvoiceNotSvkCnt":{"中文":"零售发票","英文":"Retail Invoice"},
                 "orderCnt":{"中文":"订单","英文":"NCO"},
                 "oppTestdriveCnt":{"中文":"试乘试驾","英文":"Test Drive"},
                 "oppWalkinCnt":{"中文":"展厅客流","英文":"Showroom Traffic"},
                 "oppCnt":{"中文":"潜客","英文":"NPC"},
                 "leadsTransferRate":{"中文":"LTO","英文":"LTO"},
                 "leadsArrivalRate":{"中文":"线索到店率","英文":"Leads to ST"},
                 "testdriveRate":{"中文":"试乘试驾率","英文":"ST to TD"},
                 "oppWalkinTransferRate":{"中文":"到店转化率","英文":"ST to NCO"},
                 "orderDeliveryNotSvkRate":{"中文":"订单成交率","英文":"NCO to Invoice"},
                 }


sx_mixed_chart_name = {"pie":{"中文":"饼图","英文":"Pie"},
                        "line":{"中文":"折线图","英文":"Line"},
                        "bar":{"中文":"柱状图","英文":"Bar"},
                        "bar_line":{"中文":"柱状图和折线图","英文":"Bar & Line"},
                        }


sx_display_name = {"趋势":{"中文":"趋势","英文":"Trend"},
                        "对比":{"中文":"对比","英文":"Comparison"},
                        "排名":{"中文":"排名","英文":"Rank"},
                        "分布":{"中文":"分布","英文":"Distribution"},
                        "漏斗":{"中文":"漏斗","英文":"Funnel"},
                        "目标":{"中文":"目标","英文":"Target"},
                        }


sx_date_name = {"week":{"中文":"周","英文":"week"},
                "month":{"中文":"月","英文":"month"},
                "year":{"中文":"年","英文":"year"},
                 "1":{"中文":"1","英文":"January"},
                 "2":{"中文":"2","英文":"February"},
                 "3":{"中文":"3","英文":"March"},
                 "4":{"中文":"4","英文":"April"},
                 "5":{"中文":"5","英文":"May"},
                 "6":{"中文":"6","英文":"June"},
                 "7":{"中文":"7","英文":"July"},
                 "8":{"中文":"8","英文":"August"},
                 "9":{"中文":"9","英文":"September"},
                 "10":{"中文":"10","英文":"October"},
                 "11":{"中文":"11","英文":"November"},
                 "12":{"中文":"12","英文":"December"},
                 "to":{"中文":"至","英文":" to "},
                 "from":{"中文":"","英文":"From "},
                }



# kpi_mappings = {
#     '销量': ['销量','市占','销量同比','销量环比'],
#     '市占': ['销量','市占','销量同比','销量环比'],
#     '客源数': ['客源数','线索转化率','客源数同比','客源数环比'],
#     '线索转化率': ['客源数','线索转化率','客源数同比','客源数环比'],
#     '潜客数': ['潜客数','线索到店率','潜客数同比','潜客数环比'],
#     '线索到店率': ['潜客数','线索到店率','潜客数同比','潜客数环比'],
#     '展厅客流数': ['展厅客流数','到店转化率','日均展厅客流数同比','日均展厅客流数环比'],
#     '到店转化率': ['展厅客流数','到店转化率','日均展厅客流数同比','日均展厅客流数环比'],
#     '试乘试驾数': ['试乘试驾数','试乘试驾率','试乘试驾数同比','试乘试驾数环比'],
#     '试乘试驾率': ['试乘试驾数','试乘试驾率','试乘试驾数同比','试乘试驾数环比'],
#     '订单数': ['订单数','订单成交率','订单数同比','订单数环比'],
#     '订单成交率': ['订单数','订单成交率','订单数同比','订单数环比'],
#     '发票数': ['发票数','订单成交率','发票数同比','发票数环比'],
#     '批售数': ['批售数','批售数日均同比','批售数日均环比'],
#     '含大客户发票数': ['含大客户发票数','含大客户发票数日均同比','含大客户发票数日均环比'],
#     '总部库存': ['总部库存','总部库存当量'],
#     '总部库存当量': ['总部库存','总部库存当量'],
#     '总库存': ['总库存','总库存当量'],
#     '总库存当量': ['总库存','总库存当量']}



kpi_mappings = {
    '销量': ['销量','市占','销量同比','销量环比'],
    '市占': ['销量','市占','销量同比','销量环比'],
    '客源数': ['客源数','线索转化率','客源数同比','客源数环比'],
    '线索转化率': ['客源数','线索转化率','客源数同比','客源数环比'],
    '潜客数': ['潜客数','线索到店率','潜客数同比','潜客数环比'],
    '线索到店率': ['潜客数','线索到店率','潜客数同比','潜客数环比'],
    '展厅客流数': ['展厅客流数','到店转化率','日均展厅客流数同比','日均展厅客流数环比'],
    '到店转化率': ['展厅客流数','到店转化率','日均展厅客流数同比','日均展厅客流数环比'],
    '试乘试驾数': ['试乘试驾数','试乘试驾率','试乘试驾数同比','试乘试驾数环比'],
    '试乘试驾率': ['试乘试驾数','试乘试驾率','试乘试驾数同比','试乘试驾数环比'],
    '订单数': ['订单数','订单成交率','订单数同比','订单数环比'],
    '订单成交率': ['订单数','订单成交率','订单数同比','订单数环比'],
    '发票数': ['发票数','订单成交率','发票数同比','发票数环比'],
    '本期日均客源数': ['本期日均客源数','线索转化率','客源数同比','客源数环比'],
    '本期日均潜客数': ['本期日均潜客数','线索到店率','潜客数同比','潜客数环比'],
    '本期日均展厅客流数': ['本期日均展厅客流数','到店转化率','日均展厅客流数同比','日均展厅客流数环比'],
    '本期日均试乘试驾数': ['本期日均试乘试驾数','试乘试驾率','试乘试驾数同比','试乘试驾数环比'],
    '本期日均订单数': ['本期日均订单数','订单成交率','订单数同比','订单数环比'],
    '本期日均发票数': ['本期日均发票数','订单成交率','发票数同比','发票数环比'],
    '批售数': ['批售数','批售数日均同比','批售数日均环比'],
    '含大客户发票数': ['含大客户发票数','含大客户发票数日均同比','含大客户发票数日均环比'],
    '日均批售数': ['日均批售数','批售数日均同比','批售数日均环比'],
    '日均含大客户发票数': ['日均含大客户发票数','含大客户发票数日均同比','含大客户发票数日均环比'],
    '总部库存': ['总部库存','总部库存当量'],
    '总部库存当量': ['总部库存','总部库存当量'],
    '总库存': ['总库存','总库存当量'],
    '总库存当量': ['总库存','总库存当量']}


not_main_kpi = ['市占','销量同比','销量环比',
                '线索转化率','客源数同比','客源数环比',
                '线索到店率','潜客数同比','潜客数环比',
                '到店转化率','日均展厅客流数同比','日均展厅客流数环比',
                '试乘试驾率','试乘试驾数同比','试乘试驾数环比',
                '订单成交率','订单数同比','订单数环比',
                '发票数同比','发票数环比',
                '批售数日均同比','批售数日均环比',
                '含大客户发票数日均同比','含大客户发票数日均环比',
                '总部库存当量',
                '总库存当量']


llm_error_reply = {'中文': '抱歉，数据分析内容过长，无法返回。',
                   '英文': 'Sorry, the data analysis content is too long and cannot be returned.'}

data_analysis_prefix = {'中文': '⭐大模型联网搜索中⭐\r\n------------------------------\r\n',
                        '英文': '⭐The model is performing a network search⭐ (Deep thinking process may output Chinese, but final answer will be in English)\r\n------------------------------\r\n'
                        }

data_analysis_suffix = {'中文': '【DeepSeek-R1结合互联网信息分析得出】\r\n------------------------------\r\n\n⭐结论⭐',
                        '英文': '[Analysis based on internet information by DeepSeek-R1]\r\n------------------------------\r\n\n⭐Conclusion⭐'
                        }