from langchain_community.document_loaders import Text<PERSON>oader
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS
from utils.sx_text_splitter import SXCharacterTextSplitter
from utils.config import *
import os
from utils.sx_log import sx_log
# from sx_text_splitter import SXCharacterTextSplitter
# from config import *
# import os
# from sx_log import sx_log


# embeddings = HuggingFaceEmbeddings(model_name=embedding_model_dict[init_embedding_model])
# embeddings = HuggingFaceEmbeddings(model_name="D:\code\gpt\models\m3e-base")
# embeddings = HuggingFaceEmbeddings(model_name="D:\code\gpt\models\\nlp_gte_sentence-embedding_chinese-base")
# embeddings = HuggingFaceEmbeddings(model_name="D:\models\\nlp_gte_sentence-embedding_chinese-large")
# from langchain.embeddings import QianfanEmbeddingsEndpoint
# embeddings = QianfanEmbeddingsEndpoint(qianfan_ak=BAIDU_CLIENT_ID,qianfan_sk=BAIDU_CLIENT_SECRET)

# 读取当前文件父节点路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

def save_vector(file_path,save_file_path):
    file_path = os.path.join(parent_dir, 'data', file_path)
    loader = TextLoader(file_path,encoding='utf-8')
    documents = loader.load()
    text_splitter = SXCharacterTextSplitter(separator = "\n",chunk_size = 10,chunk_overlap = 0,length_function = len,)
    texts = text_splitter.split_documents(documents)
    db = FAISS.from_documents(texts, embeddings)
    save_file_path = os.path.join(parent_dir, 'vector_store', save_file_path)
    db.save_local(save_file_path)
    return db

# brand_db = save_vector('brand_name_cn.txt',"brand_index")
# model_db = save_vector('model_name_cn.txt',"model_index")
# knowledge_db = save_vector('knowledge_document.txt',"knowledge_index")
# template_db = save_vector('template_name.txt',"template_index")


# 查询问题匹配度前k的文本内容
def vector_index(file_path,input_text,top_k=1) -> list:
    db = FAISS.load_local(file_path, embeddings, allow_dangerous_deserialization=True)
    docsearch = db.as_retriever(search_kwargs={"k": top_k})
    rel_docs = docsearch.invoke(input=input_text)
    sx_log.debug(rel_docs)
    results = [doc.page_content for doc in rel_docs]
    return docsearch,results

if __name__=="__main__":
    model_db = save_vector('model_name_cn.txt',"model_index")





