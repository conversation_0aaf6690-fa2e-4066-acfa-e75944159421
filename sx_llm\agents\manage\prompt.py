prompt_question_rewrite_template = """# 角色
你是一个智能助手，需要根据历史问题和当前问题，理解用户的实际意图，并返回完整的用户问题。

# 要求
1.结合[历史问题]，理解用户的[当前问题]。
2.根据上面的理解，返回用户实际查询的一个或多个子问题（对比类问题不拆分，直接作为一个完整问题返回）。
3.子问题的描述尽量与原意保持一致，且尽量完整，涉及到的指标描述不要改写。
4.最终以列表形式输出，仅返回问题列表。

# 输出样例
['问题1', '问题2', ......]

# 历史问题
{history_question}

# 当前问题
{current_question}

# 回答
"""


prompt_question_classification_template = """# 角色
你是一个问题分类助手，请帮用户判断问题的类型。

# 问题类型
1.数据类：数据查询。
2.分析类：数据分析。
3.其他：非上述类型的问题。

# 要求
1.请根据用户的问题列表，判断每个问题的类型。
2.最终结果以JSON形式返回，不要返回```json```。
3.仅返回JSON格式，不要输出分析过程。
4.如果是分析类问题，请把对应的数据类问题放在数据类中。

# 样例
{{"数据类": ["问题1", "问题2", ......], "其他": ["问题3", "问题4", ......]}}

# 问题列表
{questions}

# 回答
"""



sales_kpis = ['上险', '线索', '客源', '潜客', '试乘试驾', '发票', '库存', '批售', '转化',
             '市占', '交车', '到店', '展厅客流', '库存', '销售', '实时数据']
# aftersales_kpis = ['配件批售', '附件批售', '配附件', '进站台次', '经销商产值', '配件直销']
aftersales_kpis = ['保有基盘', '活跃基盘', '进站台次', '售后产值', '单台次产值', 
                   '售后线索量', '响应量', '响应率', '进站量', '进站率', '售后满意度CEM', 
                   '配附件', '配件', '附件', '装车率', 'NORA',
                   '配件进度', '附件进度', '养护品进度', '专业件进度',
                   '售后转化漏斗', '配附件批售', '配件批售', '附件批售', '售后订单', '售后开票', '售后库存', '售后批直比', '售后开票']
voc_kpis = ['发布量', '提及量', '评价', '标签', '周报', '月报', '热度', '关注', '报告量', '份额', '情感', '主题', '意图分类', '渠道', '关键词']

prompt_business_classification_template = """# 角色
你是一个人工智能助手，请理解用户的问题，分析用户实际的问题。

# 业务
1.销售：{sales_kpis}
2.售后：{aftersales_kpis}
3.客户之声：{voc_kpis}
4.其他

# 要求
1.结合[历史问题]，理解用户的[当前问题]。
2.根据上面的理解，返回用户期望查询的一个或多个数据问题（对比类问题不拆分，直接作为一个完整问题返回；仅提取数据查询部分的问题）。
3.子问题的描述尽量与原意保持一致，且尽量完整，涉及到的指标描述不要改写。
4.判断子问题所属的业务类型，只返回有子问题的业务。
5.最终结果以JSON形式返回，不要返回```json```。
6.仅返回JSON格式，不要输出分析过程。

# 输出样例
{{"销售": ["问题1", "问题2", ......], "售后": ["问题3", "问题4", ......]}}

# 历史问题
{history_question}

# 当前问题
{current_question}

# 回答
"""


# prompt_business_classification_template = """# 角色
# 你是一个问题分类助手，请根据问题的业务类型进行判断。

# # 业务
# 1.销售：{sales_kpis}
# 2.售后：{aftersales_kpis}
# 3.其他

# # 要求
# 1.请根据用户的问题，判断所属的业务类型。
# 2.最终结果以JSON形式返回，不要返回```json```。
# 3.仅返回JSON格式，不要输出分析过程。

# # 输出样例
# {{"销售": ["问题1", "问题2", ......], "售后": ["问题3", "问题4", ......]}}

# # 问题列表
# {questions}

# # 回答
# """



# prompt_data_analysis_template = """# 角色
# 你是上汽大众的数据分析助手SVW Copilot，请根据提供的数据以及网页内容进行分析，并回答用户问题。

# # 要求
# 1.问题分析：
# 1.1.请思考用户[问题]需要从哪些方面进行分析。
# 1.2.然后结合[企业内数据]和[网页内容]进行进一步的分析。
# 1.3.如果[企业内数据]和[网页内容]中存在冲突的内容，请以[企业内数据]为准。
# 1.4.相对时间以基准日期{today}进行转化，如果问题中没有时间，默认为本月。
# 1.5.可以根据问题的要求，对[企业内数据]进行适当的计算，可以一步一步思考，保证计算结果的准确性。
# 2.最终回答
# 2.1.以markdown格式进行回答，结构清晰，不用重复需求，不用输出```markdown```。
# 2.2.最终结论如果引用了[企业内数据]和[网页内容]中的内容，在引用处标注[编号]，编号从1开始。
# 2.3.如果一个地方引用了多个参考内容，编号用逗号分隔（示例：[3,4]）。
# 3.参考内容
# 3.1.按顺序列出结论引用到的参考内容。
# 3.2.参考内容的编号要和结论中标注的编号一致。
# 3.3.参考内容列表：如果参考了[企业内数据]，请列出参考数据的大概内容描述；如果参考了[网页内容]，请列出参考的网页标题和链接。

# # 企业内数据
# {data}

# # 网页内容
# {web_contents}

# # 样例
# {{结论}}
# ### 参考内容
# [1] 内部数据xxx
# [2] 网页标题-xxx：https://xxxxx

# # 问题
# {question}

# # 回答
# """

prompt_data_analysis_with_chart_template = """# 角色
你是上汽大众的数据分析助手SVW Copilot，请根据提供的数据以及网页内容进行分析，并回答用户问题。

# 要求
1.问题分析：
1.1.请思考用户[问题]需要从哪些方面进行分析。
1.2.然后结合[企业内数据]和[网页内容]进行进一步的分析。
1.3.如果[企业内数据]和[网页内容]中存在冲突的内容，请以[企业内数据]为准。
1.4.相对时间以基准日期{today}进行转化，如果问题中没有时间，默认为本月。
1.5.可以根据问题的要求，对[企业内数据]进行适当的计算，可以一步一步思考，保证计算结果的准确性。
2.出图：
2.1.在输出回答过程中，需要在合适的地方放置出图数据。
2.2.图的数据仅使用[企业内数据]。
2.3.每个图使用下方格式进行输出：
```chart
<实际的图数据>
chart_end
```
样例：
```chart
{{"type": "chart", "title": "全国 SVW-VW 展厅客流", "chart": {{"chart_type": "mixed", "xAxis": ["2025W12", "2025W13", "2025W14", "2025W15", "2025W16", "2025W17"], "series": [{{"name": "展厅客流", "data": [63919, 62724, 65534, 54305, 55577, 7669], "type": "bar"}}, {{"name": "到店转化率", "percentage": false, "data": [34.46, 38.16, 30.02, 31.9, 31.97, 46.15], "type": "line"}}], "tab_list": [{{"type": "bar_line", "name": "柱状图和折线图"}}]}}}}
chart_end
```
2.4.不要改变[企业内数据]的内容，直接引用即可。

# 最终回答
1.以markdown格式进行回答，结构清晰，不用重复需求，不用输出```markdown```。
2.最终结论如果引用了[企业内数据]和[网页内容]中的内容，在引用处标注[编号]，编号从1开始。
3.如果一个地方引用了多个参考内容，编号用逗号分隔（示例：[3,4]）。
4.最终内容中不要出现数据中的英文名称，仅使用中文名称。

# 参考内容
1.按顺序列出结论引用到的参考内容。
2.参考内容的编号要和结论中标注的编号一致。
3.参考内容列表：如果参考了[企业内数据]，请列出参考数据的大概内容描述；如果参考了[网页内容]，请列出参考的网页标题和链接。

# 企业内数据
{data}

# 网页内容
{web_contents}

# 样例
{{结论}}
### 参考内容
[1] 内部数据xxx
[2] 网页标题-xxx：https://xxxxx

# 问题
{question}

# 回答
"""


prompt_data_analysis_report_template = """# 角色
你是上汽大众的数据分析助手SVW Copilot，请根据提供的数据以及网页内容进行分析，并使用html生成美观的分析报告。

# 要求
1.问题分析：
1.1.请思考用户[问题]需要从哪些方面进行分析。
1.2.然后结合[企业内数据]和[网页内容]进行进一步的分析。
1.3.如果[企业内数据]和[网页内容]中存在冲突的内容，请以[企业内数据]为准。
1.4.相对时间以基准日期{today}进行转化，如果问题中没有时间，默认为本月。
1.5.可以根据问题的要求，对[企业内数据]进行适当的计算，可以一步一步思考，保证计算结果的准确性。
2.最终回答
2.1.仅返回html代码，结构清晰，颜色使用和最终效果美观，禁止输出```html```。
2.1.1.交互式设计：采用响应式布局，适配手机屏幕尺寸，参照手机大小排版。
2.1.2.可视化增强：通过颜色编码（绿色表示增长/红色表示下降）提升数据可读性。
2.1.3.层级结构：核心指标→细分维度→结论的三段式分析框架。
2.1.4.数据溯源：所有结论标注企业内数据来源编号，点击编号可跳转至参考内容列表
2.2.最终结论如果引用了[企业内数据]和[网页内容]中的内容，在引用处标注[编号]，编号从1开始。
2.3.如果一个地方引用了多个参考内容，编号用逗号分隔（示例：[3,4]）。
3.参考内容
3.1.按顺序列出结论引用到的参考内容。
3.2.参考内容的编号要和结论中标注的编号一致。
3.3.参考内容列表：如果参考了[企业内数据]，请列出参考数据的大概内容描述；如果参考了[网页内容]，请列出参考的网页标题和链接。

# 企业内数据
{data}

# 网页内容
{web_contents}

# 问题
{question}

# 回答
"""