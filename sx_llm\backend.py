from fastapi import Fast<PERSON><PERSON>, WebSocket, status
from fastapi.staticfiles import StaticFiles
import uvicorn

app = FastAPI()

# 模拟合法token存储
VALID_TOKENS = {"test_token_123", "test_token_456"}

def validate_token(token: str) -> bool:
    """Token验证逻辑"""
    return token in VALID_TOKENS

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    # 获取协议头并验证token
    protocols = websocket.headers.get("sec-websocket-protocol", "").split(", ")
    
    if not protocols or not validate_token(protocols[0]):
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return

    await websocket.accept(subprotocol=None)
    
    try:
        while True:
            data = await websocket.receive_text()
            print(f"收到消息: {data}")
            await websocket.send_text(f"ECHO: {data}")
    except Exception as e:
        print(f"连接异常: {str(e)}")
    finally:
        await websocket.close()

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)