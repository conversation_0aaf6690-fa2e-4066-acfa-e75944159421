import asyncio
import aiohttp
import time
import csv
from datetime import datetime

async def test_conversation_chat(session, url, data, headers, conversation_number):
    all_content = []
    start_time = time.time()
    first_data_time = None
    last_data_time = None
    data['conversation_id'] = f'16579-1-{conversation_number}'
    
    # 为每个请求生成不同月份的问题
    month = (conversation_number % 12) + 1
    data['question'] = f'分析下24年{month}月的id3销量情况'

    try:
        # 发送 SSE 请求
        async with session.post(url, json=data, headers=headers, timeout=None) as response:
            response.raise_for_status()
            # 处理 SSE 事件流
            async for line in response.content.iter_any():
                if line:
                    # 解码字节数据为字符串
                    decoded_line = line.decode('utf-8')
                    if first_data_time is None:
                        first_data_time = time.time()
                    last_data_time = time.time()
                    try:
                        import json
                        data_obj = json.loads(decoded_line)
                        if 'content' in data_obj:
                            all_content.append(data_obj['content'])
                        if 'end' in data_obj and data_obj['end']:
                            break
                    except json.JSONDecodeError:
                        continue

    except aiohttp.ClientError as e:
        print(f"请求发生错误: {e}")

    # if all_content:
    #     print(''.join(all_content))

    return start_time, first_data_time, last_data_time

async def perform_load_test(url, data, headers, concurrency):
    results = []
    # 创建会话时设置较长的超时时间或禁用超时
    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=None)) as session:
        tasks = [test_conversation_chat(session, url, data.copy(), headers, i) for i in range(concurrency)]
        results = await asyncio.gather(*tasks)

    # 打印报告
    print("\n压测报告:")
    print("request, start_time, first_data_time, last_data_time, first_data-start_time, last_data-first_data")
    
    # 生成CSV文件名
    unix_time = int(time.time())
    csv_filename = f'conversation_chat_data_{unix_time}.csv'
    
    # 将结果写入CSV文件
    with open(csv_filename, 'w', newline='') as csvfile:
        csv_writer = csv.writer(csvfile)
        # 写入CSV标题行
        csv_writer.writerow(['request', 'start_time', 'first_data_time', 'last_data_time', 'first_data-start_time', 'last_data-first_data'])
        
        for idx, (start_time, first_data_time, last_data_time) in enumerate(results, start=1):
            if first_data_time and last_data_time:
                first_data_start = first_data_time - start_time
                last_data_first = last_data_time - first_data_time
                # 转换时间戳为指定格式
                start_time_str = datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S.%f')
                first_data_time_str = datetime.fromtimestamp(first_data_time).strftime('%Y-%m-%d %H:%M:%S.%f')
                last_data_time_str = datetime.fromtimestamp(last_data_time).strftime('%Y-%m-%d %H:%M:%S.%f')
                
                # 打印到控制台
                print(f"req-{idx}, {start_time_str}, {first_data_time_str}, {last_data_time_str}, {first_data_start}, {last_data_first}")
                
                # 写入到CSV文件
                csv_writer.writerow([f"req-{idx}", start_time_str, first_data_time_str, last_data_time_str, first_data_start, last_data_first])
    
    print(f"\n压测报告已保存至: {csv_filename}")

if __name__ == "__main__":
    # 示例：测试 /conversation/chat 接口
    chat_url = 'http://127.0.0.1:8000/conversation/chat'
    chat_headers = {
        'accesstoken': 'Bearer your_token',
        'Content-Type': 'application/json'
    }
    chat_data = {
        'user_id': '16579',
        'is_internet_search': False,
        'is_deep_thinking': False,
    }
    # 输入压测并发数
    concurrency = 10
    asyncio.run(perform_load_test(chat_url, chat_data, chat_headers, concurrency))