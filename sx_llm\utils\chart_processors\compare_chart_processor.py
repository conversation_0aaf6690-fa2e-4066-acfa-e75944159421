from utils.sx_dict import sx_kpi_name, sx_mixed_chart_name, sx_kpi_compare_dict, sx_display_name
from utils.chart_processors.base_chart_processor import BaseChartProcessor
# from sx_dict import sx_kpi_name, sx_mixed_chart_name, sx_kpi_compare_dict, sx_display_name
# from chart_processors.base_chart_processor import BaseChartProcessor


def data_filter(kpi: str, data: dict, data_dim: str) -> bool:
    return (
        (
            (
                (
                    (data["model"].count("-") == 1 and kpi not in ["销量", "市占"])
                    or (kpi in ["销量", "市占"])
                )
                and data_dim == "model"
            )
            or data_dim != "model"
        )
        and kpi in data["kpiValues"].keys()
        and data["location"] not in ["总计", "Total"]
        and data["model"] not in ["总计", "Total"]
    )


class CompareChartProcessor(BaseChartProcessor):
    """Compare chart processor"""

    def __init__(self, format_json) -> None:
        super().__init__(format_json)

    def chart_process(self, data, language):
        chart_list = []

        data_times = list(set([i["time"] for i in data]))

        title_display = sx_display_name[self.json_display][language]
        type = ["bar"]
        tab_list = [{'type': tmp_type,"name": sx_mixed_chart_name[tmp_type][language]} for tmp_type in type]
        kpi = [sx_kpi_compare_dict[kpi_temp]["value"] for kpi_temp in self.json_kpi if kpi_temp in sx_kpi_compare_dict.keys()]

        if not kpi:
            return []

        for kpi_temp in kpi:
            name_kpi = sx_kpi_name[kpi_temp][language]

            if len(data_times) == 1:

                if self.json_data_dim in ["model","location"]:
                    output_dict = {
                        i[self.json_data_dim]: i["kpiValues"][kpi_temp]
                        for i in data
                        if data_filter(kpi_temp, i, self.json_data_dim)
                    }

                else:
                    output_dict = {
                        i["location"]+'-'+i["model"]:i["kpiValues"][kpi_temp] 
                        for i in data 
                        if data_filter(kpi_temp, i, self.json_data_dim)
                        }

            else:
                output_dict = {
                    i["time"]+' '+i["location"]+'-'+i["model"]:i["kpiValues"][kpi_temp] 
                    for i in data 
                    if data_filter(kpi_temp, i, self.json_data_dim)
                    }

            output_dict = sorted(output_dict.items(),key=lambda x:x[1],reverse=True)
            xAxis, output_data = zip(*output_dict)
            output_data = list(output_data)
            xAxis = list(xAxis)


            title_model = "&".join(self.json_model)
            title_location = "&".join(self.json_location)
            name = title_location + ' ' + title_model + ' ' + name_kpi
            title = title_location + ' ' + title_model + ' ' + name_kpi + ' ' + title_display
            series = [{"name": name,"percentage": sx_kpi_compare_dict[kpi_temp]["percentage"],"data": output_data}]
            chart_list.append({"type": "chart","title": title,"chart": {"chart_type": "mixed","xAxis":xAxis,"series":series,"tab_list": tab_list}})
        return chart_list



if __name__ == "__main__":
    format_json = {'time': ['20240101-20240131', '20240301-20240331'], 'date_type': 'month', 'model': ['帕萨特', '途昂'], 'location': ['全国'], 'display': '对比', 'today': 'False', 'data_dim': 'all', 'not_total': 'False', 'template': ''}
    data = [{'time': '202401', 'location': '全国', 'model': 'Passat', 'brand': None, 'kpiValues': {'leadsNotDuplicateCnt': 601551}}, {'time': '202401', 'location': '全国', 'model': 'Teramont', 'brand': None, 'kpiValues': {'leadsNotDuplicateCnt': 164144}}, {'time': '202403', 'location': '全国', 'model': 'Passat', 'brand': None, 'kpiValues': {'leadsNotDuplicateCnt': 649357}}, {'time': '202403', 'location': '全国', 'model': 'Teramont', 'brand': None, 'kpiValues': {'leadsNotDuplicateCnt': 229741}}]
    language = '中文'
    chart_processor = CompareChartProcessor(format_json)
    chart_list = chart_processor.chart_process(data, language)
    print(chart_list)