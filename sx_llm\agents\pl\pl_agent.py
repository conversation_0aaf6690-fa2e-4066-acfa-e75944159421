import ast
import time
import json
import asyncio
import traceback
from typing import List, Dict

from agents.base_agent import BaseAgent
from agents.pl.prompt import *
from agents.pl.config import *
from agents.pl.dict import *
from utils.sx_request import AsyncApiClient
from utils.config import AFTERSALES_API
from utils.env_config import env
from utils.clean_json import extract_json_new



class PLAgent(BaseAgent):
    def __init__(self):
        super().__init__()  # 调用父类初始化
        self.kpis = []
        self.categorys_kpis = {}
        self.category_kpi_api = []
        self.base_url = AFTERSALES_API[env]
        self.data_all = []


    def get_profiles_from_manager(self, agent: BaseAgent):
        # 从管理类获取数据
        self.client = agent.client
        self.access_token = agent.access_token
        self.input_json = agent.input_json
        self.user_id = agent.user_id
        self.conversation_id = agent.conversation_id
        self.question = agent.question
        self.is_internet_search = agent.is_internet_search
        self.is_deep_thinking = agent.is_deep_thinking
        self.language = agent.language
        self.input_time = agent.input_time
        self.history_message = agent.history_message
        self.history_question = agent.history_question
        self.conversation_all = agent.conversation_all
        self.message_id = agent.message_id


    async def extract_kpis(self, questions: List[str]):
        # 指标提取
        # 样例：['配件批售金额', '配件批售完成']
        prompt = prompt_kpis_extract_template.format(questions = questions, kpi_all = kpi_all)
        self.save_log('售后agent-根据问题列表提取指标列表的prompt',prompt)
        content = [{"role": "user","content": prompt}]
        result = await self.process_llm.stream_response(content)
        self.save_log('售后agent-根据问题列表提取指标列表的结果',result)
        self.kpis = ast.literal_eval(result)


    def get_kpi_category(self):
        # 根据指标获取对应的类别
        # 样例：{'经营': ['配件批售金额', '配件批售完成'], '售后': ['售后订单数']}
        for kpi in self.kpis:
            category = kpi_to_category[kpi]
            if category not in self.categorys_kpis:
                self.categorys_kpis[category] = []
            self.categorys_kpis[category].append(kpi)

    
    def get_category_api(self):
        # 根据指标获取对应的API
        # 样例：[{'category': '经营', 'kpis': ['配件批售金额', '配件批售完成'], 'api': '/report/mobile/wechat/sv/afterSaleJyController/queryJyIndex'}]
        for category, kpi_list in self.categorys_kpis.items():
            self.category_kpi_api.append({
                'category': category,
                'kpis': kpi_list,
                'api': categorie_to_api[category]
            })
        

    async def extract_params(self, item):
        # 参数提取
        category = item['category']
        kpis = item['kpis']
        prompt_template = extract_paras_prompt_dict[category]
        prompt = prompt_template.format(question = self.question, today = self.today, kpis = kpis)
        content = [{"role": "user","content": prompt}]
        result = await self.process_llm.stream_response(content)
        params_list = ast.literal_eval(result)
        return params_list


    async def get_data(self, item):
        # 获取数据
        # 样例数据仅展示部分数据
        # 样例数据：[{'yearMonth': '202301', 'queryType': 'Y', 'pjPsAmount': '0', 'pjZhiXiaoCompare': '0'}, {'yearMonth': '202406', 'queryType': 'M', 'pjZhiXiaoCompare': '0'}]
        client = AsyncApiClient(
            base_url=self.base_url,
            auth_token=self.access_token,
        )
        
        print("开始执行并行请求...")
        start_time = time.time()
        
        params_list = await self.extract_params(item)
        
        kpis = item['kpis']
        kpis = [kpi_cn_to_en_dict[kpi] for kpi in kpis]
        
        if not params_list:
            return []
        else:
            tasks = [client.request(params=params,endpoint=item['api'],method='GET',debug=True,result_key='result') for params in params_list]
            results = await asyncio.gather(*tasks)
            
            # results = [{k: v for k,v in result.items() if k in kpis} for result in results]
            
            end_time = time.time()
            print(f"所有请求完成，总耗时: {end_time - start_time:.2f}秒")
            
            if results:
                return [{**params, **{kpi_en_to_cn_dict[k]: v for k,v in result.items() if k in kpis}} for params, result in zip(params_list, results)]
            else:
                return []

    async def process_data(self, data):
        # 数据处理函数，用于将接口返回的数据根据用户查询指标进行过滤，由于暂时不清楚用户要如何查询问题，暂时不实现
        pass


    async def decompose_question(self):
        if not self.is_deep_thinking:
            self.decompose_questions = [self.question]
            self.save_log('售后agent- 分解问题列表',self.decompose_questions)
            return
        # 分解用户输入的问题
        history_question = '\n'.join([str(index)+'.'+q for index, q in enumerate(self.history_question, start=1)])
        prompt = prompt_query_decomposition_add_history_template.format(current_question = self.question, history_question = history_question, kpi_all = kpi_all)
        content = [{"role": "user","content": prompt}]
        response = await self.process_llm.async_chat_by_token(content)
        
        is_thinking = True
        
        result = ''
        all_result = ''
        
        t1 = time.time()
        is_delete = False
        
        async for chunk in response:

            content = chunk.choices[0].delta.content
            content = content.replace('\n\n','\n')
            all_result += content
            
            
            if '<' in content:
                content_prefix = content.split('<')[0]
                is_delete = True
                self.thinking_content += content_prefix
                content_suffix = '<' + content.split('<')[-1]
                yield await self.format_sse_message(content=content_prefix, content_type="thinking")
                yield await self.format_sse_message(content='\n<delete>\n', content_type="thinking")
                yield await self.format_sse_message(content=content_suffix, content_type="thinking")
            elif '>' in content:
                is_thinking = False
                content_suffix = content.split(">")[-1]
                self.thinking_content += content_suffix
                result += content_suffix
                yield await self.format_sse_message(content=content, content_type="thinking")
            else:
                if not is_delete:
                    self.thinking_content += content
                yield await self.format_sse_message(content=content, content_type="thinking")
                if not is_thinking:
                    result += content
        
        t2 = time.time()
        self.save_log('售后agent- 分解问题回答',result,time_cost=t2-t1)
          
        yield await self.format_sse_message(content='\n</delete>\n', content_type="thinking")
        self.thinking_content += '\n\n**问题分解**\n'
        yield await self.format_sse_message(content='\n\n**问题分解**\n', content_type="thinking")
        
        
        self.decompose_questions = extract_json_new(result)['查询问题']
        # 增加原始问题到分解问题列表中
        self.decompose_questions.append(self.question)
        questions_return = '\n'.join(self.decompose_questions)
        yield await self.format_sse_message(content=f'{questions_return}\n', content_type="thinking")
        self.thinking_content += f'{questions_return}\n'
        
        self.save_log('售后agent- 添加了原始问题后的问题列表',self.decompose_questions,time_cost=t2-t1)
        
        self.save_log('售后agent- 分解问题- 完整回答',all_result,time_cost=t2-t1)


    async def get_internal_information(self, queue: asyncio.Queue):
        if self.is_deep_thinking:
            await queue.put(await self.format_sse_message('\n\n**内部数据检索**\n', content_type="thinking"))
            self.thinking_content += '\n\n**内部数据检索**\n'
            await queue.put(await self.format_sse_message('售后数据查询：\n', content_type="thinking"))
            self.thinking_content += '售后数据查询：\n'

        tasks = [self.get_data_use_mcp(question) for question in self.decompose_questions]
        results = await asyncio.gather(*tasks)
        # await queue.put(await self.format_sse_message(f"数据查询结果：{results}\n", content_type="thinking"))
        self.save_log('售后agent- 分解问题查询到的全部数据',results)
        for result in results:
            self.data_all.extend(result)
        
        await queue.put(None)  # 结束标记


    async def get_data_use_mcp(self, question: str) -> List[Dict]:
        print('开始调用mcp获取数据')
        from agents.aftersales.aftersales_mcp_client import MCPClient
        client = MCPClient(self.access_token)
        await client.connect_to_server()
        data = await client.process_query(question)
        return data
        

    def create_task_functions(self):
        task_functions = []
        if self.is_internet_search:
            task_functions.append(self.get_external_information)
        task_functions.append(self.get_internal_information)
        return task_functions


    async def generate_chart_data(self):
        # 生成符合前端数据格式的图表数据
        prompt = prompt_generate_chart_data_template.format(question = self.question, data = self.data_all, today = self.today)
        self.save_log('售后agent-生成图表数据的prompt',prompt)
        content = [{"role": "user","content": prompt}]
        result = await self.process_llm_0324.stream_response(content)
        result = result.replace('true','True')
        self.save_log('售后agent-生成图表数据的结果',result)
        self.charts = ast.literal_eval(result)
        

    async def event_stream_queue(self, queue: asyncio.Queue):
        try:
            async for item in self.decompose_question():
                await queue.put(item)
            task_functions = self.create_task_functions()
            if not self.is_deep_thinking:
                item = await self.format_sse_message(content=f"正在检索\"{self.question}\"相关数据... ...\n", content_type="text")
                await queue.put(item)
            async for item in self.ordered_stream(task_functions):
                await queue.put(item)
            # 生成图表数据
            await self.generate_chart_data()
            await queue.put(None)
        except asyncio.CancelledError:
            print("捕获到 CancelledError，开始清理")
            print("客户端已断开连接")
            raise  # 重新抛出以便框架处理
        except Exception as e:
            e_msg = traceback.format_exc()
            print(e_msg)
            self.save_log('售后agent-异常捕获',e_msg)
            await queue.put(await self.format_sse_message('对不起，由于问题过于复杂，我暂时无法回答', content_type="text", ended=True))
            print('发生异常：',e_msg )
        finally:
            print("SSE返回已结束")



if __name__ == '__main__':
    agent = PLAgent()
    agent.is_deep_thinking = True
    agent.is_internet_search = True
    agent.access_token = "NvGW4h-YTGQAFVoQKylHQ5PMIvjCvnk0"
    agent.question = '去年配件批售金额和完成情况'
    
    # asyncio.run(agent.extract_kpis())
    # 测试事件流
    async def test_stream():
        async for event in agent.event_stream():
            print("收到事件:", event)  # 去掉前面的"data: "

    # 运行测试
    asyncio.run(test_stream())
    
    # item = {'category': '经营', 'kpis': ['配件批售金额', '配件批售完成'], 'api': '/report/mobile/wechat/sv/afterSaleJyController/queryJyIndex'}
    # async def test():
    #     # api = 'http://10.122.31.36:8080/report/mobile/wechat/sv/afterSaleJyController/queryJyIndex'
    #     # data = {"yearMonth": "202301", "queryType": "M"}
    #     async for item in agent.get_internal_information(queue=None):
    #         print(item)
        
    # asyncio.run(test())
    # # api = 'http://10.122.31.36:8080/report/mobile/wechat/sv/afterSaleJyController/queryJyIndex'
    # # data = {"yearMonth": "202301", "queryType": "M"}
    # # result = agent.request_data(api,data)
    # # print(result)