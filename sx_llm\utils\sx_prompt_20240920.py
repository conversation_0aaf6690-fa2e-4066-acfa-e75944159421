# prompt_template_extract_args = """```xml
# <instruction>
# 请提取<query>中的参数，并以json格式返回，输出不包含其他内容或分析过程，不要返回```json```，只有JSON文本。请按照以下步骤完成任务：
# 1.time：
# 1.1.提取用户输入中出现的明确时间，忽略"每周"、"每月"、"每日"，并以列表(list)形式保存。
# 1.2.相对时间以基准日期{today}（{weekday}）进行转化，相对时间使用完整自然年、自然季度、自然月、自然周（周从星期一开始）。
# 1.3.识别时间范围的开始和结束时间，严格使用yyyymmdd-yyyymmdd的格式。
# 1.4.如果输入的时间信息无法解析或不存在，则返回一个空列表 []。
# 2.date_type：提取用户输入查询时间的类型，到天返回"day"，到周返回"week"，到月返回"month"，到年返回"year"; 如果信息不存在, 请使用 "" 作为值。
# 3.location：提取用户输入中出现的大区、省份、城市，无需进行分析和推理，并补充"大区"、"省"、"市"关键字，多值字段，用中括号 list 结构保存，如["全国"]，["华东大区"]，["北京市","上海市"]；无相关信息，则使用 ["全国"] 作为值；"各大区" 默认返回 ["全国"]。
# 4.model：提取用户输入中的车辆品牌或车辆类别或车型名称, 多值字段, 用中括号 list 结构保存；如果信息不存在, 请使用 [] 作为值
# 5.display：提取用户输入中隐含的数据展示形式，单值字段，仅包括"趋势"、"分布"、"排名"、"对比"、"同环比（yoy and mom）"、"同比（mom）"、"环比（yoy）"、"总计"，无需添加其他不在范围的词汇; 如果信息不存在, 请使用 "" 作为值。
# 6.data_dim：
# 6.1."by SVR"表示"按大区维度下钻"的含义。
# 6.3.如果用户输入明确说明查询更下一级地区维度或出现"各大区（by region）"、"各省份（by province）"、"各城市（by city）"、"哪个大区（which region）"、"哪个省份（which province）"、"哪个城市（which city）"，返回"location"；如果用户输入明确说明查询更下一级车型维度或出现"各车型（by model）"、"哪个车型（which model）"的含义，返回"model"。
# 6.3.如果用户输入没有明确说明按地区或车型维度下钻，即使含有地区或车型相关信息，也不返回"location"或"model"，只返回"all"。
# 7.today：如果用户输入明确提到今日数据或实时数据查询，返回"True"，否则，返回"False"。
# 8.not_total：如果用户输入需要每月、每周、每日的数据，返回"True"，否则，返回"False"。
# 9.template：如果用户明确提到使用模板查询进行查询，提取模板的名称，单值字段，字符串; 如果信息不存在, 请使用 "" 作为值。
# </instruction>

# <input>
# <query>{question}</query>
# </input>

# <output>
# {{
# "time": [<time>],
# "date_type": "<date_type>",
# "model": [<model>],
# "location": [<location>],
# "display": "<display>",
# "today": "<today>",
# "data_dim": "<data_dim>",
# "not_total": "<not_total>",
# "template": "<template>"
# }}
# </output>
# ```"""