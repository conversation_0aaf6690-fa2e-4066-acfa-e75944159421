# import asyncio
# import websocket
# import threading



# def on_message(ws, message):
#     print(message)

# def on_error(ws, error):
#     print(error)
#     ws.close()

# def on_close(ws, close_status_code, close_msg):
#     print("### closed ###")

# def on_open(ws):
#     print("Opened connection")
#     # 启动一个新线程来处理用户输入
#     input_thread = threading.Thread(target=handle_input, args=(ws,))
#     input_thread.daemon = True
#     input_thread.start()

# def on_ping(ws, message):
#     # print("Got a ping! A pong reply has already been automatically sent.")
#     pass

# def on_pong(ws, message):
#     # print("Got a pong! No need to respond")
#     pass

# def handle_input(ws):
#     while True:
#         question = input("Enter a message to send (or type 'exit' to close): ")
#         if question == "exit":
#             ws.close()
#             print("WebSocket connection closed.")

#         if question.strip() == "":
#             continue

#         messageBody = 'hello server!'
#         ws.send(messageBody)




# if __name__ == "__main__":
#     uri = "ws://localhost:9000/api/V1/chat/ws"  # 根据你的实际地址修改

#     websocket.enableTrace(False)
#     ws = websocket.WebSocketApp(uri,
#                               on_open=on_open,
#                               on_message=on_message,
#                               on_error=on_error,
#                               on_close=on_close,
#                               on_ping=on_ping,
#                               on_pong=on_pong)
#     ws.run_forever()




import websockets
import asyncio
import sys

async def test_connection(token):
    try:
        async with websockets.connect(
            "ws://localhost:8000/ws",
            subprotocols=[token]  # 使用标准协议参数
        ) as websocket:
            print("连接成功！输入消息开始对话（输入exit退出）")
            while True:
                message = input("> ")
                if message.lower() == "exit":
                    break
                await websocket.send(message)
                response = await websocket.recv()
                print(f"服务器响应: {response}")
    except Exception as e:
        print(f"连接失败: {str(e)}")

if __name__ == "__main__":
    token = sys.argv[1] if len(sys.argv) > 1 else "invalid_token"
    asyncio.run(test_connection(token))