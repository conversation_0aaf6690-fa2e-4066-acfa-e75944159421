import gradio as gr
import sys
import os
from fastapi import Body, FastAPI, File, Form, Query, UploadFile, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import smtplib
from email.mime.text import MIMEText
from langchain.embeddings.huggingface import HuggingFaceEmbeddings
from transformers import AutoTokenizer, AutoModel
from typing import Any, List, Mapping, Optional
from langchain.callbacks.manager import CallbackManagerForLLMRun
from langchain.llms.base import LLM
from langchain.text_splitter import CharacterTextSplitter
from sx_text_splitter import SXCharacterTextSplitter
import uvicorn
from langchain.retrievers.multi_query import MultiQueryRetriever
from langchain.memory import ConversationBufferMemory, ConversationBufferWindowMemory
import time
from langchain.prompts.prompt import PromptTemplate
from langchain.vectorstores import FAISS
from langchain.chains import RetrievalQA
from langchain import <PERSON><PERSON>hai<PERSON>
from langchain.document_loaders import TextLoader
from langchain.chains import <PERSON><PERSON>hain
from langchain.chains import create_extraction_chain, create_extraction_chain_pydantic
from langchain.prompts import ChatPromptTemplate
from llm import *
from sx_prompt import *
import os
import nltk
from utils import clean_json, sx_index
nltk.data.path = [os.path.join(os.path.dirname(__file__), "nltk_data")] + nltk.data.path

# import logging
# logging.basicConfig()
# logging.getLogger("langchain.retrievers.multi_query").setLevel(logging.DEBUG)


import warnings
warnings.filterwarnings('ignore')



llm = WenxinLLM()

# input_text = "上个季度ID3和帕萨特销量"
# chain_warroom = LLMChain(llm=llm, prompt=prompt_V8)
# response_json = chain_warroom.run(input_text)
# formatted_json = extract_and_clean_json(response_json)
# #提取formatted_json中的model_name_cn
# model_names = json.loads(formatted_json)["model_name_cn"]

model_names = ["id3","帕三特"]

file_path = "D:/code/gpt/warroom_llm/sx_llm/data/model_name_cn.txt"

model_names = [clean_json.clean_model_name_cn(i) for i in model_names]

print(model_names)








# response = knowledge_chain({"query": input_text})

# endpoint_url = ("http://127.0.0.1:9000")
# llm = ChatGLM(endpoint_url=endpoint_url,temperature = 0)
# llm = ChatGLM()




