

kpi_cn_to_en_dict = {
    "配件批售金额": "pjPsAmount",
    "配件批售完成": "pjPsAccomplish",
    "配件批售目标": "pjPsTarget",
    "配件批售同比": "pjPsCompare",
    "附件批售金额": "fjPsAmount",
    "附件批售完成": "fjPsAccomplish",
    "附件批售目标": "fjPsTarget",
    "附件批售同比": "fjPsCompare",
    "配附件利润": "pfjPsAmount",
    "配附件完成": "pfjPsAccomplish",
    "配附件目标": "pfjPsTarget",
    "配附件同比": "pfjPsCompare",
    "进站台次": "jzTaiCi",
    "进站台次同比": "jzTaiCiCompare",
    "经销商产值": "sstChanZhi",
    "经销商产值同比": "sstChanZhiCompare",
    "配件直销": "pjZhiXiao",
    "配件直销同比": "pjZhiXiaoCompare"
}

kpi_en_to_cn_dict = {value: key for key, value in kpi_cn_to_en_dict.items()}

kpi_cn_list = list(kpi_cn_to_en_dict.keys())
kpi_en_list = list(kpi_cn_to_en_dict.values())

base_en_to_cn_dict = {
    "type": "车龄段",
    "total": "全部车龄",
    "I0": "0-1年车龄",
    "IA": "1-2年车龄",
    "IB": "3-4年车龄",
    "II": "5-7年车龄",
    "III": "8年以上车龄",
    "retain": "保有基盘",
    "active": "活跃基盘",
    "amount": "基盘数量",
    "hbRate": "基盘数量环比",
    "tbRate": "基盘数量同比"
}

business_en_to_cn_dict = {
    "type": "进站类型",
    "total": "全部进站类型",
    "sb": "首保",
    "cb": "常规保养",
    "sg": "事故车",
    "ybwx": "一般维修",
    "zhsp": "召回索赔",
    "name": "指标",
    "taiCi": "进站台次",
    "chanZhi": "售后产值",
    "amount": "数值",
    "hbRate": "环比",
    "tbRate": "同比"
}

sale_en_to_cn_dict = {
    "name": "配附件类型",
    "total": "全部配附件",
    "parts": "配件",
    "accessory": "附件",
    "amount": "金额（带单位）",
    "hbRate": "金额环比",
    "tbRate": "金额同比"
}

wholesale_en_to_cn_dict = {
    "name": "配附件类型",
    "total": "全部配附件",
    "parts": "配件",
    "accessory": "附件",
    "amount": "金额（带单位）",
    "hbRate": "金额环比",
    "tbRate": "金额同比",
    "progress": "任务完成率"
}

sale_detail_en_to_cn_dict = {
    "name": "指标",
    "balance": "结算直销",
    "outbound": "出库直销",
    "amount": "金额（带单位）",
    "hbRate": "金额环比",
    "tbRate": "金额同比"
}

sale_pfj_en_to_cn_dict = {
    "type": "结算类型",
    "name": "指标",
    "directSales": "直销",
    "workshop": "车间直销",
    "nora": "nora(批售直销+零售直销)",
    "singleCost": "单车成本",
    "amount": "金额（带单位）",
    "hbRate": "金额环比",
    "tbRate": "金额同比",
    "balance": "结算直销",
    "outbound": "出库直销"
}

load_en_to_cn_dict = {
    "directNum": "直销数量",
    "rate": "装车率",
    "name": "重点零件名称"
}

complete_en_to_cn_dict = {
    "rate": "完成进度",
    "name": "指标",
    "time": "时间进度",
    "partsAndAccessory": "配附件进度",
    "parts": "配件进度",
    "accessory": "附件进度",
    "maintain": "养护件进度",
    "professional": "专业件进度"
}

wholesale_complete_en_to_cn_dict = {
    "value": "数值",
    "name": "指标",
    "target": "目标",
    "order": "订单",
    "confirmedOrder": "确认订单",
    "invoice": "开票",
    "dayTarget": "日均目标",
    "dayOrder": "日均订单",
    "dayConfirmedOrder": "日均确认订单",
    "dayInvoice": "日均开票",
    "orderProgress": "订单进度",
    "confirmedOrderProgress": "确认订单进度",
    "invoiceProgress": "开票进度",
    "type": "配附件类型",
    "total": "全部配附件",
    "parts": "配件",
    "accessory": "附件"
}

stock_en_to_cn_dict = {
    "value": "指标值",
    "name": "指标",
    "batchDirectRatio": "批直比",
    "inventoryAmount": "库存规模",
    "inventoryEquivalent": "库存当量"
}

wholesale_type_en_to_cn_dict = {
    "inventoryAmount": "库存规模",
    "orderAmount": "订单",
    "batchDirectRatio": "批直比",
    "inventoryEquivalent": "库存当量",
    "confirmedOrderAmount": "确认订单",
    "invoice": "开票",
    "products": "产品分类名称"
}

funnel_en_to_cn_dict = {
    "orderAmount": "订单数量",
    "orderToconfirmed": "订单—确认订单转化率",
    "confirmedOrderAmount": "确认订单数量",
    "invoice": "开票数量",
    "confirmedOrderToInvoice": "确认订单--开票转化率"
}

business_detail_en_to_cn_dict = {
    "amount": "数值带单位",
    "name": "指标",
    "stationNumber": "进站台次",
    "output": "售后产值",
    "single": "单车产值",
    "hbRate": "环比",
    "tbRate": "同比"
}

business_type_en_to_cn_dict = {
    "amount": "数值带单位",
    "type": "进站类型",
    "hbRate": "环比",
    "tbRate": "同比",
    "0": "台次",
    "1": "产值",
    "2": "单台次产值"
}

business_region_en_to_cn_dict = {
    "amount": "数值带单位",
    "type": "区域名称",
    "hbRate": "环比",
    "tbRate": "同比",
    "0": "台次",
    "1": "产值",
    "2": "单台次产值"
}

business_month_en_to_cn_dict = {
    "amount": "数值带单位",
    "month": "月份",
    "0": "台次",
    "1": "产值",
    "2": "单台次产值"
}

service_en_to_cn_dict = {
    "getIn": "进站量",
    "getInRate": "进展率",
    "response": "响应量",
    "responseRate": "响应率",
    "leadsNum": "线索量"
}

cem_en_to_cn_dict = {
    "rate": "售后满意度评价CEM成绩同比",
    "hbRate": "售后满意度评价CEM成绩环比",
    "cem": "售后满意度评价CEM成绩"
}

