

from utils.chart_processors.base_chart_processor import BaseChartProcessor
from utils.chart_processors.trend_chart_processor import TrendChartProcessor
from utils.chart_processors.table_chart_processor import TableChartProcessor
from utils.chart_processors.funnel_chart_processor import FunnelChartProcessor
from utils.chart_processors.compare_chart_processor import CompareChartProcessor
from utils.chart_processors.distribution_chart_processor import DistributionChartProcessor
from utils.chart_processors.target_completion_chart_processor import TargetCompletionChartProcessor



class ChartProcessorFactory:
    """工厂类，用于创建图表处理器"""

    def __init__(self, display: str, format_json: dict) -> None:
        self.chart_processor = self._init_chart_processor(display, format_json)
    
    def _init_chart_processor(self, display: str, format_json: dict) -> BaseChartProcessor:
        """根据数据展示类型选择图表处理器"""
        
        if display == '漏斗':
            return FunnelChartProcessor(format_json)
        elif display == '分布':
            return DistributionChartProcessor(format_json)
        elif display == '趋势':
            return TrendChartProcessor(format_json)
        elif display == '同环比':
            return TableChartProcessor(format_json)
        elif display in ['排名','对比']:
            return CompareChartProcessor(format_json)
        elif display == '目标':
            return TargetCompletionChartProcessor(format_json)
        else:
            raise ValueError(f"Unsupported display type: {display}")