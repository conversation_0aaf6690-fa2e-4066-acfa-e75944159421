from abc import ABC, abstractmethod
from langchain_huggingface import Hugging<PERSON>aceEmbeddings

from config import Config


class EmbeddingModel(ABC):
    pass

class HuggingFaceEmbeddingModel(EmbeddingModel):
    model_class = "HuggingFaceEmbedding"
    def __init__(self, model_name=None):
        self.model_name = model_name or self.get_default_model_name()
        self.model_path = self.get_model_path(self.model_name)
        print(f"Loaded HuggingFace model '{self.model_name}' from {self.model_path}")

    @classmethod
    def get_model_path(cls, model_name):
        models = cls.list_available_models()
        if model_name not in models:
            raise ValueError(f"Model '{model_name}' not found in configuration")
        return models[model_name]

    @classmethod
    def get_default_model_name(cls):
        model_name = Config.get('default_huggingface_model_name')
        if model_name is None:
            raise ValueError("Default {} model is not configured".format(cls.model_class))
        return model_name

    @classmethod
    def list_available_models(cls):
        return Config.get('huggingface_models', {})
    
    def get_model_isntance(self):
        return HuggingFaceEmbeddings(model_name=self.model_path)


class EmbeddingFactory:
    @staticmethod
    def create_embedding_model(model_type = 'huggingface'):
        if model_type == 'huggingface':
            return HuggingFaceEmbeddingModel()
        # elif model_type == 'baidu':
        #     return BaiduEmbeddingModel(**kwargs)
        # elif model_type == 'openai':
        #     return OpenAIEmbeddingModel(**kwargs)
        else:
            raise ValueError(f"Unsupported model type: {model_type}")


if __name__ == "__main__":
    embedding_factory = EmbeddingFactory()
    embedding_model = embedding_factory.create_embedding_model()
    embedding_model_instance = embedding_model.get_model_isntance()












