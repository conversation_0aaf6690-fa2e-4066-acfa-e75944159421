#     "uri": "http://c-438073142a79b07d-internal.milvus.aliyuncs.com:19530",  # 目标Milvus的URI
#     "token": "root:ecTtwv43sVzmQE^R",  # 目标Milvus的Token（如果需要认证）
#     "database": "SVWServiceTest",  # 目标数据库名称（留空使用默认数据库）
    
    
    
#     "uri": "http://c-5a20f516dce20a9b-internal.milvus.aliyuncs.com:19530",  # 目标Milvus的URI
#     "token": "root:DV+@S3#6XUZ*Y2$v",  # 目标Milvus的Token（如果需要认证）
#     "database": "SVWServiceTest",  # 目标数据库名称（留空使用默认数据库）
    
# a = {"aaa": True}



# import openai
# import asyncio

# async def chat(content):
#     client = openai.AsyncOpenAI(
#             api_key='sk-2945b7c00231403c834625158531b332', 
#             base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
#             )
#     response = await client.chat.completions.create(
#                 model='qwen3-235b-a22b',
#                 messages=content,
#                 stream=True,
#                 temperature=0.1,
#                 extra_body={"enable_thinking": False}
#             )
#     ## 火山流式输出
#     result = ''
#     async for chunk in response:
#         content = chunk.choices[0].delta.content
#         result += content
#         print(content, end='')
    # reasoning_content = ""  # 完整思考过程
    # answer_content = ""  # 完整回复
    # is_answering = False  # 是否进入回复阶段
    # print("\n" + "=" * 20 + "思考过程" + "=" * 20 + "\n")

    # async for chunk in response:
    #     if not chunk.choices:
    #         print("\nUsage:")
    #         print(chunk.usage)
    #         continue

    #     delta = chunk.choices[0].delta

    #     # 只收集思考内容
    #     if hasattr(delta, "reasoning_content") and delta.reasoning_content is not None:
    #         if not is_answering:
    #             print(delta.reasoning_content, end="", flush=True)
    #         reasoning_content += delta.reasoning_content

    #     # 收到content，开始进行回复
    #     if hasattr(delta, "content") and delta.content:
    #         if not is_answering:
    #             print("\n" + "=" * 20 + "完整回复" + "=" * 20 + "\n")
    #             is_answering = True
    #         print(delta.content, end="", flush=True)
    #         answer_content += delta.content
    # return result
# content = [{"role": "user", "content": '你是谁'}]
# asyncio.run(chat(content))


# import requests
# from utils.env_config import open_api_url

# def get_secret_keys():
#     url = f"http://{open_api_url}/account-config/v1/private/account/deepSeek"
#     headers = {"Content-Type": "application/json"}
#     result = requests.get(url, headers=headers).json()
#     secret_keys = result["data"]
#     return secret_keys

# results = get_secret_keys()
# print(results)


# [深度思考] -> [业务分类] -> [业务内问题拆解]

# [是否需要提前进行问题拆解]



# a = {"history":["{\"user_id\": \"****************\", \"conversation_id\": \"89d5820b-a887-464d-83d9-b2a229ed0af3\", \"role\": \"user\", \"content\": \"帕萨特今年的评价怎么样\", \"time\": \"2025-06-27 15:41:27\", \"order\": 1}","{\"user_id\": \"****************\", \"conversation_id\": \"89d5820b-a887-464d-83d9-b2a229ed0af3\", \"message_id\": \"2ac1c999-e7b7-4145-9254-e919aa1f6b94\", \"role\": \"assistant\", \"thinking_content\": \"正在检索\\\"帕萨特今年的评价怎么样\\\"相关数据... ...\\n# 结论总结\\n### 1. 帕萨特2025年评价概况\\n帕萨特2025年1月至6月的客户评价总体呈现积极态势，总提及量达767,231次。其中正面评价占比最高的是\\\"优惠促销力度\\\"标签，提及量达151,536次，全部为正面评价。\\n\\n### 2. 主要正面评价点\\n- 优惠促销力度：151,536次正面评价，占比19.75%\\n- 金融贷款服务相关：97,070次中性咨询\\n- 置换服务相关：75,377次中性咨询\\n\\n### 3. 价格相关评价\\n价格相关讨论主要集中在：\\n- 到手价：97,559次中性咨询\\n- 裸车价：71,879次中性提及和70,573次中性咨询\\n- 官方指导价：63,299次中性提及\\n\\n### 4. 服务相关评价\\n销售服务方面：\\n- 金融贷款服务相关：97,070次中性咨询和47,172次中性提及\\n- 置换服务相关：75,377次中性咨询和51,494次中性提及\\n\\n### 5. 情感分布\\n- 正面评价占比：19.75%（主要集中在优惠促销力度）\\n- 中性评价占比：80.25%（主要为价格和服务咨询）\\n- 负面评价：0次\\n\\n### 6. 建议措施\\n1. 继续保持优惠促销策略，这是获得客户正面评价的主要来源\\n2. 加强对金融贷款和置换服务的宣传和透明度，提升客户体验\\n3. 关注裸车价和官方指导价的客户咨询，可考虑优化价格沟通策略\\n4. 虽然目前没有负面评价，但仍需持续监测客户反馈\\n\", \"content\": \"相关数据检索中... ...\\n客户之声：['帕萨特今年的评价情感']\\n正在检索\\\"帕萨特今年的评价怎么样\\\"相关数据... ...\\n# 结论总结\\n### 1. 帕萨特2025年评价概况\\n帕萨特2025年1月至6月的客户评价总体呈现积极态势，总提及量达767,231次。其中正面评价占比最高的是\\\"优惠促销力度\\\"标签，提及量达151,536次，全部为正面评价。\\n\\n### 2. 主要正面评价点\\n- 优惠促销力度：151,536次正面评价，占比19.75%\\n- 金融贷款服务相关：97,070次中性咨询\\n- 置换服务相关：75,377次中性咨询\\n\\n### 3. 价格相关评价\\n价格相关讨论主要集中在：\\n- 到手价：97,559次中性咨询\\n- 裸车价：71,879次中性提及和70,573次中性咨询\\n- 官方指导价：63,299次中性提及\\n\\n### 4. 服务相关评价\\n销售服务方面：\\n- 金融贷款服务相关：97,070次中性咨询和47,172次中性提及\\n- 置换服务相关：75,377次中性咨询和51,494次中性提及\\n\\n### 5. 情感分布\\n- 正面评价占比：19.75%（主要集中在优惠促销力度）\\n- 中性评价占比：80.25%（主要为价格和服务咨询）\\n- 负面评价：0次\\n\\n### 6. 建议措施\\n1. 继续保持优惠促销策略，这是获得客户正面评价的主要来源\\n2. 加强对金融贷款和置换服务的宣传和透明度，提升客户体验\\n3. 关注裸车价和官方指导价的客户咨询，可考虑优化价格沟通策略\\n4. 虽然目前没有负面评价，但仍需持续监测客户反馈\\n\", \"charts\": [{\"type\": \"chart\", \"title\": \"帕萨特2025年情感评价分布\", \"chart\": {\"chart_type\": \"mixed\", \"series\": [{\"name\": \"情感分布\", \"percentage\": false, \"data\": [151536, 41272, 0]}], \"xAxis\": [\"正面\", \"中性\", \"负面\"], \"tab_list\": [{\"type\": \"pie\", \"name\": \"饼图\"}]}}], \"thinking_enabled\": false, \"time\": \"2025-06-27 15:41:27\", \"order\": 2, \"feedback_type\": null, \"feedback_content\": null}","{\"user_id\": \"****************\", \"conversation_id\": \"89d5820b-a887-464d-83d9-b2a229ed0af3\", \"role\": \"user\", \"content\": \"去年呢\", \"time\": \"2025-06-27 15:42:04\", \"order\": 3}","{\"user_id\": \"****************\", \"conversation_id\": \"89d5820b-a887-464d-83d9-b2a229ed0af3\", \"message_id\": \"0a28158e-21be-4156-817c-03d1b507d927\", \"role\": \"assistant\", \"thinking_content\": \"正在检索\\\"去年呢\\\"相关数据... ...\\n\\n\", \"content\": \"相关数据检索中... ...\\n客户之声：['帕萨特去年的评价怎么样']\\n正在检索\\\"去年呢\\\"相关数据... ...\\n\\n\", \"charts\": [], \"thinking_enabled\": false, \"time\": \"2025-06-27 15:42:04\", \"order\": 4, \"feedback_type\": null, \"feedback_content\": null}"]}
# import json
# history = a['history']
# history = [json.loads(item) for item in history]
# print(history)



import base62
import uuid

# # --- 编码 ---
# number_to_encode = 123456789
# encoded_str = pybase62.encode(number_to_encode)
# print(f"数字 '{number_to_encode}' 使用pybase62编码后 -> '{encoded_str}'")

# # --- 解码 ---
# decoded_number = pybase62.decode(encoded_str)
# print(f"字符串 '{encoded_str}' 使用pybase62解码后 -> '{decoded_number}'")

# print("-" * 20)

# # --- 编码UUID ---
# id = uuid.uuid4()
# uuid_as_int = id.int
# encoded_uuid = base62.encode(uuid_as_int)
# print(f"UUID '{id}'")
# print(f"UUID整数 '{uuid_as_int}'")
# print(f"编码后 -> '{encoded_uuid}'")
# print(type(encoded_uuid))

open_api_url = "172.20.242.32:28771"
import requests

def get_secret_keys():
    url = f"http://{open_api_url}/account-config/v1/private/account/deepSeek"
    headers = {"Content-Type": "application/json"}
    result = requests.get(url, headers=headers).json()
    secret_keys = result["data"]
    return secret_keys

result = get_secret_keys()
print(result)