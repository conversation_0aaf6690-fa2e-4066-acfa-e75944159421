#!/bin/bash

# 定义常用路径变量
WECHAT_BASE="/data/app/svwcopilot-wechat"
COPILOT_BASE="/data/app/svwcopilot"
WECHAT_EMBED="$WECHAT_BASE/embeddings"
COPILOT_EMBED="$COPILOT_BASE/embeddings"
LOG_FILE="$COPILOT_BASE/backup_and_sync.log"

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 检查并重启/启动Flask服务的函数
check_and_restart_service() {
    if pgrep -f "flask --app server run --host 0.0.0.0 --port 7778" > /dev/null; then
        log_message "重启Flask服务..."
        pkill -f "flask --app server run --host 0.0.0.0 --port 7778"
        sleep 2
    fi
    
    cd $WECHAT_BASE
    nohup $WECHAT_BASE/.venv/bin/python $WECHAT_BASE/.venv/bin/flask --app server run --host 0.0.0.0 --port 7778 >> "$LOG_FILE" 2>&1 &
    log_message "Flask服务已启动"
}

# 执行脚本函数
run_script() {
    local script_path="$1"
    local script_name=$(basename "$script_path")
    
    if [ -f "$script_path" ]; then
        log_message "开始执行 $script_name"
        bash "$script_path" >> "$LOG_FILE" 2>&1
        if [ $? -eq 0 ]; then
            log_message "$script_name 执行成功"
        else
            log_message "错误: $script_name 执行失败"
        fi
    else
        log_message "错误: 脚本 $script_path 不存在"
    fi
}

# 主要处理逻辑
main() {
    log_message "开始执行备份和同步操作"

    # 1. 删除已存在的备份文件夹
    if [ -d "$WECHAT_EMBED/EmpZero-bk" ]; then
        rm -rf "$WECHAT_EMBED/EmpZero-bk"
        log_message "已删除旧的备份文件夹"
    fi

    # 2. 重命名当前文件夹为备份
    if [ -d "$WECHAT_EMBED/EmpZero" ]; then
        mv "$WECHAT_EMBED/EmpZero" "$WECHAT_EMBED/EmpZero-bk"
        log_message "已创建新的备份"
    fi

    # 3. 检查并重启服务
    check_and_restart_service

    # 4. 每30分钟检查一次新文件夹是否生成
    log_message "开始监控新文件夹生成..."
    while true; do
        if [ -d "$WECHAT_EMBED/EmpZero" ]; then
            log_message "检测到新的EmpZero文件夹已生成"
            break
        fi
        sleep 1800  # 休眠30分钟
    done

    log_message "测试已完成"
}

# 执行主函数
main >> "$LOG_FILE" 2>&1 