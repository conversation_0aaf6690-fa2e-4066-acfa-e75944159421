import sys
import os
import json
import requests
from mcp.server.fastmcp import FastMCP


# 添加上上层目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))


from utils.config import AFTERSALES_API
from utils.env_config import env

from dict import *

base_url = AFTERSALES_API[env]

# 创建 MCP 服务器实例
mcp = FastMCP("GetAftersalesData")


@mcp.tool()
def get_region_code(token: str = None):
    """
    获取全部大区代码
    :param token: 可以忽略此参数
    :return: 大区名称和大区代码
    """
    url = f'{base_url}/report/mobile/wechat/sv/FjjxcManageController/getRegionList'
    headers = {
        'Authorization': token,
        'Content-Type': 'application/json'
    }
    response = requests.get(url, headers=headers)
    return json.loads(response.text)['result']

@mcp.tool()
def get_province_code(token: str = None):
    """
    获取全部省份代码
    :param token: 可以忽略此参数
    :return: 省份名称和省份代码
    """
    url = f'{base_url}/report/mobile/wechat/sv/FjjxcManageController/getProvinceList'
    headers = {
        'Authorization': token,
        'Content-Type': 'application/json'
    }
    response = requests.get(url, headers=headers)
    return json.loads(response.text)['result']

@mcp.tool()
def get_agency_code(regionCode: str, token: str = None):
    """
    根据大区代码获取全部营销小组代码
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :return: 营销小组名称和营销小组代码
    """
    url = f'{base_url}/report/mobile/wechat/sv/FjjxcManageController/getAgencyList'
    headers = {
        'Authorization': token,
        'Content-Type': 'application/json'
    }
    params = {
        "regionCode": regionCode
    }
    response = requests.get(url, params=params, headers=headers)
    return json.loads(response.text)['result']

@mcp.tool()
def get_business_manager_code(regionCode: str, agencyCode: str, token: str = None):
    """
    获取全部商务经理代码
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :return: 商务经理名称和商务经理代码
    """
    url = f'{base_url}/report/mobile/wechat/sv/FjjxcManageController/getBusinessManagerList'
    headers = {
        'Authorization': token,
        'Content-Type': 'application/json'
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode
    }
    response = requests.get(url, params=params, headers=headers)
    return json.loads(response.text)['result']

@mcp.tool()
def get_city_code(provinceCode: str, token: str = None):
    """
    获取全部城市代码
    :param token: 可以忽略此参数
    :param provinceCode: 省份代码
    :return: 城市名称和城市代码
    """
    url = f'{base_url}/report/mobile/wechat/sv/FjjxcManageController/getCityList'
    headers = {
        'Authorization': token,
        'Content-Type': 'application/json'
    }
    params = {
        "provinceCode": provinceCode
    }
    response = requests.get(url, params=params, headers=headers)
    return json.loads(response.text)['result']

@mcp.tool()
def get_dealer_code(regionCode: str = None, agencyCode: str = None, businessManagerNum: str = None, provinceCode: str = None, cityCode: str = None, token: str = None):
    """
    获取全部经销商代码或指定经销商代码
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :return: 经销商名称和经销商代码
    """
    url = f'{base_url}/report/mobile/wechat/sv/FjjxcManageController/getDealerList'
    headers = {
        'Authorization': token,
        'Content-Type': 'application/json'
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "provinceCode": provinceCode,
        "cityCode": cityCode
    }
    response = requests.get(url, params=params, headers=headers)
    return json.loads(response.text)['result']


@mcp.tool()
def get_base_data(regionCode: str = None, agencyCode: str = None, businessManagerNum: str = None,
                  dealerCode: str = None, provinceCode: str = None, cityCode: str = None, dimension: str = None,
                  dateValue: str = None, endDateValue: str = None, token: str = None):
    """
    获取售后基盘数据，包括不同车龄段、不同基盘类型的基盘数量和同环比
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 查询时间维度，必填，枚举值：MTD 月度查询；QTD 季度查询；YTD 年度查询；OTHER 自定义时间范围查询
    :param dateValue: 必填，示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :return:
        :type: 车龄段类型（total--汇总；I0--AGE0；IA--Seg Ia Age1-2；IB--Seg Ib Age3-4；II--Seg II Age5-7；III--Seg III Age8+）
        :name: 基盘类型（retain--保有基盘；active--活跃基盘）
        :amount: 基盘数量
        :hbRate: 环比
        :tbRate: 同比
    """
    url = f'{base_url}/report/mobile/wechat/sv/seviceComprehensiveReportController/customerBaseData'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue
    }
    response = requests.get(url, params=params, headers=headers)
    result = json.loads(response.text)['result']
    converted_result = [{base_en_to_cn_dict.get(k, k): base_en_to_cn_dict.get(v, v) for k,v in item.items()} for item in result]
    return converted_result


@mcp.tool()
def get_business_data(regionCode: str = None, agencyCode: str = None, businessManagerNum: str = None,
                      dealerCode: str = None, provinceCode: str = None, cityCode: str = None, dimension: str = None,
                      dateValue: str = None, endDateValue: str = None, token: str = None):
    """
    获取售后经营状况数据，包括不同售后类型、不同经营指标的数量和同环比
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 维度，必填，枚举值：MTD/QTD/YTD/OTHER
    :param dateValue: 必填，示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :return: 
        :amount: 数值
        :name: taiCi--进站台次；chanZhi--售后产值
        :type: 进站类型，total--汇总；sb--首保；cb--常规保养；sg--事故车；ybwx--一般维修；zhsp--召回索赔
        :hbRate: 环比
        :tbRate: 同比
    """
    url = f'{base_url}/report/mobile/wechat/sv/seviceComprehensiveReportController/performanceData'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue,
    }
    response = requests.get(url, params=params, headers=headers)
    result = json.loads(response.text)['result']
    converted_result = [{business_en_to_cn_dict.get(k, k): business_en_to_cn_dict.get(v, v) for k,v in item.items()} for item in result]
    return converted_result


@mcp.tool()
def get_sale_data(dimension: str, dateValue: str, regionCode: str = None, agencyCode: str = None, businessManagerNum: str = None,
                      dealerCode: str = None, provinceCode: str = None, cityCode: str = None, endDateValue: str = None, token: str = None):
    """
    获取售后配附件直销数据，包括配附件、单独配件、单独附件的直销数量和同环比。
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 维度，必填，枚举值：MTD/QTD/YTD/OTHER
    :param dateValue: 必填，示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :return: 
        :amount: 数值带单位
        :name: 配附件类型，枚举值：total--配附件；parts--配件；accessory--附件
        :hbRate: 环比
        :tbRate: 同比
    """
    url = f'{base_url}/report/mobile/wechat/sv/seviceComprehensiveReportController/partsAccessoryDirectSalesData'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue
    }
    response = requests.get(url, params=params, headers=headers)
    result = json.loads(response.text)['result']
    converted_result = [{sale_en_to_cn_dict.get(k, k): sale_en_to_cn_dict.get(v, v) for k,v in item.items()} for item in result]
    return converted_result


@mcp.tool()
def get_wholesale_data(regionCode: str = None, agencyCode: str = None, businessManagerNum: str = None,
                      dealerCode: str = None, provinceCode: str = None, cityCode: str = None, dimension: str = None,
                      dateValue: str = None, endDateValue: str = None, token: str = None):
    """
    获取售后配附件批售数据，包括配附件、单独配件、单独附件的批售数量和同环比。
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 维度，必填，枚举值：MTD/QTD/YTD/OTHER
    :param dateValue: 必填，示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :return: 
        :amount: 数值带单位
        :name: 配附件类型，枚举值：total--配附件；parts--配件；accessory--附件
        :hbRate: 环比
        :tbRate: 同比
        :progress: 任务完成率
    """
    url = f'{base_url}/report/mobile/wechat/sv/seviceComprehensiveReportController/partsAccessoryWholesaleData'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue
    }
    response = requests.get(url, params=params, headers=headers)
    result = json.loads(response.text)['result']
    converted_result = [{wholesale_en_to_cn_dict.get(k, k): wholesale_en_to_cn_dict.get(v, v) for k,v in item.items()} for item in result]
    return converted_result


@mcp.tool()
def get_sale_detail_data(regionCode: str = None, agencyCode: str = None, businessManagerNum: str = None,
                      dealerCode: str = None, provinceCode: str = None, cityCode: str = None, dimension: str = None,
                      dateValue: str = None, endDateValue: str = None, token: str = None):
    """
    获取售后配附件直销数据，包括配附件不同直销类型（结算直销和出库直销）的金额和同环比
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 维度，必填，枚举值：MTD/QTD/YTD/OTHER
    :param dateValue: 必填，示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :return: 
        :amount: 数值带单位
        :name: 直销类型，枚举值：balance--结算直销；outbound--出库直销
        :hbRate: 环比
        :tbRate: 同比
    """
    url = f'{base_url}/report/mobile/wechat/sv/seviceComprehensiveReportController/partsAccessoryDirectSalesBusinessData'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue
    }
    response = requests.get(url, params=params, headers=headers)
    result = json.loads(response.text)['result']
    converted_result = [{sale_detail_en_to_cn_dict.get(k, k): sale_detail_en_to_cn_dict.get(v, v) for k,v in item.items()} for item in result]
    return converted_result


@mcp.tool()
def get_sale_pj_data(regionCode: str = None, agencyCode: str = None, businessManagerNum: str = None,
                      dealerCode: str = None, provinceCode: str = None, cityCode: str = None, dimension: str = None,
                      dateValue: str = None, endDateValue: str = None, token: str = None):
    """
    获取售后配件直销数据，包括配件不同直销类型（结算直销和出库直销）、不同指标（直销、车间直销、nora、单车成本）的金额和同环比
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 维度，必填，枚举值：MTD/QTD/YTD/OTHER
    :param dateValue: 必填，示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :return: 
        :amount: 数值带单位
        :type: 直销类型，枚举值：balance--结算；outbound--出库
        :name: 指标，枚举值：directSales--直销；workshop--车间直销；nora--批售直销+零售直销；singleCost--单车成本
        :hbRate: 环比
        :tbRate: 同比
    """
    url = f'{base_url}/report/mobile/wechat/sv/seviceComprehensiveReportController/partsAccessoryDirectSalesBusinessData'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue
    }
    response = requests.get(url, params=params, headers=headers)
    result = json.loads(response.text)['result']
    converted_result = [{sale_pfj_en_to_cn_dict.get(k, k): sale_pfj_en_to_cn_dict.get(v, v) for k,v in item.items()} for item in result]
    return converted_result


@mcp.tool()
def get_sale_fj_data(regionCode: str = None, agencyCode: str = None, businessManagerNum: str = None,
                      dealerCode: str = None, provinceCode: str = None, cityCode: str = None, dimension: str = None,
                      dateValue: str = None, endDateValue: str = None, token: str = None):
    """
    获取售后附件直销数据，包括附件不同直销类型（结算直销和出库直销）、不同指标（直销、车间直销、nora、单车成本）的金额和同环比
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 维度，必填，枚举值：MTD/QTD/YTD/OTHER
    :param dateValue: 必填，示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :return: 
        :amount: 数值带单位
        :type: 直销类型，枚举值：balance--结算；outbound--出库
        :name: 指标，枚举值：directSales--直销；workshop--车间直销；nora--批售直销+零售直销；singleCost--单车成本
        :hbRate: 环比
        :tbRate: 同比
    """
    url = f'{base_url}/report/mobile/wechat/sv/seviceComprehensiveReportController/accessoryDirectSalesBusinessData'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue
    }
    response = requests.get(url, params=params, headers=headers)
    result = json.loads(response.text)['result']
    converted_result = [{sale_pfj_en_to_cn_dict.get(k, k): sale_pfj_en_to_cn_dict.get(v, v) for k,v in item.items()} for item in result]
    return converted_result


@mcp.tool()
def get_load_data(partsOrAccessory: int, dimension: str, dateValue: str, regionCode: str = None, 
                  agencyCode: str = None, businessManagerNum: str = None,
                  dealerCode: str = None, provinceCode: str = None, cityCode: str = None, 
                 endDateValue: str = None, token: str = None):
    """
    获取配附件重点产品装车率，包括直销数量、装车率
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 必填，维度:MTD/QTD/YTD/OTHER
    :param dateValue: 必填，示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :param partsOrAccessory: int类型，必填，枚举值：1 配件，2 附件
    :return: 
        :directNum: 直销数量
        :rate: 装车率
        :name: 重点零件名称
    """
    url = f'{base_url}/report/mobile/wechat/sv/seviceComprehensiveReportController/keyProductsLoadingRateData'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue,
        "partsOrAccessory": partsOrAccessory
    }
    response = requests.get(url, params=params, headers=headers)
    result = json.loads(response.text)['result']
    converted_result = [{load_en_to_cn_dict.get(k, k): v for k, v in item.items()} for item in result]
    return converted_result


@mcp.tool()
def get_complete_data(dimension: str, dateValue: str, regionCode: str = None, 
                        agencyCode: str = None, businessManagerNum: str = None,
                        dealerCode: str = None, provinceCode: str = None, cityCode: str = None, 
                        endDateValue: str = None, token: str = None):
    """
    获取配附件批售目标完成情况，包括配附件进度、配件进度、附件进度、养护件进度、专业件进度
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 必填，维度:MTD/QTD/YTD/OTHER
    :param dateValue: 必填，示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :return: 
        :rate: 完成率
        :name: 指标名称，枚举值：time--时间进度；partsAndAccessory--配附件进度；parts--配件进度；accessory--附件进度；maintain--养护件进度；professional--专业件进度
    """
    url = f'{base_url}/report/mobile/wechat/sv/seviceComprehensiveReportController/completionProgressData'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue
    }
    response = requests.get(url, params=params, headers=headers)
    result = json.loads(response.text)['result']
    converted_result = [{load_en_to_cn_dict.get(k, k): load_en_to_cn_dict.get(v, v) for k, v in item.items()} for item in result]
    return converted_result


@mcp.tool()
def get_wholesale_complete_data(partsOrAccessory: int, dimension: str, dateValue: str, regionCode: str = None, 
                        agencyCode: str = None, businessManagerNum: str = None,
                        dealerCode: str = None, provinceCode: str = None, cityCode: str = None, 
                        endDateValue: str = None, token: str = None):
    """
    获取配附件（不区分产品类别）批售销量详细指标，包括目标、订单、确认订单、开票、日均目标、日均订单、日均确认订单、日均开票、订单进度、确认订单进度。
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 必填，维度:MTD/QTD/YTD/OTHER
    :param dateValue: 必填，示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :param partsOrAccessory: int类型，必填，枚举值：0 总体，1 配件，2 附件
    :return: 
        :value: 指标值
        :name: 指标名称，枚举值：target--目标；order--订单；confirmedOrder--确认订单；invoice--开票；dayTarget--日均目标；dayOrder--日均订单；dayConfirmedOrder--日均确认订单；dayInvoice--日均开票；orderProgress--订单进度；confirmedOrderProgress--确认订单进度；invoiceProgress--开票进度
        :type: 配附件类型，枚举值：total--总体；parts--配件；accessory--附件
    """
    url = f'{base_url}/report/mobile/wechat/sv/seviceComprehensiveReportController/wholesaleVolumeData'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue,
        "partsOrAccessory": partsOrAccessory
    }
    response = requests.get(url, params=params, headers=headers)
    result = json.loads(response.text)['result']
    data = [
        {**item, 'value': f"{item['value']}{result['unit']}"} 
        if item['name'] not in ["orderProgress","confirmedOrderProgress","invoiceProgress"] 
        else item
        for item in result['data']]
    converted_result = [{wholesale_complete_en_to_cn_dict.get(k, k): wholesale_complete_en_to_cn_dict.get(v, v) for k,v in item.items()} for item in data]
    return converted_result


@mcp.tool()
def get_stock_data(partsOrAccessory: int, dimension: str, dateValue: str, regionCode: str = None, 
                        agencyCode: str = None, businessManagerNum: str = None,
                        dealerCode: str = None, provinceCode: str = None, cityCode: str = None, 
                        endDateValue: str = None, token: str = None):
    """
    获取配附件的库存和批直比，包括批直比、库存当量、库存规模。
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 必填，维度:MTD/QTD/YTD/OTHER
    :param dateValue: 必填，示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :param partsOrAccessory: int类型，必填，枚举值：1 配件，2 附件
    :return: 
        :value: 指标值
        :name: 指标名称，枚举值：batchDirectRatio--批直比；inventoryAmount--库存规模；inventoryEquivalent--库存当量
    """
    url = f'{base_url}/report/mobile/wechat/sv/seviceComprehensiveReportController/inventoryBatchDirectRatioData'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue,
        "partsOrAccessory": partsOrAccessory
    }
    response = requests.get(url, params=params, headers=headers)
    result = json.loads(response.text)['result']
    converted_result = [{stock_en_to_cn_dict.get(k, k): stock_en_to_cn_dict.get(v, v) for k,v in item.items()} for item in result]
    return converted_result


@mcp.tool()
def get_wholesale_type_data(partsOrAccessory: int, dimension: str, dateValue: str, regionCode: str = None, 
                        agencyCode: str = None, businessManagerNum: str = None,
                        dealerCode: str = None, provinceCode: str = None, cityCode: str = None, 
                        endDateValue: str = None, token: str = None):
    """
    按产品类别（包括：事故件、保养件、易损件、电子件、维修件）查询配附件产品的指标情况，包括库存规模、订单、批直比、库存当量、确认订单、开票。
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 必填，维度:MTD/QTD/YTD/OTHER
    :param dateValue: 必填，示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :param partsOrAccessory: int类型，必填，枚举值：1 配件，2 附件
    :return: 
        :unit: 单位：百万/万等
        :data: 数据
            :inventoryAmount: 库存规模
            :orderAmount: 订单
            :batchDirectRatio: 批直比
            :inventoryEquivalent: 库存当量
            :confirmedOrderAmount: 确认订单
            :invoice: 开票
            :products: 产品类别名称
    """
    url = f'{base_url}/report/mobile/wechat/sv/seviceComprehensiveReportController/categoryProductsData'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue,
        "partsOrAccessory": partsOrAccessory
    }
    response = requests.get(url, params=params, headers=headers)
    result = json.loads(response.text)['result']
    data = [
        {
            k: f"{v}{result['unit']}" 
            if k in ["inventoryAmount", "orderAmount", "confirmedOrderAmount", "invoice"] 
            else v 
            for k, v in item.items()
        }
        for item in result['data']
    ]
    converted_result = [{wholesale_type_en_to_cn_dict.get(k, k): wholesale_type_en_to_cn_dict.get(v, v) for k,v in item.items()} for item in data]
    return converted_result


@mcp.tool()
def get_funnel_data(partsOrAccessory: int, regionCode: str = None, agencyCode: str = None, businessManagerNum: str = None,
                  dealerCode: str = None, provinceCode: str = None, cityCode: str = None, dimension: str = None,
                  dateValue: str = None, endDateValue: str = None, token: str = None):
    """
    获取配附件批售转化漏斗，包括订单量、订单—确认订单转化率、确认订单量、开票量、确认订单--开票转化率
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 维度，必填，枚举值：MTD/QTD/YTD/OTHER
    :param dateValue: 必填，示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :param partsOrAccessory: int类型，必填，枚举值：0 总体，1 配件，2 附件
    :return: 
        :unit: 单位：百万/万等
        :data: 数据
            :orderAmount: 订单量
            :orderToconfirmed: 订单—确认订单转化率
            :confirmedOrderAmount: 确认订单量
            :invoice: 开票量
            :confirmedOrderToInvoice: 确认订单--开票转化率
    """
    url = f'{base_url}/report/mobile/wechat/sv/seviceComprehensiveReportController/conversionFunnelData'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue,
        "partsOrAccessory": partsOrAccessory
    }
    response = requests.get(url, params=params, headers=headers)
    result = json.loads(response.text)['result']
    data = [
        {
            k: f"{v}{result['unit']}" 
            if k in ["orderAmount", "confirmedOrderAmount", "invoice"] 
            else v 
            for k, v in item.items()
        }
        for item in result['data']
    ]
    converted_result = [{funnel_en_to_cn_dict.get(k, k): funnel_en_to_cn_dict.get(v, v) for k,v in item.items()} for item in data]
    return converted_result


@mcp.tool()
def get_business_detail_data(dimension: str, dateValue: str, regionCode: str = None, agencyCode: str = None, businessManagerNum: str = None,
                      dealerCode: str = None, provinceCode: str = None, cityCode: str = None, endDateValue: str = None, token: str = None):
    """
    获取售后经营状况详细数据，包括进站台次、产值、单车产值及其同环比。
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 维度，必填，枚举值：MTD/QTD/YTD/OTHER
    :param dateValue: 必填，示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :return: 
        :amount: 数值
        :name: 指标，枚举值：stationNumber--进站台次；output--产值；single--单车产值
        :hbRate: 环比
        :tbRate: 同比
    """
    url = f'{base_url}/report/mobile/wechat/sv/seviceComprehensiveReportController/businessData'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue
    }
    response = requests.get(url, params=params, headers=headers)
    result = json.loads(response.text)['result']
    converted_result = [{business_detail_en_to_cn_dict.get(k, k): business_detail_en_to_cn_dict.get(v, v) for k,v in item.items()} for item in result]
    return converted_result


@mcp.tool()
def get_business_type_data(dimension: str, dateValue: str, regionCode: str = None, agencyCode: str = None, businessManagerNum: str = None,
                      dealerCode: str = None, provinceCode: str = None, cityCode: str = None, endDateValue: str = None, dataType: str = None,token: str = None):
    """
    获取售后经营状况数据，包括不同售后类型、不同经营指标的数量和同环比
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 维度，必填，枚举值：MTD/QTD/YTD/OTHER
    :param dateValue: 必填，示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :param dataType: 数据类型:0 台次 1 产值 2 单台次产值，默认为0
    :return: 
        :unit: 单位：百万/万/千等
        :data: 数据
            :amount: 数值
            :type: 进站类型
            :hbRate: 环比
            :tbRate: 同比
    """
    url = f'{base_url}/report/mobile/wechat/sv/seviceComprehensiveReportController/businessEntryTypeData'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue,
        "dataType": dataType
    }
    response = requests.get(url, params=params, headers=headers)
    result = json.loads(response.text)['result']
    unit = result['unit'] if result['unit'] != '--' else ''
    data = [
        {**item, 'amount': f"{item['amount']}{unit}"} 
        for item in result['data']]
    converted_result = [{business_type_en_to_cn_dict.get(k, k) if k != 'amount' else business_type_en_to_cn_dict.get(str(dataType), k): business_type_en_to_cn_dict.get(v, v) for k,v in item.items()} for item in data]
    return converted_result


@mcp.tool()
def get_business_region_data(dimension: str, dateValue: str, regionCode: str = None, agencyCode: str = None, businessManagerNum: str = None,
                      dealerCode: str = None, provinceCode: str = None, cityCode: str = None, endDateValue: str = None, dataType: str = None,token: str = None):
    """
    获取各大区的售后经营状况数据。
    :param token: 可以忽略此参数
    :param regionCode: 大区代码，大区代码不传时，可以直接返回全部大区数据
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 维度，必填，枚举值：MTD/QTD/YTD/OTHER
    :param dateValue: 必填，示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :param dataType: 数据类型:0 台次 1 产值 2 单台次产值，默认为0
    :return: 
        :unit: 单位：百万/万/千等
        :data: 数据
            :amount: 数值
            :type: 区域名称
            :hbRate: 环比
            :tbRate: 同比
    """
    url = f'{base_url}/report/mobile/wechat/sv/seviceComprehensiveReportController/businessRegionData'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue,
        "dataType": dataType
    }
    response = requests.get(url, params=params, headers=headers)
    result = json.loads(response.text)['result']
    unit = result['unit'] if result['unit'] != '--' else ''
    data = [
        {**item, 'amount': f"{item['amount']}{unit}"} 
        for item in result['data']]
    converted_result = [{business_region_en_to_cn_dict.get(k, k) if k != 'amount' else business_region_en_to_cn_dict.get(str(dataType), k): business_region_en_to_cn_dict.get(v, v) for k,v in item.items()} for item in data]
    return converted_result


@mcp.tool()
def get_business_month_data(dimension: str, dateValue: str, regionCode: str = None, agencyCode: str = None, businessManagerNum: str = None,
                      dealerCode: str = None, provinceCode: str = None, cityCode: str = None, endDateValue: str = None, dataType: str = None,token: str = None):
    """
    获取售后经营状况月度趋势数据。
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 维度，必填，枚举值：MTD/QTD/YTD/OTHER
    :param dateValue: 必填，示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :param dataType: 数据类型:0 台次 1 产值 2 单台次产值，默认为0
    :return: 
        :unit: 单位：百万/万/千等
        :data: 数据
            :amount: 数值
            :month: 月份
    """
    url = f'{base_url}/report/mobile/wechat/sv/seviceComprehensiveReportController/businessMonthData'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue,
        "dataType": dataType
    }
    response = requests.get(url, params=params, headers=headers)
    result = json.loads(response.text)['result']
    unit = result['unit'] if result['unit'] != '--' else ''
    data = [
        {**item, 'amount': f"{item['amount']}{unit}"} 
        for item in result['data']]
    converted_result = [{business_month_en_to_cn_dict.get(k, k) if k != 'amount' else business_month_en_to_cn_dict.get(str(dataType), k): business_month_en_to_cn_dict.get(v, v) for k,v in item.items()} for item in data]
    return converted_result


@mcp.tool()
def get_service_data(regionCode: str = None, agencyCode: str = None, businessManagerNum: str = None,
                  dealerCode: str = None, provinceCode: str = None, cityCode: str = None, dimension: str = None,
                  dateValue: str = None, endDateValue: str = None, token: str = None):
    """
    获取售后服务数据，包括进站量、进站率、响应量、响应率、线索量
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 维度，必填，枚举值：MTD/QTD/YTD/OTHER
    :param dateValue: 必填，示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :return: 
        :getIn: 进站量
        :getInRate: 进站率
        :response: 响应量
        :responseRate: 响应率
        :leadsNum: 线索量
    """
    url = f'{base_url}/report/mobile/wechat/sv/seviceComprehensiveReportController/serviceLeadsData'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue
    }
    response = requests.get(url, params=params, headers=headers)
    result = json.loads(response.text)['result']
    converted_result = {service_en_to_cn_dict.get(k, k): v for k,v in result.items()}
    return converted_result


@mcp.tool()
def get_cem_data(regionCode: str = None, agencyCode: str = None, businessManagerNum: str = None,
                  dealerCode: str = None, provinceCode: str = None, cityCode: str = None, dimension: str = None,
                  dateValue: str = None, endDateValue: str = None, token: str = None):
    """
    获取售后满意度评价CEM成绩，包括CEM成绩和同环比。
    :param token: 可以忽略此参数
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 维度，必填，枚举值：MTD/QTD/YTD/OTHER
    :param dateValue: 必填，示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :return: 
        :cem: 售后满意度评价成绩
        :hbRate: 环比
        :rate: 同比
    """
    url = f'{base_url}/report/mobile/wechat/sv/seviceComprehensiveReportController/serviceCemData'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue
    }
    response = requests.get(url, params=params, headers=headers)
    result = json.loads(response.text)['result']
    converted_result = {cem_en_to_cn_dict.get(k, k): v for k, v in result.items()}
    return converted_result


# 运行 MCP 服务器
if __name__ == "__main__":
    mcp.run()
    # cityCode = '341800'
    # response = get_dealer_code(cityCode=cityCode)
    # print(response)
    # provinceCode = '110000'
    # cityCode = '110100'
    # dimension = 'YTD'
    # dateValue = '2025'
    # response = get_business_data(provinceCode=provinceCode, cityCode=cityCode, dimension=dimension, dateValue=dateValue,token='I96u6vH9ShsAFVD33LhZzZhBpnJNLq-g')
    # print(response)