categorie_to_kpi = {
    '经营': ['配件批售金额', '配件批售完成', '配件批售目标', '配件批售同比', '附件批售金额', '附件批售完成', '附件批售目标', 
           '附件批售同比', '配附件利润', '配附件完成', '配附件目标', '配附件同比', '进站台次', '进站台次同比', '经销商产值', 
           '经销商产值同比', '配件直销', '配件直销同比']
}


categorie_to_api = {
    '经营': '/report/mobile/wechat/sv/afterSaleJyController/queryJyIndex'
}


# 构建KPI到类别的映射字典
kpi_to_category = {}
for category, kpi_list in categorie_to_kpi.items():
    for kpi in kpi_list:
        kpi_to_category[kpi] = category
