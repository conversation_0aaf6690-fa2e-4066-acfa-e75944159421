from threading import Lock
from typing import Dict
from cozepy import COZE_CN_BASE_URL
from cozepy.auth import Async<PERSON><PERSON><PERSON><PERSON>
from cozepy import Async<PERSON><PERSON><PERSON><PERSON>uthApp, AsyncCoze
from cozepy import Coze<PERSON><PERSON>rror

from utils.config import COZE_JWT_OAUTH_CLIENT_ID, COZE_JWT_OAUTH_PRIVATE_KEY, COZE_JWT_OAUTH_PUBLIC_KEY_ID
# from utils.sx_log import sx_log

class CozeWorkflowExecutor:
    _instance = None
    _lock = Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialize()
        return cls._instance
    
    def _initialize(self):
        jwt_oauth_client_id = COZE_JWT_OAUTH_CLIENT_ID
        jwt_oauth_private_key = COZE_JWT_OAUTH_PRIVATE_KEY
        jwt_oauth_public_key_id = COZE_JWT_OAUTH_PUBLIC_KEY_ID
        coze_api_base = COZE_CN_BASE_URL
        
        # 初始化 AsyncJWTOAuthApp
        async_jwt_oauth_app = AsyncJWTOAuthApp(
            client_id=jwt_oauth_client_id,
            private_key=jwt_oauth_private_key,
            public_key_id=jwt_oauth_public_key_id,
            base_url=coze_api_base,
        )
        # 初始化Coze client
        self.coze = AsyncCoze(
            auth=AsyncJWTAuth(oauth_app=async_jwt_oauth_app),
            base_url=coze_api_base
        )
        
    async def execute_workflow(self, workflow_id: str, parameters: Dict) -> str:
        try:
            result = await self.coze.workflows.runs.create(workflow_id=workflow_id, parameters=parameters, is_async=False)
            return result
        except CozeAPIError as e:
            # sx_log.warning(f"Error executing Coze workflow {workflow_id}: {e}")
            return None
        
# 使用示例
if __name__ == "__main__":
    executor = CozeWorkflowExecutor()
    import json
    import asyncio
    async def main():
        tasks = [
            executor.execute_workflow("7470708539339489320", {"query": "上汽大众途昂pro", "count": 4}),
            # executor.execute_workflow("7470708539339489320", {"query": "特斯拉Model S", "count": 4}),
            # executor.execute_workflow("7470708539339489320", {"query": "宝马X5", "count": 4}),
            # executor.execute_workflow("7470708539339489320", {"query": "奥迪Q7", "count": 4}),
            # executor.execute_workflow("7470708539339489320", {"query": "奔驰GLE", "count": 4}),
            # executor.execute_workflow("7470708539339489320", {"query": "捷豹XJ", "count": 4}),
            # executor.execute_workflow("7470708539339489320", {"query": "兰博基尼Aventador", "count": 4}),
            # executor.execute_workflow("7470708539339489320", {"query": "保时捷911", "count": 4}),
            # executor.execute_workflow("7470708539339489320", {"query": "法拉利488", "count": 4}),
            # executor.execute_workflow("7470708539339489320", {"query": "玛莎拉蒂Levante", "count": 4}),
        ]
        
        # 并发执行所有任务
        results = await asyncio.gather(*tasks)
        
        for i, result in enumerate(results):
            print(f"----"*12)
            print(f"Result {i+1}:")
            print(json.loads(result.data)['output'])

    # 使用 asyncio.run() 来运行异步函数
    asyncio.run(main())
    