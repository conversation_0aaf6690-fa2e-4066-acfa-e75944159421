import json
import aiohttp
import asyncio
import traceback
import time

# from utils.config import KONG_X_APP_ID
# from utils.env_config import env


async def chat_message(base_url: str, conversation_id: str, query: str, user: str, 
                       response_mode: str = "streaming", debug: bool = False, 
                       heartbeat_callback=None, queue: asyncio.Queue=None):
    """
    与Dify API进行流式对话
    
    Args:
        base_url: API基础URL
        conversation_id: 会话ID
        query: 查询内容
        user: 用户ID
        response_mode: 响应模式，默认为streaming
        debug: 是否开启调试模式，生产环境建议设为False
        heartbeat_callback: 心跳回调函数，用于在等待响应时保持连接活跃
    
    Returns:
        tuple: (完整回答, 会话ID)
    """
    headers = {
        'X-App-Id': 'TvpxaUm3',
        'X-Timestamp': '20200109163545256',
        'X-Sequence-No': '2020010916354525600001',
        'Content-Type': 'application/json',
        'X-Signature': ''
    }

    payload = {
        "inputs": {},
        "query": query,
        "response_mode": response_mode,
        "conversation_id": conversation_id,
        "user": user
    }

    url = f'{base_url}/v1/chat-messages'

    # 使用aiohttp的异步会话，配置专门处理chunked encoding问题
    timeout = aiohttp.ClientTimeout(total=300)  # 增加总超时时间到300秒
    # 创建连接器，设置更大的读取限制
    connector = aiohttp.TCPConnector(
        limit_per_host=30,
        limit=100,
        ttl_dns_cache=300,
        use_dns_cache=True,
    )
    
    async with aiohttp.ClientSession(
        timeout=timeout,
        connector=connector,
        read_bufsize=1024*1024  # 1MB读取缓冲区
    ) as session:
        if debug:
            print(f"发送请求到: {url}")
            print(f"请求体: {json.dumps(payload, ensure_ascii=False)}")

        final_answer = ""
        dify_conversation_id = ""
        chunk_count = 0
        line_count = 0
        last_data_time = time.time()
        is_stream_complete = False
        last_heartbeat_time = time.time()
        heartbeat_interval = 5.0  # 心跳间隔5秒
        
        try:
            async with session.post(url, headers=headers, json=payload) as response:
                if debug:
                    print(f"响应状态: {response.status}")
                    print(f"响应头: {dict(response.headers)}")
                
                response.raise_for_status()
                
                # 异步处理流式响应 - 专门处理 chunked encoding 和传输中断问题
                buffer = ""
                
                try:
                    # 使用小块读取方式，更好地处理 chunked encoding 问题
                    while True:
                        current_time = time.time()
                        
                        # 检查是否需要发送心跳
                        if heartbeat_callback and (current_time - last_heartbeat_time) >= heartbeat_interval:
                            try:
                                await heartbeat_callback()
                                last_heartbeat_time = current_time
                                if debug:
                                    print("💓 发送心跳消息")
                            except Exception as heartbeat_err:
                                if debug:
                                    print(f"❌ 心跳发送失败: {heartbeat_err}")
                                    print("🔌 检测到前端连接断开，停止处理以节省资源")
                                # 心跳失败说明前端已断开，抛出异常中断处理
                                raise ConnectionError(f"前端连接已断开，停止Dify调用: {heartbeat_err}")
                        
                        try:
                            # 小块读取，避免传输编码问题
                            chunk = await response.content.read(2048)  # 更小的读取块
                        except aiohttp.ClientPayloadError as payload_err:
                            if debug:
                                print(f"传输载荷错误: {payload_err}")
                            # 尝试读取剩余数据
                            try:
                                remaining = await response.content.read(1024)
                                if remaining:
                                    chunk = remaining
                                else:
                                    if debug:
                                        print("无剩余数据，结束读取")
                                    break
                            except:
                                if debug:
                                    print("读取剩余数据失败，结束读取")
                                break
                        
                        if not chunk:
                            if debug:
                                print("服务端关闭了连接")
                            break
                        
                        chunk_count += 1
                        current_time = time.time()
                        time_since_last = current_time - last_data_time
                        last_data_time = current_time
                        
                        try:
                            # 解码并添加到缓冲区
                            buffer += chunk.decode('utf-8')
                        except UnicodeDecodeError as decode_err:
                            if debug:
                                print(f"字符解码错误: {decode_err}")
                            continue
                        
                        # 按行分割处理
                        should_break = False
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            decoded_line = line.strip()
                            line_count += 1
                            
                            if debug and decoded_line:
                                print(f"数据行 #{line_count}: {decoded_line[:200]}...")
                            
                            # 检查是否为空行（SSE标准中的心跳）
                            if not decoded_line:
                                continue
                            
                            if decoded_line.startswith('data: '):
                                json_str = decoded_line[6:]
                                if not json_str:
                                    continue
                                try:
                                    data = json.loads(json_str)
                                    event_type = data.get('event', 'unknown')
                                    
                                    if debug:
                                        print(f"事件类型: {event_type}")
                                    
                                    # 检查是否为结束事件
                                    if event_type == 'workflow_finished':
                                        if debug:
                                            print("工作流完成")
                                        is_stream_complete = True
                                        should_break = True
                                        break
                                    elif event_type == 'message_end':
                                        if debug:
                                            print("消息结束")
                                        is_stream_complete = True
                                        should_break = True
                                        break
                                    elif event_type == 'error':
                                        print(f"服务端错误: {data}")  # 错误信息始终显示
                                        should_break = True
                                        break
                                    
                                    if data.get('answer'):
                                        answer_chunk = data.get('answer')
                                        if debug:
                                            print(f"答案片段: {answer_chunk}")
                                        item = await self.format_sse_message(content=f"正在检索\"{self.question}\"相关数据... ...\n", content_type="text")
                                        
                                        final_answer += answer_chunk
                                        if data.get('conversation_id') and not dify_conversation_id:
                                            dify_conversation_id = data.get('conversation_id')
                                except json.JSONDecodeError as json_err:
                                    if debug:
                                        print(f"JSON解析失败，数据长度: {len(json_str)}")
                                        print(f"错误详情: {json_err}")
                                    pass
                            elif decoded_line.startswith('event: '):
                                if debug:
                                    event_name = decoded_line[7:]
                                    print(f"SSE事件: {event_name}")
                        
                        if should_break:
                            break
                
                except Exception as read_err:
                    print(f"读取数据时出错: {read_err}")  # 读取错误始终显示
                    
                if debug:
                    print(f"流式响应结束，收到 {chunk_count} 个字节块, {line_count} 行数据")
                
        except (asyncio.TimeoutError, aiohttp.ServerTimeoutError) as timeout_err:
            print(f"请求超时: {timeout_err}")  # 超时错误始终显示
        except aiohttp.ClientConnectionError as conn_err:
            print(f"连接错误: {conn_err}")  # 连接错误始终显示
        except aiohttp.ClientPayloadError as payload_err:
            print(f"传输载荷错误 (chunked encoding问题): {payload_err}")  # 传输错误始终显示
            print("建议: 这通常是服务端传输编码问题，请联系服务端开发人员检查配置")
        except aiohttp.ClientResponseError as resp_err:
            print(f"响应错误 - 状态码: {resp_err.status}, 信息: {resp_err.message}")  # 响应错误始终显示
        except aiohttp.ClientError as e:
            print(f"客户端错误: {e}")  # 客户端错误始终显示
        except KeyboardInterrupt:
            if debug:
                print("用户中断了连接")
        except Exception as unexpected_err:
            print(f"意外错误: {unexpected_err}")  # 意外错误始终显示
            if debug:
                print(f"详细信息: {traceback.format_exc()}")
        finally:
            if debug:
                total_time = time.time() - (last_data_time - time_since_last if chunk_count > 0 else time.time())
                print(f"\n--- 流式响应接收完毕 ---")
                print(f"流式响应是否正常完成: {'是' if is_stream_complete else '否'}")
                print(f"总字节块数量: {chunk_count}")
                print(f"总数据行数量: {line_count}")
                print(f"总耗时: {total_time:.2f}秒")
                print(f"完整回答长度: {len(final_answer)} 字符")
                print(f"dify对话id: {dify_conversation_id}")
                
                # 诊断建议
                if not is_stream_complete and line_count > 0:
                    print("\n🔍 诊断建议:")
                    print("- 流式响应意外中断，没有收到正常的结束事件")
                    if line_count < 10:
                        print("- 数据行数量较少，可能是服务端问题")
                    else:
                        print("- 数据行数量正常，可能是网络中断或服务端处理超时")
                    print("- 建议检查服务端日志以确认是否有错误")
                elif line_count == 0:
                    print("\n🔍 诊断建议:")
                    print("- 没有收到任何数据行，可能是:")
                    print("  1. 服务端立即关闭了连接")
                    print("  2. 认证或权限问题")
                    print("  3. 服务端内部错误")
                    print("- 建议检查请求参数和服务端状态")
            
            return final_answer, dify_conversation_id


async def chat_message_with_retry(base_url: str, conversation_id: str, query: str, user: str, 
                                 response_mode: str = "streaming", debug: bool = False, 
                                 max_retries: int = 3, retry_delay: float = 1.0, heartbeat_callback=None, queue: asyncio.Queue=None):
    """
    带重试机制的chat_message包装函数
    
    Args:
        base_url: API基础URL
        conversation_id: 会话ID
        query: 查询内容
        user: 用户ID
        response_mode: 响应模式，默认为streaming
        debug: 是否开启调试模式
        max_retries: 最大重试次数，默认3次
        retry_delay: 重试间隔时间(秒)，默认1秒
        heartbeat_callback: 心跳回调函数
    
    Returns:
        tuple: (完整回答, 会话ID)
    """
    last_exception = None
    
    for attempt in range(max_retries):
        try:
            if debug and attempt > 0:
                print(f"\n🔄 开始第{attempt + 1}次尝试...")
            
            result = await chat_message(base_url, conversation_id, query, user, response_mode, debug, heartbeat_callback, queue)
            
            # 检查返回结果是否有效
            if result[0] or result[1]:  # 如果有返回内容或对话ID，认为成功
                if debug and attempt > 0:
                    print(f"✅ 第{attempt + 1}次尝试成功!")
                return result
            elif attempt < max_retries - 1:  # 如果返回空内容且还有重试机会
                if debug:
                    print(f"⚠️ 第{attempt + 1}次尝试返回空内容，{retry_delay}秒后重试...")
                await asyncio.sleep(retry_delay)
                continue
                
        except aiohttp.ClientResponseError as e:
            last_exception = e
            if e.status == 500 and attempt < max_retries - 1:
                if debug:
                    print(f"❌ 第{attempt + 1}次请求失败(500错误)，{retry_delay}秒后重试...")
                await asyncio.sleep(retry_delay)
                continue
            else:
                if debug:
                    print(f"❌ 第{attempt + 1}次请求失败(状态码:{e.status})，不再重试")
                raise
                
        except (asyncio.TimeoutError, aiohttp.ServerTimeoutError) as e:
            last_exception = e
            if attempt < max_retries - 1:
                if debug:
                    print(f"⏰ 第{attempt + 1}次请求超时，{retry_delay}秒后重试...")
                await asyncio.sleep(retry_delay)
                continue
            else:
                if debug:
                    print(f"⏰ 第{attempt + 1}次请求超时，不再重试")
                raise
                
        except (aiohttp.ClientConnectionError, aiohttp.ClientPayloadError) as e:
            last_exception = e
            if attempt < max_retries - 1:
                if debug:
                    print(f"🔌 第{attempt + 1}次连接错误，{retry_delay}秒后重试...")
                await asyncio.sleep(retry_delay)
                continue
            else:
                if debug:
                    print(f"🔌 第{attempt + 1}次连接错误，不再重试")
                raise
                
        except ConnectionError as e:
            # 前端连接断开，不应该重试，直接抛出
            last_exception = e
            if debug:
                print(f"❌ 前端连接已断开: {e}")
                print("🚫 停止重试，节省后端资源")
            raise  # 直接抛出，不进行重试
                
        except Exception as e:
            last_exception = e
            if attempt < max_retries - 1:
                if debug:
                    print(f"💥 第{attempt + 1}次未知错误，{retry_delay}秒后重试: {e}")
                await asyncio.sleep(retry_delay)
                continue
            else:
                if debug:
                    print(f"💥 第{attempt + 1}次未知错误，不再重试: {e}")
                raise
    
    # 如果所有重试都失败了
    if debug:
        print(f"\n❌ 所有{max_retries}次尝试都失败了")
    
    if last_exception:
        raise last_exception
    else:
        return "", ""


if __name__ == "__main__":
    # 示例调用
    url = 'http://test-aliyun-oa.b2c-api.infra.sitc/aiservice/dify/voc_data'
    conversation_id = ""
    query = "途昂Pro这个月提及的情感分类占比是怎样的？"
    user = "123"
    
    # 示例心跳回调函数
    async def example_heartbeat():
        print("💓 发送心跳: 正在处理您的问题，请稍候...")
    
    import asyncio
    # 生产环境使用: debug=False, max_retries=3
    # 调试环境使用: debug=True, max_retries=3
    try:
        result = asyncio.run(chat_message_with_retry(
            url, conversation_id, query, user, 
            debug=True,  # 开启调试模式查看重试过程
            max_retries=3,  # 最多重试3次
            retry_delay=1.0,  # 重试间隔1秒
            heartbeat_callback=example_heartbeat  # 传入心跳回调函数
        ))
        print(f"✅ 最终结果: {result[1]}")
        print(f"📝 回答内容长度: {len(result[0])} 字符")
    except Exception as e:
        print(f"❌ 最终失败: {e}")