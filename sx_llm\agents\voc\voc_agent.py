import re
import ast
import json
import asyncio
import traceback

from agents.base_agent import BaseAgent
from agents.voc.prompt import prompt_generate_chart_data_template
from functions.dify_executor import chat_message_with_retry
from utils.config import VOC_URL
from utils.env_config import env
from database.redis_db import RedisDB


class VOCAgent(BaseAgent):
    def __init__(self):
        super().__init__()  # 调用父类初始化
        self.kpis = []
        self.categorys_kpis = {}
        self.category_kpi_api = []
        self.base_url = VOC_URL[env]
        self.data_all = []
        self.answer = ''
        self.dify_conversation_id = None

    def get_profiles_from_manager(self, agent: BaseAgent):
        # 从管理类获取数据
        self.client = agent.client
        self.access_token = agent.access_token
        self.input_json = agent.input_json
        self.user_id = agent.user_id
        self.conversation_id = agent.conversation_id
        self.question = agent.question
        self.is_internet_search = agent.is_internet_search
        self.is_deep_thinking = agent.is_deep_thinking
        self.language = agent.language
        self.input_time = agent.input_time
        self.history_message = agent.history_message
        self.history_question = agent.history_question
        self.conversation_all = agent.conversation_all
        self.message_id = agent.message_id

    async def update_dify_conversation_id(self, dify_conversation_id):
        redis_db = RedisDB()
        await redis_db.connect()
        try:
            await redis_db.set_key_value(f'dify_conversation:{self.conversation_id}', dify_conversation_id)
        except Exception as e:
            print('更新dify对话id异常捕获', e)
        finally:
            await redis_db.close()
        
    async def get_dify_conversation_id(self):
        redis_db = RedisDB()
        await redis_db.connect()
        try:
            self.dify_conversation_id = await redis_db.get_key(f'dify_conversation:{self.conversation_id}')
            if not self.dify_conversation_id:
                self.dify_conversation_id = ''
            self.save_log('VOCagent-VOC 当前对话获取到的VOC dify 对话id',self.dify_conversation_id)
        except Exception as e:
            print('获取dify对话id异常捕获', e)
        finally:
            await redis_db.close()


    async def get_voc_result(self):
        # 获取VOC结果
        async def heartbeat_callback():
            """心跳回调函数，定期发送消息保持连接活跃"""
            try:
                # 如果有队列，发送心跳消息
                if hasattr(self, 'queue') and self.queue:
                    heartbeat_msg = await self.format_sse_message(
                        content="", 
                        content_type="text"
                    )
                    await self.queue.put(heartbeat_msg)
            except Exception as e:
                self.save_log('VOCagent-客户端连接断开', f'用户{self.user_id}提前离开: {e}')
                print(f"❌ 心跳回调发送失败，客户端已断开: {e}")
                # 抛出异常来中断Dify调用，节省资源
                raise ConnectionError(f"前端连接已断开，停止处理: {e}")
        
        conversation_id = self.dify_conversation_id or ""
        question = self.question or ""
        user_id = self.user_id or ""
        
        result, dify_conversation_id = await chat_message_with_retry(
            self.base_url, 
            conversation_id, 
            question, 
            user_id,
            heartbeat_callback=heartbeat_callback,  # 传入心跳回调函数
            queue=self.queue
        )
        self.save_log('VOCagent-VOC dify返回结果',result)
        self.save_log('VOCagent-VOC dify返回的conversation_id',dify_conversation_id)
        self._process_dify_result(result)
        await self.update_dify_conversation_id(dify_conversation_id)


    def _process_dify_result(self, result):
        if result == '请咨询汽车领域的相关问题':
            self.answer = '请咨询汽车领域的相关问题'
        else:
            # 使用正则表达式匹配结论总结
            conclusion_match = re.search(r'# 结论总结\n(.*?)(?=\n# |\Z)', result, re.DOTALL)
            self.answer = '# 结论总结\n' + conclusion_match.group(1).strip() if conclusion_match else ""

            # 提取结论总结前的内容（新增代码）
            echarts_match = re.search(r'(.*?)(?=# 结论总结)', result, re.DOTALL)
            echarts_json = echarts_match.group(1) if echarts_match else ""
            self.data_all.append(echarts_json)

    def parse_str_to_list(self, raw_data: str) -> list:
        """将大模型返回的字符串处理成List。"""
        # 1. 移除字符串两端的空白字符 (包括换行符)，将False、True、null转换为false、true、0，将单引号转换为双引号
        clean_data = raw_data.strip()
        clean_data = clean_data.replace('False','false')
        clean_data = clean_data.replace('True','true')
        clean_data = clean_data.replace('null','0')
        clean_data = clean_data.replace('\'','\"')

        # 2. (核心步骤) 使用正则表达式查找被 `[` 和 `]` 包裹的JSON内容
        #    这可以很有效地处理前后可能存在的Markdown标记或其他文本
        match = re.search(r'\[.*\]', clean_data, re.DOTALL)
        if not match:
            print("错误：在字符串中未找到有效的列表/JSON结构。")
            return []

        json_string = match.group(0)

        # 3. 解析JSON字符串
        try:
            # 将提取出的字符串解析为Python列表
            parsed_list = json.loads(json_string)
            return parsed_list
        except json.JSONDecodeError as e:
            print(f"JSON 解析失败: {e}")
            # 这里可以根据你的需要返回空列表或重新抛出异常
            return []

    async def generate_chart_data(self):
        # 生成符合前端数据格式的图表数据
        prompt = prompt_generate_chart_data_template.format(question = self.question, data = self.data_all, today = self.today)
        self.save_log('VOCagent-生成图表数据的prompt',prompt)
        content = [{"role": "user","content": prompt}]
        result = await self.process_llm_0324.stream_response(content)
        result = result.replace('true','True')
        self.save_log('VOCagent-生成图表数据的结果',result)
        
        self.charts = self.parse_str_to_list(result)

    async def event_stream_queue(self, queue: asyncio.Queue):
        try:
            # 将队列保存到实例变量，供心跳回调使用
            self.queue = queue
            
            await self.get_dify_conversation_id()
            await self.get_voc_result()
            item = await self.format_sse_message(content=f"正在检索\"{self.question}\"相关数据... ...\n", content_type="text")
            await queue.put(item)
            item = await self.format_sse_message(content=self.answer + '\n', content_type="text")
            await queue.put(item)
            # 生成图表数据
            await self.generate_chart_data()
            await queue.put(None)
        except asyncio.CancelledError:
            print("捕获到 CancelledError，开始清理")
            print("客户端已断开连接")
            raise  # 重新抛出以便框架处理
        except Exception as e:
            e_msg = traceback.format_exc()
            print(e_msg)
            self.save_log('VOCagent-异常捕获',e_msg)
            await queue.put(await self.format_sse_message('对不起，由于问题过于复杂，我暂时无法回答', content_type="text", ended=True))
            print('发生异常：',e_msg )
        finally:
            # 清理队列引用
            if hasattr(self, 'queue'):
                delattr(self, 'queue')
            print("SSE返回已结束")



if __name__ == '__main__':
    agent = VOCAgent()