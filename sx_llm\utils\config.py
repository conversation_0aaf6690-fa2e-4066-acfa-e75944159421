import socket
import requests

from utils.env_config import redis_env, open_api_url


# 公网访问代理地址
COZE_PROXY_URL = "http://172.20.242.32/chatglm/coze"
OPENAI_PROXY_URL = "http://172.20.242.32/chatglm/azure/"
BAIDU_BCE_PROXY_URL = "http://172.20.242.32/chatglm/baidubce"
VOLCENGINE_PROXY_URL = "http://172.20.242.32/chatglm/volces"


def wenxin_ak():
    url = f"http://{open_api_url}/account-config/v1/private/account/yiYan"
    headers = {"Content-Type": "application/json"}
    result = requests.get(url, headers=headers).json()
    api_key = result["data"]["api-key"]
    secret_key = result["data"]["secret-key"]
    return api_key,secret_key

def get_chatgpt_keys():
    url = f"http://{open_api_url}/account-config/v1/private/account/chatGPT"
    headers = {"Content-Type": "application/json"}
    result = requests.get(url, headers=headers).json()
    chatgpt_keys = result["data"]
    return chatgpt_keys


def get_secret_keys():
    url = f"http://{open_api_url}/account-config/v1/private/account/deepSeek"
    headers = {"Content-Type": "application/json"}
    result = requests.get(url, headers=headers).json()
    secret_keys = result["data"]
    return secret_keys


# init model config
init_llm = "chatglm2-6b"
init_embedding_model = "nlp_gte_sentence-embedding_chinese-large"


# model config
embedding_model_dict = {
    "m3e-base": "/root/models/m3e-base",
    "text2vec-large-chinese": "/root/models/text2vec-large-chinese",
    "nlp_gte_sentence-embedding_chinese-large": "/root/models/nlp_gte_sentence-embedding_chinese-large",
    "nlp_gte_sentence-embedding_chinese-base": "/root/models/nlp_gte_sentence-embedding_chinese-base",
}


llm_model_dict = {
    "chatglm2-6b": "/root/models/chatglm2-6b",
    "chatglm2-6b-int4": "THUDM/chatglm-6b-int4",

}


# 文心千帆账号
BAIDU_CLIENT_ID,BAIDU_CLIENT_SECRET = wenxin_ak()

# OpenAI
OPENAI_AZURE_ENDPOINT = OPENAI_PROXY_URL
OPENAI_API_KEY = get_chatgpt_keys()[0]
OPENAI_API_VERSION = "2023-07-01-preview"
OPENAI_DEPLOYMENT_NAME = "GPT-4-Turbo-SX"
OPENAI_TEMPERATURE = 0
OPENAI_SEED = 123
OPENAI_RESPONSE_FORMAT = { "type": "json_object" }

# 模型参数
init_temperature = 0

SECRET_KEYS = get_secret_keys()

# 火山
VOLCENGINE_WARROOM_API_KEY = SECRET_KEYS['VOLCENGINE_WARROOM_API_KEY']
VOLCENGINE_DEEPSEEK_V3_END_POINT = SECRET_KEYS['VOLCENGINE_DEEPSEEK_V3_END_POINT']
VOLCENGINE_DEEPSEEK_V3_0324_END_POINT = "ep-20250331151844-7qvsb"
VOLCENGINE_DEEPSEEK_R1_END_POINT = SECRET_KEYS['VOLCENGINE_DEEPSEEK_R1_END_POINT']

# 百炼
DASHSCOPE_API_KEY = SECRET_KEYS['DASHSCOPE_API_KEY']

# Coze
COZE_API_KEY = SECRET_KEYS['COZE_API_KEY']

COZE_JWT_OAUTH_CLIENT_ID = "1195497993820"
COZE_JWT_OAUTH_PUBLIC_KEY_ID = "Mo2SwtlhrZWOwQH8sperSN4b8J760rznm04lSyZrOgQ"
COZE_JWT_OAUTH_PRIVATE_KEY = SECRET_KEYS['COZE_JWT_OAUTH_PRIVATE_KEY']

# 阿里云私有化部署Deepseek
PRIVATE_DEEPSEEK_API_KEY = SECRET_KEYS['PRIVATE_DEEPSEEK_API_KEY']
PRIVATE_DEEPSEEK_URL = "http://1680606300477185.cn-shanghai.pai-eas.aliyuncs.com/api/predict/deepseek_svw_1_a.deepseek_svw_1/v1"

# Redis
REDIS_PASSWORD_LOCAL = 'warroom'
REDIS_PASSWORD_DEV = SECRET_KEYS['REDIS_PASSWORD_DEV']
REDIS_PASSWORD_PRD = SECRET_KEYS['REDIS_PASSWORD_PRD']
REDIS_PASSWORD = (
    REDIS_PASSWORD_LOCAL if redis_env == 'local' else
    REDIS_PASSWORD_DEV if redis_env == 'dev' else
    REDIS_PASSWORD_PRD if redis_env == 'prd' else
    None
)

# 机器IP
def get_machine_ip():
    # 获取主机名
    hostname = socket.gethostname()
    # 获取 IP 地址
    ip_address = socket.gethostbyname(hostname)
    return ip_address

MACHINE_IP = get_machine_ip()

AFTERSALES_API = {'dev': 'http://alb-64mm6n89j1imh3i5ds.cn-shanghai.alb.aliyuncsslb.com:81/dac_test',
                  'prd': 'http://alb-64mm6n89j1imh3i5ds.cn-shanghai.alb.aliyuncsslb.com/dac_prod'}


VOC_URL = {'dev': "http://test-aliyun-oa.b2c-api.infra.sitc/aiservice/dify/voc_data",
           'prd': "http://aliyun-oa.b2c-api.infra.sitc/aiservice/dify/voc_data"}

KONG_X_APP_ID = {'dev': "TvpxaUm3",
                 'prd': "zGbp5dKv"}