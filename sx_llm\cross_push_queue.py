# from langchain_huggingface import HuggingFaceEmbeddings


# embeddings = HuggingFaceEmbeddings(model_name="D:\models\\nlp_gte_sentence-embedding_chinese-small")






# query = "部门如何进行会务接待礼仪服务申请？会务礼仪申请？"


# print(embeddings.embed_query(query))



# from dataclasses import dataclass
# from typing import Optional  # 需要添加Optional类型

# @dataclass
# class AgentContext:
#     profile_a: Optional[str] = None  # 使用Optional类型并设置默认值
#     profile_b: Optional[str] = None  # 保持字段顺序一致


# class BaseAgent:
#     def __init__(self, context: AgentContext = None):
#         self.ctx = context
#         self.client = self.ctx.client
#         self.user_name = self.ctx.user_name
        
#     def set_context(self, input_json):
#         self.ctx = input_json
#         self.client = self.ctx.get('client', None)
#         self.user_name = self.ctx.get('user_name', None)

# class TestAAgent(BaseAgent):
#     def __init__(self, context: AgentContext = None):
#         super().__init__(context)

# class TestBAgent(BaseAgent):
#     def __init__(self, context: AgentContext = None):
#         super().__init__(context)

# if __name__ == '__main__':
#     agentA = TestAAgent()
#     agentA.set_context({'client': 'test', 'user_name': 'test'})
#     print(agentA.client)
#     agentB = TestBAgent(agentA.ctx)
#     print(agentB.client)

import asyncio
class Processor:
    async def _run_program(self, program_name, steps, output_queue, wait_queue=None):
        for i, step in enumerate(steps, 1):
            if wait_queue:
                await wait_queue.get()  # 等待前序信号
                
            # 执行具体步骤
            result = await step()
            await output_queue.put((f"{program_name}{i}", result))
            
            # 创建下一步骤的通行队列
            if i < len(steps):
                next_queue = asyncio.Queue()
                await next_queue.put(None)  # 添加初始信号
                wait_queue = next_queue

    async def ordered_execution(self):
        a_queues = [asyncio.Queue() for _ in range(3)]
        b_queues = [asyncio.Queue() for _ in range(3)]
        
        await a_queues[0].put(None)
        await b_queues[0].put(None)
        
        self.program_a = asyncio.create_task(self._run_program('A', [a1, a2, a3], a_queues[0]))
        self.program_b = asyncio.create_task(self._run_program('B', [b1, b2, b3], b_queues[0]))

        completed_steps = set()
        while len(completed_steps) < 6:
            # 修复点1：创建明确的任务对象
            tasks = [
                asyncio.create_task(a_queues[0].get()),
                asyncio.create_task(b_queues[0].get())
            ]
            
            done, pending = await asyncio.wait(
                tasks,
                return_when=asyncio.FIRST_COMPLETED
            )
            
            for queue_task in done:
                # 修复点2：添加结果类型检查
                result = queue_task.result()
                if not isinstance(result, tuple):
                    continue
                    
                step_name, result = result
                if step_name not in completed_steps:
                    completed_steps.add(step_name)
                    yield step_name, result
                    
                # 修复点3：添加步骤号有效性检查
                try:
                    step_num = int(step_name[1])
                except (IndexError, ValueError):
                    continue
                    
                if step_num == 1:
                    await a_queues[1].put(None)
                    await b_queues[1].put(None)
                elif step_num == 2:
                    if 'A' in step_name:
                        await a_queues[2].put(None)
                    else:
                        await b_queues[2].put(None)
            
            # 修复点4：取消未完成的任务
            for task in pending:
                task.cancel()



# 示例步骤函数
async def a1(): 
    await asyncio.sleep(1)
    return "A1结果"
async def a2(): 
    await asyncio.sleep(1)
    return "A2结果"
async def a3(): 
    await asyncio.sleep(1)
    return "A3结果"
async def b1(): 
    await asyncio.sleep(0.8)
    return "B1结果"
async def b2(): 
    await asyncio.sleep(1)
    return "B2结果"
async def b3(): 
    await asyncio.sleep(1)
    return "B3结果"

# 主执行函数
async def main():
    processor = Processor()
    try:
        async for step, result in processor.ordered_execution():
            print(f"{step}: {result}")
    finally:
        # 确保任务正常关闭
        processor.program_a.cancel()
        processor.program_b.cancel()
        await asyncio.gather(
            processor.program_a,
            processor.program_b,
            return_exceptions=True
        )

if __name__ == "__main__":
    asyncio.run(main())