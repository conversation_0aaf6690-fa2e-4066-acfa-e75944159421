import requests
import time

# 提取基础 URL
BASE_URL = 'http://127.0.0.1:8000'
# 提取公共请求头
COMMON_HEADERS = {
    'accesstoken': 'Bearer your_token',
    'Content-Type': 'application/json'
}

def test_conversation_chat():
    chat_url = f'{BASE_URL}/conversation/chat'
    chat_data = {
        'user_id': '16579',
        'conversation_id': '16579-1',
        'question': '分析下今年id4销量情况如何？',
        'is_internet_search': False,
        'is_deep_thinking': False,
    }

    all_content = []
    start_time = time.time()
    first_data_time = None
    last_data_time = None

    try:
        # 发送 SSE 请求
        response = requests.post(chat_url, json=chat_data, headers=COMMON_HEADERS, stream=True)
        response.raise_for_status()

        # 处理 SSE 事件流
        for line in response.iter_lines():
            if line:
                # 解码字节数据为字符串
                decoded_line = line.decode('utf-8')
                if first_data_time is None:
                    first_data_time = time.time()
                last_data_time = time.time()
                try:
                    import json
                    data_obj = json.loads(decoded_line)
                    print(data_obj)
                    if 'content' in data_obj:
                        all_content.append(data_obj['content'])
                    if 'end' in data_obj and data_obj['end']:
                        break
                except json.JSONDecodeError:
                    continue

    except requests.exceptions.RequestException as e:
        print(f"请求发生错误: {e}")

    if all_content:
        print(''.join(all_content))
    if first_data_time and last_data_time:
        print(f"first_data-start_time: {first_data_time - start_time} s")
        print(f"last_data-first_data: {last_data_time - first_data_time} s")

def test_user_history():
    user_history_url = f'{BASE_URL}/user/history'
    user_id = '16579'
    user_history_params = {
        'user_id': user_id
    }
    try:
        user_history_response = requests.get(user_history_url, params=user_history_params, headers=COMMON_HEADERS)
        user_history_response.raise_for_status()
        print(f"/user/history 接口响应: {user_history_response.json()}")
    except requests.exceptions.RequestException as e:
        print(f"/user/history 接口请求发生错误: {e}")

def test_conversation_history():
    conversation_history_url = f'{BASE_URL}/conversation/history'
    conversation_id = '16579-1'
    conversation_history_params = {
        'conversation_id': conversation_id
    }
    try:
        conversation_history_response = requests.get(conversation_history_url, params=conversation_history_params, headers=COMMON_HEADERS)
        conversation_history_response.raise_for_status()
        print(f"/conversation/history 接口响应: {conversation_history_response.json()}")
    except requests.exceptions.RequestException as e:
        print(f"/conversation/history 接口请求发生错误: {e}")

def test_feedback_operate():
    feedback_operate_url = f'{BASE_URL}/feedback/operate'
    feedback_operate_data = {
        'message_id': '12345',
        'feedback_type': 'good',
        'feedback_content': 'This is a positive feedback.'
    }
    try:
        feedback_operate_response = requests.post(feedback_operate_url, json=feedback_operate_data, headers=COMMON_HEADERS)
        feedback_operate_response.raise_for_status()
        print(f"/feedback/operate 接口响应: {feedback_operate_response.json()}")
    except requests.exceptions.RequestException as e:
        print(f"/feedback/operate 接口请求发生错误: {e}")

if __name__ == "__main__":
    # 调用所有测试函数
    test_conversation_chat()
    test_user_history()
    test_conversation_history()
    test_feedback_operate()