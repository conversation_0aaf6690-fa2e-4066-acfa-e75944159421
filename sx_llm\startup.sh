#!/bin/bash
# ----------------------------------------------------------------------
# name:         startup.sh
# version:      1.0
# createTime:   2024-05-12
# description:  沙盘模型服务后端启动脚本
# author:       liangkaizhi
# email:        <EMAIL>
# $1  当前服务器的环境,如dev
# -----------------------------------------------------------------------
# 在后台启动Python脚本，并将输出重定向到nohup.out文件（nohup的默认行为）
nohup python api.py &

# 指定pid文件名及路径
pid_file="./application.pid"

# 检查上一个命令（即nohup命令）的退出状态
# $? 保存了上一个命令的退出状态，0表示成功，非0表示失败
if [ $? -eq 0 ];then
    # 如果成功，将当前后台进程的PID写入指定的PID文件
    echo $! > $pid_file
else
    # 如果启动失败，打印错误消息并退出脚本（返回码1）
    # 注意：这个分支确实可能永远不会执行，因为nohup命令通常不会返回失败状态，除非它自身有问题（比如无法执行python或找不到脚本等）
    echo "start up failed!"
    exit 1
fi

# 打印成功消息，并显示运行中的PID（从PID文件中读取）
echo  "start success, running pid is $(cat $pid_file)"
