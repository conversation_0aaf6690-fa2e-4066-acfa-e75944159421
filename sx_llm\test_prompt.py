import numpy as np
import random

# 定义环境类
class GridWorld:
    def __init__(self, grid_size=5):
        self.grid_size = grid_size
        self.start_state = (0, 0)
        self.goal_state = (grid_size - 1, grid_size - 1)
        self.state = self.start_state
    
    # 重置环境
    def reset(self):
        self.state = self.start_state
        return self.state
    
    # 根据动作更新状态
    def step(self, action):
        x, y = self.state
        if action == 0:  # 上
            x = max(x - 1, 0)
        elif action == 1:  # 下
            x = min(x + 1, self.grid_size - 1)
        elif action == 2:  # 左
            y = max(y - 1, 0)
        elif action == 3:  # 右
            y = min(y + 1, self.grid_size - 1)
        
        self.state = (x, y)
        
        # 如果到达目标位置，给出奖励
        if self.state == self.goal_state:
            return self.state, 1, True  # (新的状态，奖励，是否结束)
        else:
            return self.state, 0, False
    
    # 获取可能的动作
    def action_space(self):
        return [0, 1, 2, 3]  # 上, 下, 左, 右

# 定义Q-learning算法类
class QLearningAgent:
    def __init__(self, env, alpha=0.1, gamma=0.99, epsilon=0.1):
        self.env = env
        self.q_table = np.zeros((env.grid_size, env.grid_size, 4))  # 初始化Q表
        self.alpha = alpha  # 学习率
        self.gamma = gamma  # 折扣因子
        self.epsilon = epsilon  # 探索率
    
    # 选择动作 (ε-greedy 策略)
    def choose_action(self, state):
        if random.uniform(0, 1) < self.epsilon:
            return random.choice(self.env.action_space())  # 随机探索
        else:
            x, y = state
            return np.argmax(self.q_table[x, y])  # 利用Q值选择最优动作
    
    # 更新Q表
    def update_q_table(self, state, action, reward, next_state):
        x, y = state
        next_x, next_y = next_state
        best_next_action = np.argmax(self.q_table[next_x, next_y])
        td_target = reward + self.gamma * self.q_table[next_x, next_y, best_next_action]
        td_error = td_target - self.q_table[x, y, action]
        self.q_table[x, y, action] += self.alpha * td_error

# 训练智能体
def train_agent(env, agent, episodes=1000):
    for episode in range(episodes):
        state = env.reset()
        done = False
        while not done:
            action = agent.choose_action(state)
            next_state, reward, done = env.step(action)
            agent.update_q_table(state, action, reward, next_state)
            state = next_state
    print("训练完成！")

# 测试智能体
def test_agent(env, agent):
    state = env.reset()
    done = False
    total_reward = 0
    while not done:
        action = agent.choose_action(state)
        next_state, reward, done = env.step(action)
        total_reward += reward
        state = next_state
        print(f"状态：{state}，动作：{action}，奖励：{reward}")
    print(f"总奖励：{total_reward}")

# 初始化环境和智能体
env = GridWorld()
agent = QLearningAgent(env)

# 训练智能体
train_agent(env, agent)

# 测试智能体
test_agent(env, agent)
