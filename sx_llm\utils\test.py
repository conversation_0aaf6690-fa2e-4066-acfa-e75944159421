# import base64
# import hashlib
# from Crypto.Cipher import DES
# from Crypto.Protocol.KDF import PBKDF2

# def decrypt_jasypt(enc_text, password, iterations=1000):
#     # 移除 ENC() 包裹
#     enc_text = enc_text.replace("ENC(", "").replace(")", "")
    
#     # Base64 解码
#     enc_text_bytes = base64.b64decode(enc_text)
    
#     # 生成密钥和 iv（没有盐值时使用固定值）
#     key_iv = PBKDF2(password, b'', dkLen=16, count=iterations)
#     key, iv = key_iv[:8], key_iv[8:]
    
#     # 创建 DES 解密器
#     cipher = DES.new(key, DES.MODE_CBC, iv)
    
#     # 解密并移除填充
#     decrypted = cipher.decrypt(enc_text_bytes)
#     decrypted = decrypted[:-decrypted[-1]]  # PKCS5 填充移除
    
#     return decrypted.decode("utf-8")



# # 示例解密调用
# encrypted_text = "ENC(t0qjyGqb8Bvw3lgF4cN1JA==)"
# password = "pbDJNEJi7oQ%"
# print("Decrypted text:", decrypt_jasypt(encrypted_text, password))

# from sx_data_load import *

# def clean_location(location):
#     if location == [''] or location == []:
#         return ["Nationwide"]
    
#     location_map = set()
    
#     for i in location:
#         if '全国' in location or 'Nationwide' in location:
#             location_map.add('Nationwide')
#             continue
#         tmp_city = [tmp for tmp in data_city if tmp.replace('市','') in i.replace('吉林省','')]
#         if len(tmp_city) > 1:
#             tmp_city.remove('吉林市')
#         if len(tmp_city) > 0:
#             location_map.update(tmp_city)
#             continue
#         tmp_province = [tmp for tmp in data_province if tmp.replace('省','') in i]
#         if len(tmp_province) > 0:
#             location_map.update(tmp_province)
#             continue
#         tmp_rssc = [tmp.replace('大区','') for tmp in data_rssc if tmp.replace('大区','') in i]
#         if len(tmp_rssc) > 0:
#             location_map.update(tmp_rssc)
#             continue
    
#     location_map = list(location_map)
#     if location_map == []:
#         return ["Nationwide"]
#     return location_map

# if __name__ == "__main__":
#     print(clean_location(['吉林市','吉林市']))
#     print(clean_location(['吉林']))
#     print(clean_location(['吉林省吉林市']))


# # 示例json对象
# json_data = {
#     "apple": 10,
#     "banana": 5,
#     "orange": 7,
#     "grape": 12
# }

# # 示例list
# my_list = ["apple", "orange", "grape"]

# # 简化版，使用 filter 和 map
# new_list = list(map(json_data.get, my_list))

# print(new_list)




# # 输入数据
# a = [{'time': '20241101', 'volume': 100, 'mom': 0.55, 'yoy': 0.18},
#      {'time': '20241103', 'volume': 200, 'mom': 0.40, 'yoy': 0.37},
#      {'time': '20241102', 'volume': 300, 'mom': 0.30, 'yoy': 0.26}]




# output = [{'time': i['time'],
#         # 只在需要时添加额外的字段
#         **(
#             {'volume': i['volume']} 
#             if True else {}
#         )} for i in a]
# print(output)

# import json
# content = [1,2]

# if not isinstance(content, str):
#     content = json.dumps(content,ensure_ascii=False)
#     print(content)


# from bs4 import BeautifulSoup
# import requests

# # 发送HTTP请求获取网页内容
# url = 'https://zhuanlan.zhihu.com/p/706892127'
# response = requests.get(url)
# html_content = response.text

# # 创建BeautifulSoup对象
# soup = BeautifulSoup(html_content, 'html.parser')

# print(soup)

# 查找并打印所有<a>标签的href属性
# for a_tag in soup.find_all('a'):
#     href = a_tag.get('href')
#     print(href)



# import requests
# import json


# def main():
        
#     url = "https://qianfan.baidubce.com/v2/app/conversation"
    
#     payload = json.dumps({
#         "app_id": "6084778e-0b07-4fdb-b47d-7e62b75d3d99"
#     })
#     headers = {
#         'Content-Type': 'application/json',
#         'X-Appbuilder-Authorization': 'Bearer bce-v3/ALTAK-ZrMhSRBne30FDyOyJMXog/e5ae6aa437c75a5a940033899a3ee15094cb981e'
#     }
    
#     response = requests.request("POST", url, headers=headers, data=payload)
    
#     print(response.text)
    

# if __name__ == '__main__':
#     main()




# import requests
# import json


# def main():
        
#     url = "https://qianfan.baidubce.com/v2/app/conversation/runs"
    
#     payload = json.dumps({
#         "app_id": "6084778e-0b07-4fdb-b47d-7e62b75d3d99",
#         "stream": True,
#         "conversation_id": "a9879883-6dfb-4b14-a380-0b49a2314720",
#         "query": "返回网页的全部内容：https://www.msn.cn/zh-cn/news/other/%E9%A9%AC%E6%96%AF%E5%85%8B560%E4%BA%BF%E7%BE%8E%E5%85%83-%E5%A4%A9%E4%BB%B7%E8%96%AA%E9%85%AC%E8%AE%A1%E5%88%92-%E5%8F%97%E9%98%BB/ar-AA1v9Zui?ocid=hpmsn&cvid=94b30d95d78d44bcedc6f858c1089837&ei=17"
#     })
#     headers = {
#         'Content-Type': 'application/json',
#         'X-Appbuilder-Authorization': 'Bearer bce-v3/ALTAK-ZrMhSRBne30FDyOyJMXog/e5ae6aa437c75a5a940033899a3ee15094cb981e'
#     }
    
#     response = requests.request("POST", url, headers=headers, data=payload)
#     # response = json.loads(response.text)['answer']
    
#     # result = response
#     print(response.text)
#     # print(type(result))
    

# if __name__ == '__main__':
#     main()



# from datetime import datetime

# # 获取当前日期和时间
# now = datetime.now()

# # 格式化日期，只显示年月日
# today = now.strftime("%Y-%m-%d")

# print("今天的日期是：", today)


# import re
# import json
# import requests

# emp_id = '111'

# conversation_list = {}

# def create_conversation_id():
#     url = "https://qianfan.baidubce.com/v2/app/conversation"
#     payload = json.dumps({
#         "app_id": "6084778e-0b07-4fdb-b47d-7e62b75d3d99"
#     })
#     headers = {
#         'Content-Type': 'application/json',
#         'X-Appbuilder-Authorization': 'Bearer bce-v3/ALTAK-ZrMhSRBne30FDyOyJMXog/e5ae6aa437c75a5a940033899a3ee15094cb981e'
#     }
#     response = requests.request("POST", url, headers=headers, data=payload)
    
#     return json.loads(response.text)['conversation_id']

# def get_conversation_id(emp_id, conversation_list):
#     from datetime import datetime
#     today = datetime.now().strftime("%Y-%m-%d")
#     conversation_list.setdefault(emp_id, {})

#     if today not in conversation_list[emp_id]:
#         conversation_list[emp_id][today] = create_conversation_id()

#     return conversation_list[emp_id][today]


# conversation_id = get_conversation_id(emp_id, conversation_list)

# def appbuilder_agent(user_query: str,conversation_id: str, stream: bool = False) -> object:
#   url = "https://qianfan.baidubce.com/v2/app/conversation/runs"

  
#   payload = json.dumps({
#       "app_id": "6084778e-0b07-4fdb-b47d-7e62b75d3d99",
#       "stream": stream,
#       "conversation_id": conversation_id,
#       "query": user_query
#   })
#   headers = {
#       'Content-Type': 'application/json',
#       'X-Appbuilder-Authorization': 'Bearer bce-v3/ALTAK-ZrMhSRBne30FDyOyJMXog/e5ae6aa437c75a5a940033899a3ee15094cb981e'
#   }
  
#   response = requests.request("POST", url, headers=headers, data=payload, stream=stream)
#   return response



# def answer_return(response: object, language: str, stream: bool = False):
#   result_text = ""
#   if stream:
#     for line in response.iter_lines():
#         line = line.decode("UTF-8")
#         result_match = re.search(r'"answer": "(.*?)", "conversation_id"', line, re.DOTALL)
#         if result_match:
#             answer = result_match.group(1)
#             print(answer)
#   else:
#     answer = json.loads(response.text)['answer']
#     print(answer)
    
# user_query = 'https://www.baidu.com/'

# response = appbuilder_agent(user_query,'44196e63-5184-4bce-ba5d-5e5fcb614672',True)
# answer_return(response=response, language='zh', stream=True)
    

# a = 1
# b = a + 1

# def example_generator(x):
#     if x == 1:
#         yield "First yield"
#         yield "Second yield"
#     else:
#         def b():
#             yield "First yield"
#             yield "Second yield"
#         yield from b()

# c = example_generator(x=0)

# for i in c:
#     print(i)

# gen = example_generator()  # 返回生成器对象
# for value in gen:
#     print(value)

# import markdown2
# a = markdown2.markdown('https://gapi.shutu.cn/ai/edit-mind-url?works_guid=open3e220b1e1e8af7dd935f395050fece10_coze')
# print(a)


# import markdown2

# # 原始URL
# url = 'https://gapi.shutu.cn/ai/edit-mind-url?works_guid=open3e220b1e1e8af7dd935f395050fece10_coze'

# # 直接输出URL
# print(url)

# # 或者转换为Markdown链接
# markdown_link = f"[链接]({url})"
# print(markdown_link)



# jump_link = 'https://gapi.shutu.cn/ai/edit-mind-url?works_guid=open2f8ac5ab0cc9cb9ee21728a5069160c0_coze'
# jump_link_title = "Online Edit"
# markdown_jump_link = f'<a href="{jump_link}" target="_blank">{jump_link_title}</a>'

# pic_link = 'https://static.shutu.cn/shutu/static/2023/10/20/1aec90/1aec90aaec8b95889328e2d00c575033.jpeg'
# pic_link_title = "Mind Map"
# markdown_pic = f"![{pic_link_title}]({pic_link})"

# result = f"{markdown_pic}\n{markdown_jump_link}"


# print(markdown2.markdown(result, extras=["fenced_code", "footnotes"]))


# def sub_generator(a):
#     if a:
#         yield 1
#         yield 2
#     else:
#         return "Sub-generator done"
# a = False
# print(sub_generator(a))


# def main_generator():
#     result = yield from sub_generator()
#     yield f"Main generator got: {result}"

# result = yield from sub_generator()

# gen = main_generator()
# print(gen)
# for value in gen:
#     print(value)

# import requests

# cskb_url = "http://10.122.83.46:8573"   #生产环境
# dir_tree_url = f"{cskb_url}/cskb/open/api/v1/directory?type=1"

# access_token = '99f747e3-687b-4a5f-81c3-51513ea192a5'
# user_id = ''

# headers = {        
#     "Content-Type": "application/json",    
#     "charset": "utf-8",
#     "Authorization": access_token,
#     "X-USER-ID": user_id
# }

# response = requests.get(dir_tree_url, headers=headers)
# answer = response.json()['data']

# temp_dir_dict = {}
# for dir in answer:
#     dir_id = dir['id']
#     dir_name = dir['name']
#     dir_parent_id = dir['parentId']
#     temp_dir_dict[dir_id] = {"name": dir_name, "parent_id": dir_parent_id, "full_dir_name": dir_name}




# import os
# import appbuilder


# # 请前往千帆AppBuilder官网创建密钥，流程详见：https://cloud.baidu.com/doc/AppBuilder/s/Olq6grrt6#1%E3%80%81%E5%88%9B%E5%BB%BA%E5%AF%86%E9%92%A5
# os.environ["APPBUILDER_TOKEN"] = 'bce-v3/ALTAK-ZrMhSRBne30FDyOyJMXog/e5ae6aa437c75a5a940033899a3ee15094cb981e'


# doc_format_converter = appbuilder.DocFormatConverter()

# image_url = "https://ai-cape-strategy-data.bj.bcebos.com/document-restructure/1EF33F9307451C9413D5D1160.jpg"

# resp = doc_format_converter(appbuilder.Message({"file_path": image_url}))
# # 输出{"word_url":"", "excel_url":""}
# print(resp.content)


# from appbuilder.core.components.doc_parser.doc_parser import DocParser
# from appbuilder.core.message import Message
# import os
# import requests


# os.environ["APPBUILDER_TOKEN"] = "bce-v3/ALTAK-ZrMhSRBne30FDyOyJMXog/e5ae6aa437c75a5a940033899a3ee15094cb981e"

# # 进行文档内容解析
# # file_url = "https://agi-dev-platform-bos.bj.bcebos.com/ut_appbuilder/test.pdf?authorization=bce-auth-v1/e464e6f951124fdbb2410c590ef9ed2f/2024-01-25T12%3A56%3A15Z/-1/host/b54178fea9be115eafa2a8589aeadfcfaeba20d726f434f871741d4a6cb0c70d"
# # file_data = requests.get(file_url).content
# file_path = "c:/Users/<USER>/Desktop/userguide.pdf"  # 待解析的文件路径
# # with open(file_path, "wb") as f:
# #     f.write(file_data)
# msg = Message(file_path)
# parser = DocParser()
# parse_result = parser(msg)
# print(parse_result.content)

# import json
# import base64
# from cryptography.hazmat.backends import default_backend
# from cryptography.hazmat.primitives.hashes import SHA256
# from cryptography.hazmat.primitives.asymmetric import padding
# from cryptography.hazmat.primitives.serialization import load_pem_private_key



# # 3. Base64Url 编码函数
# def base64url_encode(data):
#     data_json = json.dumps(data)
#     encoded = base64.urlsafe_b64encode(data_json.encode('utf-8'))  # 使用 URL 安全的 Base64 编码
#     return encoded.decode('utf-8').rstrip('=')  # 去掉填充符号 '='

# def generate_rs256_token(header, payload, private_key_pem):
 
#     # 拼接待签名字符串
#     message = f"{header}.{payload}"
    
#     # 加载私钥
#     private_key = load_pem_private_key(private_key_pem.encode('utf-8'), password=None, backend=default_backend())
    
#     # 使用 RS256 签名
#     signature = private_key.sign(
#         message.encode('utf-8'),
#         padding.PKCS1v15(),
#         SHA256()
#     )
    
#     # 对签名进行 Base64URL 编码
#     encoded_signature = base64url_encode(signature)
    
#     # 拼接最终的 JWT
#     token = f"{message}.{encoded_signature}"
#     return token


# header = {
#     "alg": "RS256",          # 签名算法
#     "typ": "JWT",            # 类型
#     "kid": "EXXXRIHdTRXw-idSD0R9gz1cVC1TsDcncb7QFshty-U" # 密钥标识符
# }

# # 4. 编码 Header
# header_base64url = base64url_encode(header)

# payload = {
#     "iss": "1293683334274",   
#     "aud": "api.coze.cn",   
#     "iat": 1516239022,       
#     "exp": 1734771336,       
#     "jti": "fhjashjgkhalskj" 
# }

# payload_base64url = base64url_encode(payload)
# print(payload_base64url)


# private_key_pem = """-----BEGIN PRIVATE KEY-----
# MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCzht/hOapazXSV
# 7B2w9M99sbyULnbUGaqwYILef2mwGhDABBqlgH0xW9PulMAL0vOs0Gl6YtPvn7bA
# BisGnkCs0bZJ1TsMpTanM1ViJ9SH4UGGKgEevntiWAAw3xOkgD32J62miZsF9Gs5
# lRyuu3xYYofYxow6sMAi+Xb41Qv03ZC1gtRM1eW+nTm7KHmN4tZb1Zr7DpdmzE20
# E4qxgMuhTkoG1uldknvgj1sRIHopxzxpJkrIJgHsYIHTL7zMySimq4Pp5LuXlB47
# YlY/Z3sx2/fbb0Csl0Y7tfhTsJi0poaYDLswKKrh8JOyRp/2ObSVPDevsZP1Cmk9
# ztpSwWYLAgMBAAECggEAA/659+ODBi13eeg2aojhuF5YLMP/Io35RAUhytAmPfD8
# 8zwkJUKyubWqOOKLU4TVco1z8desXhmKK5CRLIu6mYRUYvHUmpEJCLd8hiHbLOe3
# Jl33U51rIxXqPl9e3iF5P4N3VDMDuO8ASj7zOg0AbpPivTdnfEOqqU/IqGKCHZs3
# Aq3JXUeA+jR9gWyxwlpLIjxvnjUM+e5wzHMlJ0lDwVbe4PtKeZzdZPvGbLlzDJHy
# kUulV+GKZ24EJuPMODk6icl13z/+sh0dVxa/QCggmv+EREjJW4pAe5oTce1b7MiE
# sLi356bS1h7k28bBcoDqs8aVO0dwvjqt2sRMvAo5IQKBgQDyR0YMOI7iwdbJpQlY
# DDq6QhDCS1X6X2h/x47xxOtDKhaXg6TayAlZ0DvVlhkQc830LqD0M78luh5+UuOi
# hflkyK97tls1QLyib1m+ubtz2Pcjy+1KqJv2S1aKSuOVJqJMslirATu5mzaTnhgJ
# NmYZ/9pCDLmosPqp2ScxI8FptQKBgQC9scij31L5I2qCJXaey4MyeeWpdb9bWMzJ
# WBh3oyMcuMH6/8uj1zs6vEkX2/I/gkMEiOmP0R9DGZv2AGBXFADZ8ljGN0gqRQpq
# ET/KaLZtT5qryu803rJJJ2SDGRb5oS1kyn5zQqsQ/NavtOYxs3mopSp0QaylUhC3
# RoGNUbpovwKBgQCrSOJn73BtNfdhFfGWMwct+VkZ0+MUIHGjrPUNTle5Yz8XnTVm
# i3e47HPw264aqJSZpoGaRB9GT+ARFQ7kLu2TaCJQRysSahMyj6CWK38wNtr6jsjT
# OIMSzE6BdNKS9v5VVN1FAJk17lrWYDXEBZaHmQ+4FIEJlx2QCPshTZMG5QKBgQCv
# M67uu+s0tGNBBT0D+U+ZpUBbPaeZxPCP3k/q38AkXPi8zNjW4ImR/Ck+YiTiR3uC
# irm93RYUsWh802gnxQ/sPIuFt7VtZQrzYC8c0Y38dBOVK/+3QyBDtKB+/QNdWGJG
# v0f/SMobDIt1CY26xdtNyg7mBWph3uQVgodRcPTMnQKBgDiqKNwaJ0XRCFxWxxXb
# LU+kB5d6xmq7IlAbW0EgM6JB+eS4TzpIfoGGklfd8JeneglrE+h6tw61BROaMcfj
# +00gqcjG0o49jvjNc6wUx/bt2KRs8MebPJ4boyrLz5N8yrQjyXbiRa0nsvfIB5vX
# PMtHIITP5186sof1iOk2/KG5
# -----END PRIVATE KEY-----"""


# jwt = generate_rs256_token(header_base64url, payload_base64url, private_key_pem)
# print(jwt)

# # # 示例完整 JWT (只含 Header 和占位的 Payload)
# # payload = {"sub": "1234567890", "name": "John Doe", "iat": 1516239022}
# # payload_base64url = base64url_encode(json.dumps(payload))

# # jwt = f"{header_base64url}.{payload_base64url}."  # 签名部分暂时为空
# # print("\nGenerated JWT:")
# # print(jwt)





# import base64
# import json
# import hashlib
# from cryptography.hazmat.primitives.asymmetric import padding
# from cryptography.hazmat.primitives.hashes import SHA256
# from cryptography.hazmat.primitives.serialization import load_pem_private_key
# from cryptography.hazmat.backends import default_backend

# def base64url_encode(data):
#     """Base64URL 编码"""
#     return base64.urlsafe_b64encode(data).rstrip(b'=').decode('utf-8')

# def generate_rs256_token(header, payload, private_key_pem):
#     # 将 Header 和 Payload 转换为 JSON，并进行 Base64URL 编码
#     encoded_header = base64url_encode(json.dumps(header).encode('utf-8'))
#     encoded_payload = base64url_encode(json.dumps(payload).encode('utf-8'))
    
#     # 拼接待签名字符串
#     message = f"{encoded_header}.{encoded_payload}"
    
#     # 加载私钥
#     private_key = load_pem_private_key(private_key_pem.encode('utf-8'), password=None, backend=default_backend())
    
#     # 使用 RS256 签名
#     signature = private_key.sign(
#         message.encode('utf-8'),
#         padding.PKCS1v15(),
#         SHA256()
#     )
    
#     # 对签名进行 Base64URL 编码
#     encoded_signature = base64url_encode(signature)
    
#     # 拼接最终的 JWT
#     token = f"{message}.{encoded_signature}"
#     return token

# # 示例
# header = {
#     "alg": "RS256",
#     "typ": "JWT"
# }

# payload = {
#     "sub": "1234567890",
#     "name": "John Doe",
#     "admin": True
# }

# # 使用 PEM 格式的私钥


# # 生成 Token
# token = generate_rs256_token(header, payload, private_key_pem)
# print("Generated Token:", token)




# import faiss
# import numpy as np

# # 假设数据库向量和查询向量
# database_vectors = np.random.random((10000, 128)).astype('float32')  # 100 个 128 维向量
# query_vector = np.random.random((1, 128)).astype('float32')

# # 创建 PQ 索引
# d = 128  # 向量维度
# m = 16   # 子空间数量
# nbits = 8  # 每个子空间的量化位数
# quantizer = faiss.IndexFlatL2(d)
# index = faiss.IndexIVFPQ(quantizer, d, 256, m, nbits)
# index.train(database_vectors)
# index.add(database_vectors)

# # 查询最近邻
# k = 1  # 查找前 5 个最近邻
# distances, indices = index.search(query_vector, k)

# print("Distances:", distances)
# print("Indices:", indices)

# for idx in indices[0]:
#     print(vector_to_doc_map.get(idx, "Document not found"))




