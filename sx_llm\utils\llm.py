from typing import List,Any,Optional,Mapping
import json
import requests
import openai
import aiohttp

from langchain.llms.base import LLM
from langchain.callbacks.manager import CallbackManagerForLLMRun

# from config import *
# from sx_date import *
# from sx_log import sx_log, process_log
from utils.config import *
from utils.sx_date import *
from utils.sx_log import sx_log, process_log



from openai import AzureOpenAI

# unicode打印为中文
import codecs
def decode_unicode(obj):
    if isinstance(obj, dict):
        return {decode_unicode(key): decode_unicode(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [decode_unicode(element) for element in obj]
    elif isinstance(obj, str):
        return codecs.decode(obj, 'unicode_escape')
    else:
        return obj


result_list = []





class WenxinLLM(LLM):
    """百度文心一言"""

    @property
    def _llm_type(self) -> str:
        """Return type of llm."""
        return "baidu-wenxin"

    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
    ) -> str:
        """First try to lookup in queries, else return 'foo' or 'bar'."""
        
        api_reponse = self.chat_by_token(prompt, stream=False)

        if "error_code" in api_reponse:
            response = api_reponse["error_msg"]
        else:
            response = api_reponse["result"]

        return response

    @property
    def _identifying_params(self) -> Mapping[str, Any]:
        return {}

    def get_access_token(self):
        """根据API Key、Secret Key换取access_token"""
            
        url = f"{BAIDU_BCE_PROXY_URL}/oauth/2.0/token?grant_type=client_credentials&client_id={BAIDU_CLIENT_ID}&client_secret={BAIDU_CLIENT_SECRET}"

        payload = json.dumps("")
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'}
        
        access_time = datetime.datetime.now()
        response = requests.request("POST", url, headers=headers, data=payload)
        response_json = json.loads(response.text)
        access_token = response_json["access_token"]

        return access_time, access_token

    def update_access_token(self):
        """更新access_token"""
        global BAIDU_ACCESS_TIME
        global BAIDU_ACCESS_TOKEN

        try:
            BAIDU_ACCESS_TIME = datetime.strptime(BAIDU_ACCESS_TIME, '%Y-%m-%dT%H:%M:%S.%f')
        except:
            BAIDU_ACCESS_TIME = datetime(2023,1,1)
        
        if (datetime.datetime.now()-BAIDU_ACCESS_TIME).days <= 20:
            print('Token无需更新')
            return 

        access_time, access_token = self.get_access_token()
        if access_token is None:
            print('Token返回值为空')
            return
        
        BAIDU_ACCESS_TIME, BAIDU_ACCESS_TOKEN = access_time, access_token
        # cfg.setValue('API_BAIDU', 'ACCESS_TIME', BAIDU_ACCESS_TIME)
        # cfg.setValue('API_BAIDU', 'ACCESS_TOKEN', BAIDU_ACCESS_TOKEN)
        # print('初始化完成, access_token已更新')

        return
    
    def chat_by_token(self, content:str, stream=False):
        """根据access_token进行对话

        Params
            message: list 聊天上下文信息
                    (1)messages成员不能为空;1个成员表示单轮对话,多个成员表示多轮对话。
                    (2)最后一个message为当前请求的信息,前面的message为历史对话信息。
                    (3)必须为奇数个成员,成员中message的role必须依次为user、assistant。
                    (4)最后一个message的content长度(即此轮对话的问题)不能超过2000个字符;
                    如果messages中content总长度大于2000字符,系统会依次遗忘最早的历史会话,直到content的总长度不超过2000个字符。

                    单轮请求示例
                    [
                        {"role":"user","content":"介绍一下你自己"}
                    ]
                    多轮请求示例
                    [
                        {"role":"user","content":"请介绍一下你自己"},
                        {"role":"assistant","content":"我是百度公司开发的人工智能语言模型，我的中文名是文心一言，英文名是ERNIE Bot，可以协助您完成范围广泛的任务并提供有关各种主题的信息，比如回答问题，提供定义和解释及建议。如果您有任何问题，请随时向我提问。"},
                        {"role":"user","content": "我在上海，周末可以去哪里玩？"}
                    ]
            
            stream: bool 是否以流式接口的形式返回数据,默认false。
            user_id: str 最终用户的唯一标识符，可以监视和检测滥用行为，防止接口恶意调用。

        Return
            id: string 本轮对话的id。
            object: string 回包类型。
                    chat.completion 多轮对话返回
            created: int 时间戳。
            sentence_id: int 表示当前子句的序号。只有在流式接口模式下会返回该字段。
            is_end: bool 表示当前子句是否是最后一句。只有在流式接口模式下会返回该字段。
            result: string 对话返回结果。
            need_clear_history: bool 表示用户输入是否存在安全，是否关闭当前会话，清理历史回话信息。
                                true表示用户输入存在安全风险,建议关闭当前会话,清理历史会话信息。
                                false:否,表示用户输入无安全风险。
            usage: token统计信息,token数 = 汉字数+单词数*1.3 （仅为估算逻辑）。
                prompt_tokens-问题tokens数,completion_tokens-回答tokens数,total_tokens-tokens总数。
        """
        # self.update_access_token()
        access_time, access_token = self.get_access_token()
        BAIDU_ACCESS_TIME, BAIDU_ACCESS_TOKEN = access_time, access_token
        url = f"{BAIDU_BCE_PROXY_URL}/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-4.0-8k-0613?access_token={BAIDU_ACCESS_TOKEN}"
        payload = json.dumps({
            "messages": [{"role": "user", "content": content}],
            "temperature": 0.01,
            "response_format": 'text',
            "disable_search": True,
            "stream": stream})
        headers = {'Content-Type': 'application/json'}

        # 使用函数转换JSON字典中的Unicode编码
        payload_decoded = decode_unicode(payload)

        # 记录模型调用的全部内容
        sx_log.debug(f"日志类型：模型调用的全部内容 - 日志内容：{process_log(payload_decoded)}")

        response = requests.request("POST", url, headers=headers, data=payload)
        
        response_json = json.loads(response.text)
        return response_json


    async def async_chat_by_token(self, content:str, stream=False):
        access_time, access_token = self.get_access_token()
        BAIDU_ACCESS_TIME, BAIDU_ACCESS_TOKEN = access_time, access_token
        url = f"{BAIDU_BCE_PROXY_URL}/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-4.0-8k-0613?access_token={BAIDU_ACCESS_TOKEN}"
        payload = json.dumps({
            "messages": [{"role": "user", "content": content}],
            "temperature": 0.01,
            "response_format": 'text',
            "disable_search": True,
            "stream": stream})
        headers = {'Content-Type': 'application/json'}

        # 使用函数转换JSON字典中的Unicode编码
        payload_decoded = decode_unicode(payload)

        # 记录模型调用的全部内容
        sx_log.debug(f"日志类型：模型调用的全部内容 - 日志内容：{process_log(payload_decoded)}")

        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, data=payload) as response:
                response_text = await response.text()
                response_json = json.loads(response_text)
                return response_json


class WenxinLLMStream(LLM):
    """百度文心一言"""

    @property
    def _llm_type(self) -> str:
        """Return type of llm."""
        return "baidu-wenxin"

    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
    ) -> str:
        """First try to lookup in queries, else return 'foo' or 'bar'."""
        
        api_reponse = self.chat_by_token(prompt, stream=False)

        if "error_code" in api_reponse:
            response = api_reponse["error_msg"]
        else:
            response = api_reponse["result"]

        return response

    @property
    def _identifying_params(self) -> Mapping[str, Any]:
        return {}

    def get_access_token(self):
        """根据API Key、Secret Key换取access_token"""
            
        url = f"{BAIDU_BCE_PROXY_URL}/oauth/2.0/token?grant_type=client_credentials&client_id={BAIDU_CLIENT_ID}&client_secret={BAIDU_CLIENT_SECRET}"

        payload = json.dumps("")
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'}
        
        access_time = datetime.datetime.now()
        response = requests.request("POST", url, headers=headers, data=payload)
        response_json = json.loads(response.text)
        access_token = response_json["access_token"]

        return access_time, access_token

    def update_access_token(self):
        """更新access_token"""
        global BAIDU_ACCESS_TIME
        global BAIDU_ACCESS_TOKEN

        try:
            BAIDU_ACCESS_TIME = datetime.strptime(BAIDU_ACCESS_TIME, '%Y-%m-%dT%H:%M:%S.%f')
        except:
            BAIDU_ACCESS_TIME = datetime(2023,1,1)
        
        if (datetime.datetime.now()-BAIDU_ACCESS_TIME).days <= 20:
            print('Token无需更新')
            return 

        access_time, access_token = self.get_access_token()
        if access_token is None:
            print('Token返回值为空')
            return
        
        BAIDU_ACCESS_TIME, BAIDU_ACCESS_TOKEN = access_time, access_token

        return
    
    def chat_by_token(self, content:list):
        # self.update_access_token()
        access_time, access_token = self.get_access_token()
        BAIDU_ACCESS_TIME, BAIDU_ACCESS_TOKEN = access_time, access_token
        url = f"{BAIDU_BCE_PROXY_URL}/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro?access_token={BAIDU_ACCESS_TOKEN}"
        payload = json.dumps({
            "messages": content,
            "temperature": 0.01,
            "stream": True})
        headers = {'Content-Type': 'application/json'}

        # 使用函数转换JSON字典中的Unicode编码
        payload_decoded = decode_unicode(payload)

        # 记录模型调用的全部内容
        sx_log.debug(f"日志类型：模型调用的全部内容 - 日志内容：{process_log(payload_decoded)}")

        response = requests.request("POST", url, headers=headers, data=payload, stream=True)
        return response




class DeepseekR1Stream(LLM):
    """Deepseek R1"""

    @property
    def _llm_type(self) -> str:
        """Return type of llm."""
        return "deepseek-R1"

    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
    ) -> str:
        """First try to lookup in queries, else return 'foo' or 'bar'."""
        
        api_reponse = self.chat_by_token(prompt, stream=False)

        if "error_code" in api_reponse:
            response = api_reponse["error_msg"]
        else:
            response = api_reponse["result"]

        return response

    @property
    def _identifying_params(self) -> Mapping[str, Any]:
        return {}

    # 火山云Deepseek
    def chat_by_token(self, content:list):
        client = openai.OpenAI(
                api_key=VOLCENGINE_WARROOM_API_KEY, 
                base_url=f"{VOLCENGINE_PROXY_URL}/api/v3",
                )
        response = client.chat.completions.create(
                    model=VOLCENGINE_DEEPSEEK_R1_END_POINT,
                    messages=content,
                    stream=True,
                )
        return response

    async def async_chat_by_token(self, content:list):
        client = openai.AsyncOpenAI(
                api_key=VOLCENGINE_WARROOM_API_KEY, 
                base_url=f"{VOLCENGINE_PROXY_URL}/api/v3",
                )
        response = await client.chat.completions.create(
                    model=VOLCENGINE_DEEPSEEK_R1_END_POINT,
                    messages=content,
                    stream=True,
                )
        return response


    # # 私有化部署Deepseek
    # def chat_by_token(self, content: list):
    #     client = openai.OpenAI(
    #         api_key=PRIVATE_DEEPSEEK_API_KEY,
    #         base_url=PRIVATE_DEEPSEEK_URL
    #     )
    #     response = client.chat.completions.create(
    #         model="DeepSeek-R1",
    #         messages=content,
    #         stream=True
    #     )
    #     return response


class DeepseekV3Stream(LLM):
    """Deepseek V3"""

    @property
    def _llm_type(self) -> str:
        """Return type of llm."""
        return "deepseek-R1"

    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
    ) -> str:
        """First try to lookup in queries, else return 'foo' or 'bar'."""
        
        api_reponse = self.chat_by_token(prompt, stream=False)

        if "error_code" in api_reponse:
            response = api_reponse["error_msg"]
        else:
            response = api_reponse["result"]

        return response

    @property
    def _identifying_params(self) -> Mapping[str, Any]:
        return {}


    async def async_chat_by_token(self, content:list):
        client = openai.AsyncOpenAI(
                api_key=VOLCENGINE_WARROOM_API_KEY, 
                base_url=f"{VOLCENGINE_PROXY_URL}/api/v3",
                )
        response = await client.chat.completions.create(
                    model=VOLCENGINE_DEEPSEEK_V3_END_POINT,
                    messages=content,
                    stream=True,
                    temperature=0.5
                )
        return response


    async def stream_response(self, content:list):
        client = openai.AsyncOpenAI(
                api_key=VOLCENGINE_WARROOM_API_KEY, 
                base_url=f"{VOLCENGINE_PROXY_URL}/api/v3",
                )
        response = await client.chat.completions.create(
                    model=VOLCENGINE_DEEPSEEK_V3_END_POINT,
                    messages=content,
                    stream=True,
                    temperature=0.5
                )
        ### 火山流式输出
        result = ''
        async for chunk in response:
            content = chunk.choices[0].delta.content
            result += content
            print(content, end='')
        return result


class DeepseekV3Stream0324(LLM):
    """Deepseek V3 0324"""

    @property
    def _llm_type(self) -> str:
        """Return type of llm."""
        return "deepseek-V3-0324"

    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
    ) -> str:
        """First try to lookup in queries, else return 'foo' or 'bar'."""
        
        api_reponse = self.chat_by_token(prompt, stream=False)

        if "error_code" in api_reponse:
            response = api_reponse["error_msg"]
        else:
            response = api_reponse["result"]

        return response

    @property
    def _identifying_params(self) -> Mapping[str, Any]:
        return {}


    async def async_chat_by_token(self, content:list):
        client = openai.AsyncOpenAI(
                api_key=VOLCENGINE_WARROOM_API_KEY, 
                base_url=f"{VOLCENGINE_PROXY_URL}/api/v3",
                )
        response = await client.chat.completions.create(
                    model=VOLCENGINE_DEEPSEEK_V3_0324_END_POINT,
                    messages=content,
                    stream=True,
                    temperature=0.5
                )
        return response


    async def stream_response(self, content:list):
        client = openai.AsyncOpenAI(
                api_key=VOLCENGINE_WARROOM_API_KEY, 
                base_url=f"{VOLCENGINE_PROXY_URL}/api/v3",
                )
        response = await client.chat.completions.create(
                    model=VOLCENGINE_DEEPSEEK_V3_0324_END_POINT,
                    messages=content,
                    stream=True,
                    temperature=0.5
                )
        ### 火山流式输出
        result = ''
        async for chunk in response:
            content = chunk.choices[0].delta.content
            result += content
            print(content, end='')
        return result


class Qwen3(LLM):
    """Qwen3-235b-a22b"""

    @property
    def _llm_type(self) -> str:
        """Return type of llm."""
        return "Qwen3-235b-a22b"

    @property
    def _identifying_params(self) -> Mapping[str, Any]:
        return {}

    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
    ) -> str:
        """First try to lookup in queries, else return 'foo' or 'bar'."""
        
        try:
            api_reponse = self.chat_by_token(prompt)
            response = api_reponse.choices[0].message.content

        except Exception as e:
            response = e

        return response

    def chat_by_token(self, content:str):
        client = openai.OpenAI(
                api_key=DASHSCOPE_API_KEY, 
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
                )
        response = client.chat.completions.create(
                    model='qwen3-235b-a22b',
                    messages=[{"role": "user", "content": content}],
                    stream=False,
                    temperature=0.1,
                    extra_body={"enable_thinking": False},
                )
        return response

    async def async_chat_by_token(self, content:list):
        client = openai.AsyncOpenAI(
                api_key=DASHSCOPE_API_KEY, 
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
                )
        response = await client.chat.completions.create(
                    model='qwen3-235b-a22b',
                    messages=content,
                    stream=False,
                    temperature=0.1,
                    extra_body={"enable_thinking": False},
                )
        return response


    async def stream_response(self, content:list):
        client = openai.AsyncOpenAI(
                api_key=DASHSCOPE_API_KEY, 
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
                )
        response = await client.chat.completions.create(
                    model='qwen3-235b-a22b',
                    messages=content,
                    stream=True,
                    temperature=0.1,
                    extra_body={"enable_thinking": False}
                )
        ### 火山流式输出
        result = ''
        async for chunk in response:
            content = chunk.choices[0].delta.content
            result += content
            print(content, end='')
        return result


class WenxinLLMEn(LLM):
    """百度文心一言连续对话"""

    @property
    def _llm_type(self) -> str:
        """Return type of llm."""
        return "baidu-wenxin"

    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
    ) -> str:
        """First try to lookup in queries, else return 'foo' or 'bar'."""
        
        api_reponse = self.chat_by_token(prompt, stream=False)

        if "error_code" in api_reponse:
            response = api_reponse["error_msg"]
        else:
            response = api_reponse["result"]

        return response

    @property
    def _identifying_params(self) -> Mapping[str, Any]:
        return {}

    def get_access_token(self):
        """根据API Key、Secret Key换取access_token"""
            
        url = f"{BAIDU_BCE_PROXY_URL}/oauth/2.0/token?grant_type=client_credentials&client_id={BAIDU_CLIENT_ID}&client_secret={BAIDU_CLIENT_SECRET}"

        payload = json.dumps("")
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'}
        
        access_time = datetime.datetime.now()
        response = requests.request("POST", url, headers=headers, data=payload)
        response_json = json.loads(response.text)
        access_token = response_json["access_token"]

        return access_time, access_token

    def update_access_token(self):
        """更新access_token"""
        global BAIDU_ACCESS_TIME
        global BAIDU_ACCESS_TOKEN

        try:
            BAIDU_ACCESS_TIME = datetime.strptime(BAIDU_ACCESS_TIME, '%Y-%m-%dT%H:%M:%S.%f')
        except:
            BAIDU_ACCESS_TIME = datetime(2023,1,1)
        
        if (datetime.datetime.now()-BAIDU_ACCESS_TIME).days <= 20:
            print('Token无需更新')
            return 

        access_time, access_token = self.get_access_token()
        if access_token is None:
            print('Token返回值为空')
            return
        
        BAIDU_ACCESS_TIME, BAIDU_ACCESS_TOKEN = access_time, access_token
        # cfg.setValue('API_BAIDU', 'ACCESS_TIME', BAIDU_ACCESS_TIME)
        # cfg.setValue('API_BAIDU', 'ACCESS_TOKEN', BAIDU_ACCESS_TOKEN)
        # print('初始化完成, access_token已更新')

        return
    
    def chat_by_token(self, content:str, stream=False):
        """根据access_token进行对话"""
        # self.update_access_token()
        access_time, access_token = self.get_access_token()
        BAIDU_ACCESS_TIME, BAIDU_ACCESS_TOKEN = access_time, access_token
        url = f"{BAIDU_BCE_PROXY_URL}/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro?access_token={BAIDU_ACCESS_TOKEN}"
        payload = json.dumps({
            "messages": [{"role": "user", "content": "现在开始请使用英文回答"},
                         {"role": "assistant", "content": "Sure, I can answer your questions in English. Please let me know what you need help with."},
                         {"role": "user", "content": content}],
            "temperature": 0.01,
            "stream": stream})
        headers = {'Content-Type': 'application/json'}

        # 使用函数转换JSON字典中的Unicode编码
        payload_decoded = decode_unicode(payload)

        # 记录模型调用的全部内容
        sx_log.debug(f"日志类型：模型调用的全部内容 - 日志内容：{process_log(payload_decoded)}")

        response = requests.request("POST", url, headers=headers, data=payload)
        
        response_json = json.loads(response.text)
        return response_json



class WenxinLLMChat(LLM):
    """百度文心一言连续对话"""

    @property
    def _llm_type(self) -> str:
        """Return type of llm."""
        return "baidu-wenxin"

    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
    ) -> str:
        """First try to lookup in queries, else return 'foo' or 'bar'."""
        
        api_reponse = self.chat_by_token(prompt, stream=False)

        if "error_code" in api_reponse:
            response = api_reponse["error_msg"]
        else:
            response = api_reponse["result"]

        return response

    @property
    def _identifying_params(self) -> Mapping[str, Any]:
        return {}

    def get_access_token(self):
        """根据API Key、Secret Key换取access_token"""
            
        url = f"{BAIDU_BCE_PROXY_URL}/oauth/2.0/token?grant_type=client_credentials&client_id={BAIDU_CLIENT_ID}&client_secret={BAIDU_CLIENT_SECRET}"

        payload = json.dumps("")
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'}
        
        access_time = datetime.datetime.now()
        response = requests.request("POST", url, headers=headers, data=payload)
        response_json = json.loads(response.text)
        access_token = response_json["access_token"]

        return access_time, access_token

    def update_access_token(self):
        """更新access_token"""
        global BAIDU_ACCESS_TIME
        global BAIDU_ACCESS_TOKEN

        try:
            BAIDU_ACCESS_TIME = datetime.strptime(BAIDU_ACCESS_TIME, '%Y-%m-%dT%H:%M:%S.%f')
        except:
            BAIDU_ACCESS_TIME = datetime(2023,1,1)
        
        if (datetime.datetime.now()-BAIDU_ACCESS_TIME).days <= 20:
            print('Token无需更新')
            return 

        access_time, access_token = self.get_access_token()
        if access_token is None:
            print('Token返回值为空')
            return
        
        BAIDU_ACCESS_TIME, BAIDU_ACCESS_TOKEN = access_time, access_token

        return
    
    def chat_by_token(self, content:list, stream=False):
        """根据access_token进行对话"""
        # self.update_access_token()
        access_time, access_token = self.get_access_token()
        BAIDU_ACCESS_TIME, BAIDU_ACCESS_TOKEN = access_time, access_token
        url = f"{BAIDU_BCE_PROXY_URL}/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro?access_token={BAIDU_ACCESS_TOKEN}"
        payload = json.dumps({
            "messages": content,
            "temperature": 0.01,
            "stream": stream})
        headers = {'Content-Type': 'application/json'}

        # 使用函数转换JSON字典中的Unicode编码
        payload_decoded = decode_unicode(payload)

        # 记录模型调用的全部内容
        sx_log.debug(f"日志类型：模型调用的全部内容 - 日志内容：{process_log(payload_decoded)}")
        response = requests.request("POST", url, headers=headers, data=payload)
        
        response_json = json.loads(response.text)
        return response_json


class GPTLLM(LLM):
    """GPT对话"""

    @property
    def _llm_type(self) -> str:
        """Return type of llm."""
        return "openai-GPT"

    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
    ) -> str:
        """First try to lookup in queries, else return 'foo' or 'bar'."""
        
        try:
            api_reponse = self.chat_by_token(prompt, stream=False)
            response = api_reponse.choices[0].message.content

        except Exception as e:
            response = e

        return response

    @property
    def _identifying_params(self) -> Mapping[str, Any]:
        return {}

    
    def chat_by_token(self, content:str, stream=False):
        client = AzureOpenAI(azure_endpoint = OPENAI_AZURE_ENDPOINT, 
                             api_key = OPENAI_API_KEY,  
                             api_version = OPENAI_API_VERSION)

        response_json = client.chat.completions.create(model = OPENAI_DEPLOYMENT_NAME, 
                                                  temperature = OPENAI_TEMPERATURE,
                                                  response_format = { "type": 'text' },
                                                  seed = OPENAI_SEED,
                                                  messages=[{"role": "user", "content": content}])
        # 记录模型调用的全部内容
        sx_log.debug(f"日志类型：模型调用的全部内容 - 日志内容：{process_log(content)}")

        return response_json


def test_WenxinLLM():
    llm = WenxinLLM()
    llm.response_format = 'json_object'
    question = "## 指令\n请完成意图识别任务，已知的意图有[\"查路线\"，\"查话费\",\"查手机号\"]，分别对应的函数如下：\n{\"查路线\": \"search_path\", \"查话费\": \"search_biling\", \"查手机号\": \"search_phone\"}\n你需要根据已经提供的符合openAPI规范的【参数配置】，然后将【输入】转变成满足规定的数据格式。\n输出中必须包含 <ApiCall> 和 </ApiCall> ，不要有任何额外信息。\n【参数配置】\n{\"properties\": [{\"intention\": {\"description\": \"用户的意图\", \"type\": \"string\"}},{\"function\": {\"description\": \"意图对应的function\", \"type\": \"string\"}}], \"required\": [\"intention\", \"function\"]}\n\n## 示例\n【输入】 我想查询从西二旗到清河的路线\n【返回】<ApiCall> {\"intention\": \"查路线\", \"function\": \"search_path\"}</ApiCall>\n\n【输入】 我想查话费\n【返回】<ApiCall> {\"intention\": \"查话费\", \"function\": \"search_biling\"}</ApiCall>\n\n【输入】 我想查询地铁站工作人员的手机号\n【返回】<ApiCall> {\"intention\": \"查手机号\", \"function\": \"search_phone\"}</ApiCall>\n\n\n请输出包含 <ApiCall> 和 </ApiCall> 的结果\n【输入】我想查询A到B的路线"
    result = llm.chat_by_token(question)
    print(result)

def test_WenxinLLMEn():
    llm_en = WenxinLLMEn()
    question = "今天是几号"
    result = llm_en.chat_by_token(question)
    print(result)


def test_WenxinLLMChat():
    llm_en = WenxinLLMChat()
    question = [{"role": "user", "content": "上汽大众是什么"},
                {"role": "assistant", "content": "上汽大众是中德合资的轿车巨头，全称是上海大众汽车有限公司，简称上汽大众。它是由上汽集团和德国大众汽车集团合资经营的汽车企业，成立于1984年，总部位于上海安亭。该企业主要生产和销售大众和柯斯达这两个品牌的汽车，产品覆盖A0级、A级、B级、C级、SUV、MPV等细分市场。大众品牌的车型包括：Polo家族、新桑塔纳家族、拉曼多家族、拉曼多、新帕萨特帕萨特、菲迪昂辉昂、途观丝路版等。"},
                {"role": "user", "content": "这家公司口碑怎么样"}]
    result = llm_en.chat_by_token(question)["result"]
    print(type(result))
    print(result)


def test_WenxinLLMStream():
    llm = WenxinLLMStream()
    question = [{"role": "user","content": "介绍下上汽大众，不少于50字"}]
    response = llm.chat_by_token(question)
    for line in response.iter_lines():
        # if line.decode("UTF-8").split(': ') != ['']:
        print(line.decode("UTF-8"))
        print(type(line.decode("UTF-8")))



def test_DeepseekR1Stream():
    llm = DeepseekR1Stream()
    question = [{"role": "user","content": "介绍下上汽大众，不少于50字"}]
    llm.chat_by_token(question)




llm_dict = {"wenxin":WenxinLLM(),
            "wenxinen":WenxinLLMEn(),
            "wenxinchat":WenxinLLMChat(),
            "gpt":GPTLLM(),
            "wenxinstream":WenxinLLMStream(),
            "deepseekr1stream":DeepseekR1Stream(),
            "deepseekv3stream":DeepseekV3Stream(),
            "deepseekv3stream0324":DeepseekV3Stream0324(),
            "qwen3":Qwen3()
            }


if __name__ == '__main__':
    test_DeepseekR1Stream()