from utils.sx_dict import sx_kpi_value_percentage_dict, sx_kpi_name, sx_kpi_table_dict
from utils.chart_processors.base_chart_processor import BaseChartProcessor


def data_filter(kpi: str, data: dict, not_total: str, data_times: list) -> bool:
    return kpi in data["kpiValues"].keys() and (
        (not_total == "True" and len(data_times) != 2) or data["time"] == "total"
    )


class TableChartProcessor(BaseChartProcessor):
    """Table chart processor"""
    
    def __init__(self, format_json) -> None:
        super().__init__(format_json)
    
    def chart_process(self, data, language):
        data_times = list(set([i["time"] for i in data]))
        chart_list = []
        
        
        for kpi_temp in self.json_kpi:
            if kpi_temp in sx_kpi_value_percentage_dict.keys():
                kpi_value = sx_kpi_value_percentage_dict[kpi_temp]["value"]

                # 获取 kpi_percentage（如果存在）
                kpi_percentage = sx_kpi_value_percentage_dict.get(kpi_temp, {}).get("percentage")

                # 生成输出数据
                output_data = [
                    {
                        sx_kpi_name["time"][language]: i["time"],
                        sx_kpi_name["location"][language]: i["location"],
                        sx_kpi_name["model"][language]: i["model"],
                        sx_kpi_name[kpi_value][language]: i['kpiValues'][kpi_value],
                        # 如果 kpi_percentage 存在，添加对应字段
                        **(
                            {sx_kpi_name[kpi_percentage][language]: i['kpiValues'][kpi_percentage]} 
                            if kpi_percentage else {}
                        ),
                        **(
                            # 只有在 kpi_temp 不包含 '库存' 时才添加 mom 和 yoy 字段
                            {sx_kpi_name[sx_kpi_table_dict[kpi_value]['mom']][language]: f"{i['kpiValues'][sx_kpi_table_dict[kpi_value]['mom']]}%",
                            sx_kpi_name[sx_kpi_table_dict[kpi_value]['yoy']][language]: f"{i['kpiValues'][sx_kpi_table_dict[kpi_value]['yoy']]}%"} 
                            if '库存' not in kpi_temp else {}
                        ),
                        **(
                            # 如果 kpi_temp 是 '销量'，则添加 kpi_percentage 对应的 mom 和 yoy 字段
                            {sx_kpi_name[sx_kpi_table_dict[kpi_percentage]['mom']][language]: f"{i['kpiValues'][sx_kpi_table_dict[kpi_percentage]['mom']]}%",
                            sx_kpi_name[sx_kpi_table_dict[kpi_percentage]['yoy']][language]: f"{i['kpiValues'][sx_kpi_table_dict[kpi_percentage]['yoy']]}%"} 
                            if kpi_temp == '销量' else {}
                        )
                    }
                    for i in data if data_filter(kpi_value, i, self.json_not_total, data_times)
                ]

                # 如果 output_data 不为空，则添加到 chart_list
                if output_data:
                    chart_list.append({"type": "table", "table": {"data": output_data}})
                    
        return chart_list

