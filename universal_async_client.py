import httpx
import asyncio
import json
from typing import Dict, Union, Any, Optional

class AsyncApiClient:
    """通用异步API客户端，支持GET和POST请求"""
    
    def __init__(self, base_url: str, auth_token: str = None, timeout: float = 30.0):
        self.base_url = base_url
        self.auth_token = auth_token
        self.timeout = timeout
        self.default_headers = {
            'User-Agent': 'python-requests/2.32.3',
            'Accept-Encoding': 'gzip, deflate',
            'Accept': '*/*',
            'Connection': 'keep-alive'
        }
        if auth_token:
            self.default_headers['Authorization'] = auth_token
    
    async def request(self, 
                     method: str, 
                     endpoint: str, 
                     params: Dict = None, 
                     data: Dict = None, 
                     json_data: Dict = None,
                     headers: Dict = None,
                     debug: bool = True) -> Optional[Dict[str, Any]]:
        """
        发送请求并返回解析后的JSON结果
        
        参数:
            method: 请求方法，'GET' 或 'POST'
            endpoint: API端点（相对路径）
            params: URL查询参数（GET请求）
            data: 表单数据（POST请求）
            json_data: JSON数据（POST请求）
            headers: 自定义请求头
            debug: 是否打印调试信息
        
        返回:
            解析后的JSON数据或None（如果请求失败）
        """
        # 构建完整URL
        url = f"{self.base_url}/{endpoint}" if not endpoint.startswith('http') else endpoint
        url = url.rstrip('/')
        
        # 合并请求头
        request_headers = {**self.default_headers}
        if headers:
            request_headers.update(headers)
        
        if debug:
            print(f"请求方法: {method}")
            print(f"请求URL: {url}")
            if params:
                print(f"查询参数: {params}")
            if data:
                print(f"表单数据: {data}")
            if json_data:
                print(f"JSON数据: {json_data}")
            print(f"请求头: {request_headers}")
        
        try:
            # 创建异步客户端
            async with httpx.AsyncClient(
                timeout=self.timeout,
                verify=False,  # 禁用SSL验证，生产环境可能需要启用
                follow_redirects=True  # 允许重定向
            ) as client:
                if debug:
                    print("发送请求...")
                
                # 根据方法发送不同类型的请求
                if method.upper() == 'GET':
                    response = await client.get(url, params=params, headers=request_headers)
                elif method.upper() == 'POST':
                    # 选择使用表单数据还是JSON数据
                    if json_data is not None:
                        response = await client.post(url, params=params, json=json_data, headers=request_headers)
                    else:
                        response = await client.post(url, params=params, data=data, headers=request_headers)
                else:
                    raise ValueError(f"不支持的请求方法: {method}")
                
                if debug:
                    print(f"状态码: {response.status_code}")
                    print(f"响应头: {response.headers}")
                
                # 处理响应
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    if debug:
                        print(f"内容类型: {content_type}")
                        print(f"响应内容前100个字符: {response.text[:100] if response.text else 'N/A'}")
                    
                    # 尝试解析JSON
                    try:
                        json_response = response.json()
                        if debug:
                            print(f"成功解析JSON数据")
                        return json_response
                    except json.JSONDecodeError as e:
                        if debug:
                            print(f"JSON解析错误: {e}")
                            if '<html' in response.text.lower():
                                print("服务器返回了HTML而不是JSON")
                                if 'login' in response.text.lower():
                                    print("发现登录页面，可能需要重新认证")
                        
                        # 尝试直接解析文本内容为JSON
                        try:
                            if response.text and response.text.strip():
                                text = response.text.strip()
                                if text.startswith('{') and text.endswith('}'):
                                    return json.loads(text)
                        except:
                            pass
                        
                        return None
                else:
                    if debug:
                        print(f"请求失败，状态码: {response.status_code}")
                    return None
        
        except httpx.RequestError as e:
            if debug:
                print(f"请求错误: {e}")
            return None
        except Exception as e:
            if debug:
                print(f"未知错误: {type(e).__name__}: {e}")
            return None

# 使用示例
async def demo():
    # 创建客户端实例
    client = AsyncApiClient(
        base_url="http://************:8080/report/mobile/wechat",
        auth_token="SYAAkjENRNIAFVoQKylHQ51nZRNeX12-"
    )
    
    # GET请求示例
    get_result = await client.request(
        method="GET",
        endpoint="sv/afterSaleJyController/queryJyIndex",
        params={"yearMonth": "202301", "queryType": "M"}
    )
    print(f"GET请求结果: {get_result}")
    
    # POST请求示例
    post_result = await client.request(
        method="POST",
        endpoint="sv/afterSaleJyController/somePostEndpoint",  # 替换为实际的POST端点
        params={"param1": "value1"},  # URL参数
        json_data={"key1": "value1", "key2": "value2"}  # POST的JSON数据
    )
    print(f"POST请求结果: {post_result}")

if __name__ == "__main__":
    asyncio.run(demo()) 