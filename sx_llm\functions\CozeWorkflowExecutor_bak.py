import json
import async<PERSON>
from typing import Dict
from cozepy import COZE_CN_BASE_URL
from cozepy.auth import Async<PERSON><PERSON>Auth
from cozepy import AsyncJW<PERSON>AuthA<PERSON>, AsyncCoze


class CozeWorkflowExecutor:
    
    def __init__(self):
        file = open('d:\secret_keys\coze_oauth_python_jwt\coze_oauth_config.json', 'r', encoding='utf-8')
        data = json.load(file)
        self.jwt_oauth_client_id = data["client_id"]
        self.jwt_oauth_private_key =  data["private_key"]
        self.jwt_oauth_public_key_id = data["public_key_id"]
        self.coze_api_base = COZE_CN_BASE_URL
        self.access_token = None
        self.oauth_token = None
        self.coze_test = None
    
    
    async def get_oauth_token(self):
        # 初始化JWTAuth
        jwt_oauth_app = AsyncJWTOAuthApp(
            client_id=self.jwt_oauth_client_id,
            private_key=self.jwt_oauth_private_key,
            public_key_id=self.jwt_oauth_public_key_id,
            base_url=self.coze_api_base,
        )
        self.oauth_token = await jwt_oauth_app.get_access_token(ttl=3600)
        self.access_token = self.oauth_token.access_token
        print(self.access_token)
    
    
    async def _initialize(self):
        await self.get_oauth_token()
        self.coze_test = AsyncCoze(auth=AsyncTokenAuth(token=self.access_token), base_url=self.coze_api_base)
        
    async def execute_workflow(self, workflow_id: str, parameters: Dict) -> str:
        result = await self.coze_test.workflows.runs.create(workflow_id=workflow_id, parameters=parameters, is_async=True)
        return result
        

async def main():
    # 创建 CozeWorkflowExecutor 实例
    executor = CozeWorkflowExecutor()
    print(executor.jwt_oauth_public_key_id)
    
    # 初始化 executor
    await executor._initialize()
    
    # 定义要执行的 workflow_id 和 parameters
    workflow_id = '7474809031053377571'
    parameters = {
        'query_list': ['今天上海天气怎么样', '途昂pro'],
        'count': 1
    }
    
    # 执行工作流
    result = await executor.execute_workflow(workflow_id, parameters)
    
    # 打印结果
    print(result)

# 运行异步主函数
if __name__ == '__main__':
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"运行时出错: {e}")
    finally:
        # 强制清理循环（仅在某些情况下需要）
        loop = asyncio.get_event_loop()
        if not loop.is_closed():
            loop.close()
    