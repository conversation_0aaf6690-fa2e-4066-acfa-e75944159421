import time
import asyncio
import traceback


from utils.clean_json import extract_json_new
from agents.base_agent import BaseAgent
from agents.free_chat.prompt import *

class FreeChatAgent(BaseAgent):
    def __init__(self):
        super().__init__()  # 调用父类初始化
        self.data_all = []
    
    
    def get_profiles_from_manager(self, agent: BaseAgent):
        # 从管理类获取数据
        self.client = agent.client
        self.access_token = agent.access_token
        self.input_json = agent.input_json
        self.user_id = agent.user_id
        self.conversation_id = agent.conversation_id
        self.question = agent.question
        self.is_internet_search = agent.is_internet_search
        self.is_deep_thinking = agent.is_deep_thinking
        self.language = agent.language
        self.input_time = agent.input_time
        self.history_message = agent.history_message
        self.history_question = agent.history_question
        self.conversation_all = agent.conversation_all
        self.message_id = agent.message_id


    def create_task_functions(self):
        task_functions = []
        if self.is_internet_search:
            task_functions.append(self.get_external_information)
        return task_functions
    
    
    async def event_stream_queue(self, queue: asyncio.Queue):
        try:
            async for item in self.decompose_question():
                await queue.put(item)
            task_functions = self.create_task_functions()
            async for item in self.ordered_stream(task_functions):
                await queue.put(item)
            await queue.put(None)
        except asyncio.CancelledError:
            print("捕获到 CancelledError，开始清理")
        except Exception as e:
            e_msg = traceback.format_exc()
            self.save_log('异常捕获',e_msg)
            await queue.put(await self.format_sse_message('对不起，由于问题过于复杂，我暂时无法回答', content_type="text", ended=True))
            print('发生异常：',e_msg )
        finally:
            print("SSE返回已结束")


    async def decompose_question(self):
        if not self.is_deep_thinking:
            self.decompose_questions = [self.question]
            return
        # 分解用户输入的问题
        history_question = '\n'.join([str(index)+'.'+q for index, q in enumerate(self.history_question, start=1)])
        prompt = prompt_query_decomposition_add_history_template.format(current_question = self.question, history_question = history_question)
        content = [{"role": "user","content": prompt}]
        response = await self.process_llm.async_chat_by_token(content)
        
        is_thinking = True
        
        result = ''
        all_result = ''
        
        t1 = time.time()
        is_delete = False
        
        async for chunk in response:

            content = chunk.choices[0].delta.content
            content = content.replace('\n\n','\n')
            all_result += content
            
            
            if '<' in content:
                content_prefix = content.split('<')[0]
                is_delete = True
                self.thinking_content += content_prefix
                content_suffix = '<' + content.split('<')[-1]
                yield await self.format_sse_message(content=content_prefix, content_type="thinking")
                yield await self.format_sse_message(content='\n<delete>\n', content_type="thinking")
                yield await self.format_sse_message(content=content_suffix, content_type="thinking")
            elif '>' in content:
                is_thinking = False
                content_suffix = content.split(">")[-1]
                self.thinking_content += content_suffix
                result += content_suffix
                yield await self.format_sse_message(content=content, content_type="thinking")
            else:
                if not is_delete:
                    self.thinking_content += content
                yield await self.format_sse_message(content=content, content_type="thinking")
                if not is_thinking:
                    result += content
        
        t2 = time.time()
        self.save_log('新流程- 分解问题回答',result,time_cost=t2-t1)
          
        yield await self.format_sse_message(content='\n</delete>\n', content_type="thinking")
        self.thinking_content += '\n\n**问题分解**\n'
        yield await self.format_sse_message(content='\n\n**问题分解**\n', content_type="thinking")
        
        self.decompose_questions = extract_json_new(result)['查询问题']
        # 增加原始问题到分解问题列表中
        self.decompose_questions.append(self.question)
        questions_return = '\n'.join(self.decompose_questions)
        yield await self.format_sse_message(content=f'{questions_return}\n', content_type="thinking")
        self.thinking_content += f'{questions_return}\n'
        
        self.save_log('新流程- 添加了原始问题后的问题列表',self.decompose_questions,time_cost=t2-t1)
        
        self.save_log('新流程- 分解问题- 完整回答',all_result,time_cost=t2-t1)