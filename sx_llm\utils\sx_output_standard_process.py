def response_standard_process(type, **kwargs):

    if type == 'text':
        content = kwargs.get('content')
        return {"type": "text","text": {"content": content}}
    elif type == 'chart':
        chart_type = kwargs.get('chart_type')
        if chart_type == 'funnel':
            data = kwargs.get('data')
            return {"type": "chart","chart": {"chart_type": "funnel","list": data}}
        elif chart_type == 'table':
            data = kwargs.get('data')
            output_data = []
            for item in data:
                output_data.append({"name": item[0], "value": item[1]})


        list = kwargs.get('list')
{"type": "table","table": {"data": output_data}
        return {"type": "chart","chart": {"chart_type": "funnel","list": }
