import time
import json
import uuid
import asyncio
import traceback
from fastapi import Request

from utils.llm import llm_dict
from utils.sx_dict import *
from utils.sx_prompt import kpi_all
from utils.sx_date import get_date_info
from utils.sx_log import process_log, sx_log
from utils.config import MACHINE_IP
from utils.clean_json import extract_json_new
from functions.warroom_data_process import get_cleaned_json_list, format_cleaned_json_list, remove_empty_from_list, filter_warroom_data_for_llm
from functions.web_search import get_web_contents_batch, format_web_contents_batch, format_output_web_contents_batch
from agents.chat_new.prompt import *
from agents.data_analyse.data_analyse_agent import DataAnalyseAgent
from database.redis_db import RedisDB






class ChatNewAgent:
    def __init__(self):
        self.input_json = None
        self.user_id = None
        self.conversation_id = None
        self.question = None
        self.is_internet_search = False
        self.client = None
        self.language = '中文'
        self.intention = None
        self.kpi = None
        self.input_time = time.time()
        self.history_message = []
        self.history_question = []
        self.conversation_all = []
        self.analyse_llm = llm_dict['deepseekr1stream']
        self.process_llm = llm_dict['deepseekv3stream']
        self.process_llm_0324 = llm_dict['deepseekv3stream0324']
        self.decompose_questions = None
        self.answer_src = sx_answer_source[self.language]["LLM"]
        self.formatted_web_contents = None
        self.date_info = get_date_info()
        self.today = self.date_info['today_date_cn']
        self.weekday = self.date_info['today_weekday']
        self.json_list = None
        self.warroom_data = None
        self.is_data_question = None
        self.analysis_result = None
        self.final_answer = ''
        self.thinking_content = ''
        self.message_id = None
        self.charts = []
        
    async def get_input(self, request: Request):
        self.input_json = await request.json()
        self.save_log('请求内容',self.input_json)
        self.message_id = str(uuid.uuid4())
        self.input_time = time.time()
        self.user_id = self.input_json["user_id"]
        self.conversation_id = self.input_json["conversation_id"]
        self.question = self.input_json["question"]
        self.is_internet_search = self.input_json["is_internet_search"]
        self.is_deep_thinking = self.input_json["is_deep_thinking"]
        await self.get_conversation_history()
        await self.update_user_history()
        self.save_log('输入',self.question)


    async def update_user_history(self):
        redis_db = RedisDB()
        await redis_db.connect()
        try:
            user_history = await redis_db.get_history_all(f"user:{self.user_id}")
            if user_history and any(json.loads(item)["conversation_id"] == self.conversation_id for item in user_history):
                return 
            user_history_message = {"user_id": self.user_id, "conversation_id": self.conversation_id, "content": self.question, "time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), "order": len(user_history)+1}
            await redis_db.insert_conversation_message(f'user:{self.user_id}', json.dumps(user_history_message,ensure_ascii=False))
        except Exception as e:
            print(e)
        finally:
            await redis_db.close()


    async def get_conversation_history(self):
        """根据conversation_id获取对话历史"""
        
        if self.conversation_id:
            redis_db = RedisDB()
            await redis_db.connect()
            try:
                self.conversation_all = await redis_db.get_history_all(f"conversation:{self.conversation_id}")
                self.history_message = [{k: v for k, v in json.loads(item).items() if k in ['role', 'content']} for item in self.conversation_all]
                self.history_question = [item['content'] for item in self.history_message if item['role'] == 'user']
            finally:
                await redis_db.close()
        else:
            self.conversation_id = str(uuid.uuid4())
                
            
    async def update_conversation_history(self):
            redis_db = RedisDB()
            await redis_db.connect()
            try:
                
                user_message = {"user_id": self.user_id, "conversation_id": self.conversation_id, "role": "user", "content": self.question, "time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), "order": len(self.conversation_all)+1}
                assistant_message = {"user_id": self.user_id, "conversation_id": self.conversation_id, "message_id": self.message_id, "role": "assistant", "thinking_content": self.thinking_content, "content": self.final_answer, "charts": self.charts, "thinking_enabled": self.is_deep_thinking, "time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), "order": len(self.conversation_all)+2}
                await redis_db.insert_conversation_message(f'conversation:{self.conversation_id}', json.dumps(user_message,ensure_ascii=False))
                await redis_db.insert_conversation_message(f'conversation:{self.conversation_id}', json.dumps(assistant_message,ensure_ascii=False))
                self.history_question.append(self.question)
            except Exception as e:
                print('更新对话历史异常捕获', e)
            finally:
                await redis_db.close()
        

    async def classify_question(self):
        # 问题分类
        prompt = prompt_question_classification_template.format(current_question = self.question, history_question = self.history_question)
        content = [{"role": "user","content": prompt}]
        response = await self.process_llm.async_chat_by_token(content)
        result = ''
        async for chunk in response:
            content = chunk.choices[0].delta.content
            result += content
        self.is_data_question = result
        self.save_log('新流程-问题分类结果', self.is_data_question)

    
    async def decompose_question(self):
        if not self.is_deep_thinking:
            self.decompose_questions = [self.question]
            return
        # 分解用户输入的问题
        history_question = '\n'.join([str(index)+'.'+q for index, q in enumerate(self.history_question, start=1)])
        prompt = prompt_query_decomposition_add_history_template.format(current_question = self.question, history_question = history_question)
        content = [{"role": "user","content": prompt}]
        response = await self.process_llm.async_chat_by_token(content)
        
        is_thinking = True
        
        result = ''
        all_result = ''
        
        t1 = time.time()
        is_delete = False
        
        async for chunk in response:

            content = chunk.choices[0].delta.content
            content = content.replace('\n\n','\n')
            all_result += content
            
            
            if '<' in content:
                content_prefix = content.split('<')[0]
                is_delete = True
                self.thinking_content += content_prefix
                content_suffix = '<' + content.split('<')[-1]
                yield await self.format_sse_message(content=content_prefix, content_type="thinking")
                yield await self.format_sse_message(content='\n<delete>\n', content_type="thinking")
                yield await self.format_sse_message(content=content_suffix, content_type="thinking")
            elif '>' in content:
                is_thinking = False
                content_suffix = content.split(">")[-1]
                self.thinking_content += content_suffix
                result += content_suffix
                yield await self.format_sse_message(content=content, content_type="thinking")
            else:
                if not is_delete:
                    self.thinking_content += content
                yield await self.format_sse_message(content=content, content_type="thinking")
                if not is_thinking:
                    result += content
        
        t2 = time.time()
        self.save_log('新流程- 分解问题回答',result,time_cost=t2-t1)
          
        yield await self.format_sse_message(content='\n</delete>\n', content_type="thinking")
        self.thinking_content += '\n\n**问题分解**\n'
        yield await self.format_sse_message(content='\n\n**问题分解**\n', content_type="thinking")
        
        self.decompose_questions = extract_json_new(result)['查询问题']
        # 增加原始问题到分解问题列表中
        self.decompose_questions.append(self.question)
        questions_return = '\n'.join(self.decompose_questions)
        yield await self.format_sse_message(content=f'{questions_return}\n', content_type="thinking")
        self.thinking_content += f'{questions_return}\n'
        
        self.save_log('新流程- 添加了原始问题后的问题列表',self.decompose_questions,time_cost=t2-t1)
        
        self.save_log('新流程- 分解问题- 完整回答',all_result,time_cost=t2-t1)


    async def get_question_json_list(self):
        prompt = prompt_internal_query_json_template.format(question_list=json.dumps(self.decompose_questions,ensure_ascii=False), today=self.today, weekday=self.weekday, kpi_all=kpi_all)
        self.save_log('新流程- 内部查询问题转化为json的prompt',prompt)
        content = [{"role": "user","content": prompt}]
        response = await self.process_llm.async_chat_by_token(content)
        yield '<delete>'
        result = ''
        async for chunk in response:
            content = chunk.choices[0].delta.content
            yield content
            result += content
        yield '</delete>'
        self.save_log('新流程- 内部查询问题转化为json的结果',result)
        if 'json_list' in result:
            self.json_list = json.loads(result)['json_list']
            self.json_list = remove_empty_from_list(self.json_list)


    async def get_internal_information(self, queue: asyncio.Queue):
        if self.is_deep_thinking:
            await queue.put(await self.format_sse_message('\n\n**内部数据检索**\n', content_type="thinking"))
            self.thinking_content += '\n\n**内部数据检索**\n'
            await queue.put(await self.format_sse_message('销售数据查询：\n', content_type="thinking"))
            self.thinking_content += '销售数据查询：\n'
        async for chunk in self.get_question_json_list():
            if self.is_deep_thinking:
                await queue.put(await self.format_sse_message(chunk, content_type="thinking"))
        
        self.save_log('新流程- 从问题列表提取到的JSON列表', self.json_list)
        
        if self.json_list:
        
            cleaned_json_list = get_cleaned_json_list(self.json_list)
            self.save_log('新流程- 清洗后的json列表', cleaned_json_list)
            
            content = format_cleaned_json_list(cleaned_json_list)
            if self.is_deep_thinking:
                await queue.put(await self.format_sse_message(f"{content}\n", content_type="thinking"))
                self.thinking_content += f"{content}\n"

            self.save_log('新流程-清洗后限制数量的内部查询JSON列表', cleaned_json_list)
            
            t3 = time.time()
            self.warroom_data = await DataAnalyseAgent.data_query_new(cleaned_json_list)
            t4 = time.time()
            self.save_log('新流程-内部数据查询调用接口查询数据', self.warroom_data, t4 - t3)
        
        await queue.put(None)  # 结束标记


    async def get_external_information(self, queue: asyncio.Queue):
        if self.is_deep_thinking:
            self.thinking_content += '\n\n**联网搜索**\n'
            await queue.put(await self.format_sse_message('\n\n**联网搜索**\n', content_type="thinking"))
        web_contents = await get_web_contents_batch(self.decompose_questions, count=1)
        self.formatted_web_contents = format_web_contents_batch(web_contents)
        web_contents_return = format_output_web_contents_batch(web_contents[0:3])
        
        self.save_log('新流程-查询到的网页内容', web_contents)
        if self.is_deep_thinking:
            self.thinking_content += f"{web_contents_return}\n"
            await queue.put(await self.format_sse_message(f"{web_contents_return}\n", content_type="thinking"))
        await queue.put(None)  # 结束标记


    def create_task_functions(self):
        task_functions = []
        if self.is_internet_search:
            task_functions.append(self.get_external_information)
        if self.is_data_question == '是':
            task_functions.append(self.get_internal_information)
        return task_functions


    async def ordered_stream(self, task_functions):
        # 创建动态任务列表和队列
        tasks = []
        queues = []
        for task_func in task_functions:
            q = asyncio.Queue()
            tasks.append(asyncio.create_task(task_func(q)))
            queues.append(q)

        # 统一处理所有队列
        for queue in queues:
            while True:
                chunk = await queue.get()
                if chunk is None:  # 检测到任务结束
                    break
                yield chunk

        # 确保所有任务完成
        for task in tasks:
            await task


    async def get_analysis_result(self):
        if self.is_deep_thinking:
            self.thinking_content += '\n\n**整合分析**\n'
            yield await self.format_sse_message(content='\n\n**整合分析**\n', content_type="thinking")
        if self.warroom_data:
            filtered_warroom_data = filter_warroom_data_for_llm(self.warroom_data)
        else:
            filtered_warroom_data = None
        prompt = prompt_data_analysis_template.format(question=self.question, warroom_data=filtered_warroom_data, web_contents=self.formatted_web_contents, today=self.today)
        self.save_log('新流程-数据分析prompt',prompt)
        content = {"role": "user","content": prompt}
        self.history_message.append(content)
        if self.is_deep_thinking:
            response = await self.analyse_llm.async_chat_by_token(self.history_message)
            is_thinking = True
            async for chunk in response:
                # 火山云流式输出
                if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
                    content = chunk.choices[0].delta.reasoning_content
                    content = content.replace('\n\n', '\n')
                    self.thinking_content += content
                    yield await self.format_sse_message(content, content_type="thinking")
                else:
                    if is_thinking:
                        is_thinking = False
                    content = chunk.choices[0].delta.content
                    content = content.replace('\n\n', '\n')
                    self.final_answer += content
                    yield await self.format_sse_message(content, content_type="text")
        else:
            response = await self.process_llm_0324.async_chat_by_token(self.history_message)
            result = ''
            async for chunk in response:
                content = chunk.choices[0].delta.content
                yield await self.format_sse_message(content=content, content_type="text")
                result += content
                self.final_answer += content
        if self.is_data_question == '是':
            if self.warroom_data:
                for data in self.warroom_data:
                    if data['type'] == 'chart' or data['type'] == 'table':
                        if data not in self.charts:
                            yield await self.format_sse_message(data, content_type="chart", ended=False)
                            self.charts.append(data)
                self.save_log('新流程-沙盘图生成结果', self.charts)
        
        yield await self.format_sse_message('', content_type="text", ended=True)


    async def event_stream(self):
        try:
            await self.classify_question()
            async for item in self.decompose_question():
                yield f'data: {json.dumps(item,ensure_ascii=False)}\n\n'
            task_functions = self.create_task_functions()
            if self.is_data_question == '是'and not self.is_deep_thinking:
                item = await self.format_sse_message(content="相关数据检索中... ...\n", content_type="text")
                yield f'data: {json.dumps(item,ensure_ascii=False)}\n\n'
            async for item in self.ordered_stream(task_functions):
                yield f'data: {json.dumps(item,ensure_ascii=False)}\n\n'
            async for item in self.get_analysis_result():
                yield f'data: {json.dumps(item,ensure_ascii=False)}\n\n'
            await self.update_conversation_history()
        except asyncio.CancelledError:
            print("捕获到 CancelledError，开始清理")
            try:
                # 使用 shield 防止清理被取消
                await asyncio.shield(self.update_conversation_history())
            except Exception as e:
                e_msg = traceback.format_exc()
                self.save_log('异常捕获',e_msg)
                print('发生异常：',e_msg )
            print("客户端已断开连接")
            raise  # 重新抛出以便框架处理
        except Exception as e:
            e_msg = traceback.format_exc()
            self.save_log('异常捕获',e_msg)
            yield await self.format_sse_message('对不起，由于问题过于复杂，我暂时无法回答', content_type="text", ended=True)
            print('发生异常：',e_msg )
        finally:
            print("SSE返回已结束")


    async def format_sse_message(self, content, content_type, ended=False):
        """给前端返回的内容

        Args:
            content (string): 返回内容
            content_type (string): 返回类型
            ended (bool): 是否结束流式返回
        """
        return {"conversation_id": self.conversation_id, "message_id": self.message_id, "content": content, "type": content_type, "ended": ended}


    def save_log(self, log_type,log_content,time_cost=0):
        """记录日志

        Args:
            log_type (String): 日志类型
            log_content (Any): 日志记录内容
        """
        log_content = process_log(log_content)
        sx_log.debug(f"ip：{MACHINE_IP} - client：{self.client} - 用户userId：{self.user_id} - 问题：{process_log(self.question)} - 日志类型：{log_type} - 日志内容：{log_content} - 处理时间：{time_cost}")


if __name__ == '__main__':
    agent = ChatNewAgent()
    agent.question = '去年上汽大众的总销量是多少？'
    asyncio.run(agent.decompose_question())
    