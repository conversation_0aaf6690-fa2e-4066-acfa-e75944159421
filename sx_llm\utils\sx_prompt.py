from langchain.prompts.prompt import PromptTemplate



prompt_template_knowledge_search = """[要求]
1.以文本格式进行回答，结果不包含分析过程或解释或说明。
2.识别已知内容是否含有与用户输入相关的信息：
2.1.如果含有相关信息，则根据已知内容的信息，进行简洁专业的回答，不要添加其他内容；
2.2.如果不含有相关信息，则结合历史对话和经验，进行简洁专业的回答，不要添加其他内容。
3.mermaid图生成：
3.1.如果用户要求使用mermaid语法生成图，确保语法正确无误，将图代码放在<mermaid>和</mermaid>之间。
3.2.如果存在多个图，请拆分成不通的mermaid代码。

[已知内容]:{context}

[用户输入]：{question}
[回答]："""

prompt_knowledge_search = PromptTemplate(template=prompt_template_knowledge_search,input_variables=["context", "question"])







PREFIX = """你是SX人工智能助手，被设计用于回答用户的问题。
如果无法直接回答问题，你可以选择使用下方列举的工具，查询与问题相关的内容。
回答请简洁专业，不允许编造虚假内容，答案请使用中文。

工具:
------
"""

# PREFIX = """
# 助手旨在能够协助各种任务，从回答简单的问题到提供有关广泛主题的深入解释和讨论。 作为语言模型，助手能够根据收到的输入生成类似人类的文本，使其能够进行自然听起来的对话，并提供与手头主题相一致且相关的响应。

# 助手一直在学习和改进，其能力不断发展。 它能够处理和理解大量文本，并可以利用这些知识来对广泛的问题提供准确的信息回答。 此外，助手能够根据收到的输入来生成自己的文本，使其能够进行讨论，并就广泛主题提供解释和描述。

# 总体而言，助手是一个强大的工具，可以帮助完成各种任务，并提供有关广泛主题的宝贵见解和信息。 无论您是在特定问题方面需要帮助还是只想就特定主题进行对话，助手都可以在这里提供帮助。

# 工具：
# -------

# 助手可以访问以下工具：
# """

# FORMAT_INSTRUCTIONS = """
# 要使用工具，请使用以下格式：

# ```
# Thought: 我需要使用工具吗？ 是的
# Action: 要采取的动作应该是[{tool_names}]之一
# Action Input: 动作的输入
# Observation: 动作的结果
# ```

# 当您对人类说的回复，或者如果您不需要使用工具，则必须使用该格式：

# ```
# Thought: 我需要使用工具吗？ 不
# {ai_prefix}：[您的回答在这里]
# ```
# """

FORMAT_INSTRUCTIONS = """问题可以采用如下步骤进行推理：

```
问题: 你需要回答的问题
思考: 回答问题需要进行哪些行动? 是否需要使用工具? (是或者否)
行动: 需要执行的行动，使用[{tool_names}]中的一个工具
行动输入: 行动的输入内容
观察: 行动的输出结果
...(其中 思考/行动/行动输入/观察 可以重复很多次)
思考: 当行动的输出结果可以回答问题时，停止上述过程，并返回最终答案
最终答案: 问题的最终答案
```
"""

# SUFFIX = """
# 开始！

# 以前的对话历史记录：
# {chat_history}

# 新输入：{input}
# {agent_scratchpad}
# """


SUFFIX = """开始！给出你的最终答案。

之前的对话历史：
{chat_history}

问题: {input}
{agent_scratchpad}"""




kpi_all = ["上险（Insurance）", "市占（Market Share or M.S.）", "转化（Conversion）", "漏斗（Funnel）", "实时数据", "销量（Volume）", "销售表现",
           "日均客源数", "日均潜客数", "日均展厅客流数", "日均到店数", "日均试乘试驾数", "日均订单数", "日均零售发票数",
           "客源数（Leads）", "潜客数（NPC）", "展厅客流数（Showroom Traffic）", "到店数", "试乘试驾数（Test Drive）", "订单数（NCO）", 
           "交车数", "发票数（Invoice）", "零售发票数（Retail Invoice）",
           "批售数（Whole sale）", "含大客户发票数（Invoice KA Included）", "库存",
           "日均交车数", "日均销量", "日均发票数", "日均含大客户发票数", "日均批售数",
           "库存数（Stock）", "经销商库存数（Dealer Stock）", "总部库存数（HQ Stock）", "库存当量（Stock Factor）", "总部库存当量（HQ Stock Factor）", "经销商存数当量（Dealer Stock Factor）",
           "线索转化率（LTO）", "线索到店率（Leads to ST）", "试乘试驾率（ST to TD）", "到店转化率（ST to NCO）", "订单成交率（NCO to Invoice）", 
           "客源目标（Leads Target）","客源完成","客源任务完成（Leads Achieved）","客源进度",
           "潜客目标（NPC Target）","潜客完成","潜客任务完成（NPC Achieved）","潜客进度",
           "展厅客流目标（Showroom Traffic Target）","展厅客流完成","展厅客流任务完成（Showroom Traffic Achieved）","展厅客流进度",
           "到店目标","到店完成","到店任务完成","到店进度",
           "试乘试驾目标（Test Drive Target）","试乘试驾完成","试乘试驾任务完成（Test Drive Achieved）","试乘试驾进度",
           "订单目标（NCO Target）","订单完成","订单任务完成（NCO Achieved）","订单进度",
           "发票目标（Invoice Target）","发票完成","发票任务完成（Invoice Achieved）","发票进度",
           "交车目标","交车完成","交车任务完成","交车进度",
           "销量目标","销量完成","销量任务完成","销量进度",
           "含大客户发票目标（Invoice KA Included Target）","含大客户发票完成","含大客户发票任务完成（Invoice KA Included Achieved）","含大客户发票进度",
           "零售发票目标（Retail Invoice Target）","零售发票完成","零售发票任务完成（Retail Invoice Achieved）","零售发票进度",
           "批售目标（Whole sale Target）","批售完成","批售任务完成（Whole sale Achieved）","批售进度"]

kpi_all = "\"" + "\", \"".join(kpi_all) + "\""


# prompt_template_extract_args = """[要求]
# 1.以JSON格式返回，输出不包含其他内容或分析过程，不要返回```json```，只有JSON文本
# 2.JSON的内容按如下方式生成：
# 2.1.time：
# 2.1.1.提取用户输入中出现的明确时间，并以列表(list)形式保存。
# 2.1.2.相对时间以基准日期{today}进行转化，相对时间使用完整自然年、自然季度、自然月。
# 2.1.3.识别时间范围的开始和结束时间，严格使用yyyymmdd-yyyymmdd的格式。
# 2.1.4.如果输入的时间信息无法解析或不存在，则返回一个空列表 []。
# 2.2.date_type：提取用户输入查询时间的类型，按天返回"day"，按周返回"week"，按月返回"month"，按年返回"year"; 如果信息不存在, 请使用 "" 作为值。
# 2.3.location：提取用户输入中出现的大区、省份、城市，无需进行分析和推理，并补充"大区"、"省"、"市"关键字，多值字段，用中括号 list 结构保存，如["全国"]，["华东大区"]，["北京市","上海市"]；无相关信息，则使用 ["全国"] 作为值；"各大区" 默认返回 ["全国"]。
# 2.4.model：提取用户输入中的车辆品牌或车辆类别或车型名称, 多值字段, 用中括号 list 结构保存；如果信息不存在, 请使用 [] 作为值。
# 2.5.display：提取用户输入中隐含的数据展示形式，单值字段，仅包括"趋势","分布","排名","对比","同环比（yoy and mom）","同比（mom）","环比（yoy）"，无需添加其他不在范围的词汇; 如果信息不存在, 请使用 "" 作为值。
# 2.6.data_dim：
# 2.6.1."by SVR"表示"按大区维度下钻"的含义。
# 2.6.2.如果用户输入明确说明按地区维度下钻或出现"各大区"、"各省份"、"各城市"，返回"location"；如果用户输入明确说明按车型维度下钻或出现"各车型"，返回"model"。
# 2.6.3.如果用户输入没有明确说明按地区或车型维度下钻，即使含有地区或车型相关信息，也不返回"location"或"model"，只返回"all"。
# 2.7.today：如果用户输入仅涉及今日数据或实时数据，返回"True"，否则，返回"False"。
# 2.8.not_total：如果用户输入需要每月、每周、每日的数据，返回"True"，否则，返回"False"。
# 2.9.template：如果用户明确提到使用模板查询进行查询，提取模板的名称，单值字段，字符串; 如果信息不存在, 请使用 "" 作为值。

# [用户输入]：
# {question}
# [输出]："""


prompt_template_extract_args = """[要求]
1.以JSON格式返回，输出不包含其他内容或分析过程，不要返回```json```，只有JSON文本
2.[指标]：{kpi_all}
3.JSON的内容按如下方式生成：
3.1.time：
3.1.1.提取用户输入中出现的明确时间，忽略"每周"、"每月"、"每日"，并以列表(list)形式保存。
3.1.2.相对时间以基准日期{today}（{weekday}）进行转化，相对时间使用完整自然年、自然季度、自然月、自然周（周从星期一开始）。
3.1.3.识别时间范围的开始和结束时间，严格使用yyyymmdd-yyyymmdd的格式。
3.1.4.如果输入的时间信息无法解析或不存在，则返回一个空列表 []。
3.2.date_type：提取用户输入查询时间的类型，到天返回"day"，到周返回"week"，到月返回"month"，到年返回"year"; 如果信息不存在, 请使用 "" 作为值。
3.3.location：提取用户输入中出现的大区、省份、城市，无需进行分析和推理，并补充"大区"、"省"、"市"关键字，多值字段，用中括号 list 结构保存，如["全国"]，["华东大区"]，["北京市","上海市"]；无相关信息，则使用 ["全国"] 作为值；"各大区" 默认返回 ["全国"]。
3.4.model：提取用户输入中的车辆品牌或车辆类别或车型名称, 多值字段, 用中括号 list 结构保存；如果信息不存在, 请使用 [] 作为值
3.5.display：提取用户输入中隐含的数据展示形式，单值字段，仅包括"趋势"、"分布"、"排名"、"对比"、"同环比（yoy and mom）"、"同比（mom）"、"环比（yoy）"、"总计"，无需添加其他不在范围的词汇; 如果信息不存在, 请使用 "" 作为值。
3.6.data_dim：
3.6.1."by SVR"表示"按大区维度下钻"的含义。
3.6.3.如果用户输入明确说明查询更下一级地区维度或出现"各大区（by region）"、"各省份（by province）"、"各城市（by city）"、"哪个大区（which region）"、"哪个省份（which province）"、"哪个城市（which city）"，返回"location"；如果用户输入明确说明查询更下一级车型维度或出现"各车型（by model）"、"哪个车型（which model）"的含义，返回"model"。
3.6.3.如果用户输入没有明确说明按地区或车型维度下钻，即使含有地区或车型相关信息，也不返回"location"或"model"，只返回"all"。
3.7.today：如果用户输入明确提到今日数据或实时数据查询，返回"True"，否则，返回"False"。
3.8.not_total：如果用户输入需要每月、每周、每日的数据，返回"True"，否则，返回"False"。
3.9.template：如果用户明确提到使用模板查询进行查询，提取模板的名称，单值字段，字符串; 如果信息不存在, 请使用 "" 作为值。

[样例]
1.[用户输入]：今年北方各省份上汽大众分月销量
[输出]："{{"time": ["{this_year_first_day}-{this_year_last_day}"], "date_type": "month", "model": ["上汽大众"], "location": ["北方大区"], "display": "趋势", "today": "False", "data_dim": "location", "not_total": "True", "template": ""}}"
2.[用户输入]：This year's completion of invoice of the last three models
[输出]："{{"time": ["{this_year_first_day}-{this_year_last_day}"], "date_type": "year", "model": [], "location": ["全国"], "display": "排名", "today": "False", "data_dim": "model", "not_total": "False", "template": ""}}"
3.[用户输入]：How were the sales of ID3 by model this year in hebei?
[输出]："{{"time": ["{this_year_first_day}-{this_year_last_day}"], "date_type": "year", "model": ["ID3"], "location": ["河北省"], "display": "分布", "today": "False", "data_dim": "model", "not_total": "False", "template": ""}}"
4.[用户输入]：今年累计日均客源数
[输出]："{{"time": ["{this_year_first_day}-{this_year_last_day}"], "date_type": "year", "model": [], "location": ["全国"], "display": "总计", "today": "False", "data_dim": "all", "not_total": "False", "template": ""}}"
5.[用户输入]：Comparison of the volume of invoice between FAW-VW and SVW-VW
[输出]："{{"time": [], "date_type": "", "model": ["FAW-VW","SVW-VW"], "location": ["全国"], "display": "对比", "today": "False", "data_dim": "all", "not_total": "False", "template": ""}}"

[用户输入]：
{question}
[输出]："""




prompt_extract_args = PromptTemplate(template=prompt_template_extract_args,input_variables=["question","today","kpi_all","weekday","this_year_first_day","this_year_last_day"])






# todo：修改数据查询
prompt_template_language_question_intention_kpi = """[要求]
1.以文本格式返回下方内容，结果不包含分析过程或解释或说明。
2.[指标]：{kpi_all}
3.文本的内容按如下方式生成：
3.1.[语言]：识别用户输入的语言，包括"英文"和"中文"。
3.2.[意图]：识别用户输入的意图，包括：
3.2.1.数据查询：如果用户输入涉及指标结果查询，请返回"数据查询"。
3.2.2.数据分析：如果涉及数据结果原因分析或对数据的进一步提问，包括哪一个(which)，哪些(which ones)，谁(who)，什么时间(when)，请返回"数据分析"。
3.2.3.页面交互：如果用户输入明确需要进行"时间筛选"、"页面返回"、"页面关闭"、"页面退出"，请返回"页面交互"。
3.2.4.如果数据查询不包含指标，或不是上述三种意图的其中一种，请返回"未知意图"。
3.3.[查询指标]：如果意图为"数据查询"或"数据分析"，识别查询的指标，并返回指标完整名称，多值字段, 用中括号 list 结构保存，例如["订单数（NCO）"],["销量（Volume）","市占（Market Share or M.S.）"]; 如果信息不存在, 请使用 [] 作为值。

[用户输入]：{current_question}

[语言]：
[意图]：
[查询指标]："""

prompt_language_question_intention_kpi = PromptTemplate(template=prompt_template_language_question_intention_kpi,input_variables=["current_question","kpi_all"])


# prompt_template_data_analysis = """[要求]
# 1.以文本格式进行回答，不用重复需求。
# 2.相对时间以基准日期{today}进行转化，如果问题中没有时间，默认为本月。
# 3.[数据]中不同参数的含义：
# 3.1.车型组：HIGH代表高端车型组；MAIN代表主流车型组；ID代表ID车型组；SK代表斯柯达车型组。
# 3.2.指标：volume代表上险量；marketShare代表市占；lastVolume代表上期上险量；yoymVolume代表同期上险量；volumeMom代表上险量环比；volumeYoy代表上险量同比；monthOnMonthChange代表市占环比；yearOnYearChange代表市占同比；leadsNotDuplicateCnt代表客源数；deliveryEinvoiceNotSvkCnt代表零售发票数；orderCnt代表订单数；oppTestdriveCnt代表试乘试驾数；oppWalkinCnt代表展厅客流数；oppCnt代表潜客数；insurance代表上险数据；funnel代表转化漏斗；targetLeads代表客源目标；targetOpportunity代表潜客目标；targetOppWalkin代表展厅客流目标；targetOppTestdrive代表试乘试驾目标；targetOrder代表订单目标；targetInvoice代表发票目标；leadsTransferRate代表线索转化率；leadsArrivalRate代表线索到店率；testdriveRate代表试乘试驾率；oppWalkinTransferRate代表到店转化率；orderDeliveryNotSvkRate代表订单成交率；conversion代表转化数据；completion代表完成数；target代表目标值；time_schedule代表时间进度；leadsNotDuplicateCntDayAvgMom代表日均客源数环比；leadsNotDuplicateCntDayAvgYoy代表日均客源数同比；oppCntDayAvgMom代表日均潜客数环比；oppCntDayAvgYoy代表日均潜客数同比；oppWalkinCntDayAvgMom代表日均展厅客流数环比；oppWalkinCntDayAvgYoy代表日均展厅客流数同比；oppTestdriveCntDayAvgMom代表日均试乘试驾数环比；oppTestdriveCntDayAvgYoy代表日均试乘试驾数同比；orderCntDayAvgMom代表日均订单数环比；orderCntDayAvgYoy代表日均订单数同比；deliveryEinvoiceNotSvkCntDayAvgMom代表日均零售发票数环比；deliveryEinvoiceNotSvkCntDayAvgYoy代表日均零售发票数同比；leadsTransferRateMom代表线索转化率环比；leadsTransferRateYoy代表线索转化率同比；leadsArrivalRateMom代表线索到店率环比；leadsArrivalRateYoy代表线索到店率同比；testdriveRateMom代表试乘试驾率环比；testdriveRateYoy代表试乘试驾率同比；oppWalkinTransferRateMom代表到店转化率环比；oppWalkinTransferRateYoy代表到店转化率同比；orderDeliveryNotSvkRateMom代表订单成交率环比；orderDeliveryNotSvkRateYoy代表订单成交率同比；wholesaleCnt代表批售数；eInvoiceCnt代表发票数；wholesaleTargetCnt代表批售目标；eInvoiceTargetCnt代表发票目标；stockTotalDealerCnt代表经销商库存数；stockTotalHqCnt代表总部库存数；stockTotalDealerIndex代表经销商库存当量；stockTotalHqIndex代表总部库存当量；wholesaleCntMom代表批售数环比；wholesaleCntYoy代表批售数同比；wholesaleCntDayAvgMom代表日均批售数环比；wholesaleCntDayAvgYoy代表日均批售数同比；eInvoiceCntMom代表发票数环比；eInvoiceCntYoy代表发票数同比；eInvoiceCntDayAvgMom代表日均发票数环比；eInvoiceCntDayAvgYoy代表日均发票数同比；todayLeadsNotDuplicateCnt代表今日客源数；todayOppCnt代表今日潜客数；todayOppWalkinCnt代表今日展厅客流数；todayOppTestdriveCnt代表试乘试驾；todayOrderCnt代表订单；todayDeliveryEinvoiceCnt代表发票；todayDeliveryEinvoiceNotSvkCnt代表零售发票；todayEInvoiceCnt代表今日发票数；stockTotalCnt代表经销商库存数；stockTotalIndex代表经销商库存当量；stockTotalIndexMomChange代表经销商库存当量环比；stockTotalIndexYoyChange代表经销商库存当量同比。
# 4.结合[数据]，如果[问题]可以回答，则给出简洁准确的回答；如果无法回答或[数据]为空，可以根据历史对话信息或经验给出简洁准确的回答。
# 5.[数据]：{data}

# [问题]：{question}

# [回答]："""


prompt_template_data_analysis = """# 角色
你是一个数据分析助手，需要根据提供的数据以及网页内容进行分析，并回答用户问题。

# 要求
1.图表生成：
- 问题回答数据图生成：根据[企业内数据-问题回答数据]数据，根据数据中的图表类型，仅选择Mermaid支持的图表类型，严格使用mermaid语法生成，不允许自己定义或创造，将图代码放在<mermaid>和</mermaid>之间，如果存在多个图，请拆分成不同的mermaid代码。
- 扩展查询数据图生成：根据[企业内数据-拓展查询数据]数据，根据数据中的图表类型，仅选择Mermaid支持的图表类型，严格使用mermaid语法生成，不允许自己定义或创造，将图代码放在<mermaid>和</mermaid>之间，如果存在多个图，请拆分成不同的mermaid代码。
2.问题分析：
2.1.请思考用户[问题]需要从哪些方面进行分析，然后结合[企业内数据]和[网页内容]进行回答。
2.2.如果[企业内数据]和[网页内容]中存在冲突，请以[企业内数据]为准。
2.3.以文本格式进行回答，不用重复需求。
2.4.相对时间以基准日期{today}进行转化，如果问题中没有时间，默认为本月。
2.5.参考内容引用：
2.5.1.每个事实陈述后标注对应的文献编号（示例：[3][13][24]）。
2.5.2.编号按引用顺序排列，多个编号用逗号分隔。
2.5.3.只使用提供的文献编号，禁止虚构编号。
2.5.4.回答最后列出全部使用到的参考链接。
2.6.[数据]中不同参数的含义：
2.6.1.车型组：HIGH代表高端车型组；MAIN代表主流车型组；ID代表ID车型组；SK代表斯柯达车型组。
2.6.2.指标：volume代表上险量；marketShare代表市占；lastVolume代表上期上险量；yoymVolume代表同期上险量；volumeMom代表上险量环比；volumeYoy代表上险量同比；monthOnMonthChange代表市占环比；yearOnYearChange代表市占同比；leadsNotDuplicateCnt代表客源数；deliveryEinvoiceNotSvkCnt代表零售发票数；orderCnt代表订单数；oppTestdriveCnt代表试乘试驾数；oppWalkinCnt代表展厅客流数；oppCnt代表潜客数；insurance代表上险数据；funnel代表转化漏斗；targetLeads代表客源目标；targetOpportunity代表潜客目标；targetOppWalkin代表展厅客流目标；targetOppTestdrive代表试乘试驾目标；targetOrder代表订单目标；targetInvoice代表发票目标；leadsTransferRate代表线索转化率；leadsArrivalRate代表线索到店率；testdriveRate代表试乘试驾率；oppWalkinTransferRate代表到店转化率；orderDeliveryNotSvkRate代表订单成交率；conversion代表转化数据；completion代表完成数；target代表目标值；time_schedule代表时间进度；leadsNotDuplicateCntDayAvgMom代表日均客源数环比；leadsNotDuplicateCntDayAvgYoy代表日均客源数同比；oppCntDayAvgMom代表日均潜客数环比；oppCntDayAvgYoy代表日均潜客数同比；oppWalkinCntDayAvgMom代表日均展厅客流数环比；oppWalkinCntDayAvgYoy代表日均展厅客流数同比；oppTestdriveCntDayAvgMom代表日均试乘试驾数环比；oppTestdriveCntDayAvgYoy代表日均试乘试驾数同比；orderCntDayAvgMom代表日均订单数环比；orderCntDayAvgYoy代表日均订单数同比；deliveryEinvoiceNotSvkCntDayAvgMom代表日均零售发票数环比；deliveryEinvoiceNotSvkCntDayAvgYoy代表日均零售发票数同比；leadsTransferRateMom代表线索转化率环比；leadsTransferRateYoy代表线索转化率同比；leadsArrivalRateMom代表线索到店率环比；leadsArrivalRateYoy代表线索到店率同比；testdriveRateMom代表试乘试驾率环比；testdriveRateYoy代表试乘试驾率同比；oppWalkinTransferRateMom代表到店转化率环比；oppWalkinTransferRateYoy代表到店转化率同比；orderDeliveryNotSvkRateMom代表订单成交率环比；orderDeliveryNotSvkRateYoy代表订单成交率同比；wholesaleCnt代表批售数；eInvoiceCnt代表发票数；wholesaleTargetCnt代表批售目标；eInvoiceTargetCnt代表发票目标；stockTotalDealerCnt代表经销商库存数；stockTotalHqCnt代表总部库存数；stockTotalDealerIndex代表经销商库存当量；stockTotalHqIndex代表总部库存当量；wholesaleCntMom代表批售数环比；wholesaleCntYoy代表批售数同比；wholesaleCntDayAvgMom代表日均批售数环比；wholesaleCntDayAvgYoy代表日均批售数同比；eInvoiceCntMom代表发票数环比；eInvoiceCntYoy代表发票数同比；eInvoiceCntDayAvgMom代表日均发票数环比；eInvoiceCntDayAvgYoy代表日均发票数同比；todayLeadsNotDuplicateCnt代表今日客源数；todayOppCnt代表今日潜客数；todayOppWalkinCnt代表今日展厅客流数；todayOppTestdriveCnt代表试乘试驾；todayOrderCnt代表订单；todayDeliveryEinvoiceCnt代表发票；todayDeliveryEinvoiceNotSvkCnt代表零售发票；todayEInvoiceCnt代表今日发票数；stockTotalCnt代表经销商库存数；stockTotalIndex代表经销商库存当量；stockTotalIndexMomChange代表经销商库存当量环比；stockTotalIndexYoyChange代表经销商库存当量同比。

# 企业内数据
- 问题回答数据
{data}
- 拓展查询数据
{warroom_data}

# 网页内容
{web_contents}

[问题]：{question}

[回答]："""

prompt_data_analysis = PromptTemplate(template=prompt_template_data_analysis,input_variables=["data","warroom_data","web_contents","today","question"])


# prompt_template_related_question = """
# 你是一个人工智能助手，请根据已知问题，生成3个可能的相关问题。
# 相关问题需要包含"销量", "同比", "环比", "市占", "客源", "潜客", "到店", "试乘试驾", "订单", "发票", "线索到店转化率", "试乘试驾转化率", "到店转化率", "LTO转化率", "订单成交转化率"等内容。

# 已知问题：介绍下model y这个车
# 相关问题1：
# 相关问题2：
# 相关问题3：
# """

# prompt_related_question = PromptTemplate(template=prompt_template_related_question,input_variables=["history_question","current_question"])









prompt_template_ui = """你是一个人工智能助手，请按顺序执行下面的任务。
第一步，识别问题中的时间范围，并以今天（{today}）为基准，将时间转换成月度范围yyyymm-yyyymm的格式，季度请返回对应的月份。
其中，一季度：1月，2月，3月；2季度：4月，5月，6月；3季度：7月，8月，9月；4季度：10月，11月，12月。
第二步，从问题中识别以下信息，不要自动假设信息：
- start_time: 提取第一步中时间范围的开始时间，日期格式为yyyymm; 如果信息不存在, 请使用 "" 作为值。
- end_time: 提取第一步中时间范围的结束时间，日期格式为yyyymm; 如果信息不存在, 请使用 "" 作为值。 
- action: 提取问题中的操作，单值字段，包括"筛选","返回"; 如果信息不存在, 请使用 "" 作为值。
第三步，输出 "start_time","end_time","action" 为键的 JSON 对象。

下面是正式的问题:
问题: {question}
回答:
"""

prompt_ui = PromptTemplate(template=prompt_template_ui,input_variables=["question","today"])






# todo：问题改写测试，多语言交叉情况
prompt_template_question = """请根据用户历史提问和用户当前问题的完整程度，对用户当前问题进行改写。
历史提问：
{history}


用户当前问题：{question}
改写："""

prompt_question = PromptTemplate(template=prompt_template_question,input_variables=["question","history"])





prompt_template_validate = """
你是一个答案检查助手，请根据下方的问题和答案，判断回答是否准确，如准确，请直接输出答案，不准确，请返回问题的准确回答。

原问题：```{question}```

原答案：```{answer}```

你的回答：
"""

prompt_validate = PromptTemplate(template=prompt_template_validate,input_variables=["question","answer"])












prompt_dict = {"prompt_knowledge_search":prompt_knowledge_search,}
