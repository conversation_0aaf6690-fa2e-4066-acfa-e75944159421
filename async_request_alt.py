import aiohttp
import asyncio
import json

async def test():
    async def request_data(api, data):
        headers = {
            'Authorization': 'SYAAkjENRNIAFVoQKylHQ51nZRNeX12-',
            'User-Agent': 'python-requests/2.32.3',
            'Accept': 'application/json, text/plain, */*',  # 接受多种格式
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        }
        
        # 构建URL - 确保与同步请求格式一致
        url = api + '?' + '&'.join([f"{key}={value}" for key, value in data.items()])
        print(f"[异步请求] URL: {url}")
        print(f"[异步请求] Headers: {headers}")
        
        # 配置选项更接近同步请求
        session_options = {
            'trust_env': True,
            'connector': aiohttp.TCPConnector(
                ssl=False,
                force_close=True,  # 每次请求后关闭连接
                limit=1  # 限制连接数
            ),
            'version': aiohttp.HttpVersion11,  # 强制使用HTTP/1.1
            'cookie_jar': aiohttp.CookieJar(unsafe=True)  # 启用cookie处理
        }
        
        try:
            async with aiohttp.ClientSession(**session_options) as session:
                # 设置超时并禁用压缩
                timeout = aiohttp.ClientTimeout(total=30)
                
                async with session.get(url, headers=headers, timeout=timeout, 
                                      allow_redirects=True) as response:
                    print(f"[异步响应] Status: {response.status}")
                    print(f"[异步响应] Headers: {response.headers}")
                    
                    # 读取原始响应内容
                    content = await response.text(encoding='utf-8', errors='replace')
                    print(f"[异步响应] Content length: {len(content)}")
                    
                    if response.status == 200:
                        # 检查内容类型，如果是JSON格式，直接解析
                        content_type = response.headers.get('Content-Type', '')
                        print(f"[异步响应] Content-Type: {content_type}")
                        
                        if content.strip():
                            print(f"[异步响应] 内容前100个字符: {content[:100]}")
                            
                            # 尝试手动解析JSON
                            try:
                                # 修复常见的JSON问题
                                clean_content = content.strip()
                                json_data = json.loads(clean_content)
                                return json_data
                            except json.JSONDecodeError as e:
                                print(f"JSON解析错误: {e}")
                                
                                # 检查是否返回了HTML而不是预期的JSON
                                if '<html' in content.lower() or '<body' in content.lower():
                                    print("服务器返回了HTML而不是JSON")
                                    # 进一步分析HTML看是否包含登录页面
                                    if 'login' in content.lower():
                                        print("可能需要重新登录或会话已过期")
                                else:
                                    print("返回的内容不是有效的JSON或HTML")
                                
                                return None
                        else:
                            print("服务器返回了空内容")
                            return None
                    else:
                        print(f"服务器返回错误状态码: {response.status}")
                        return None
        except aiohttp.ClientError as e:
            print(f"客户端错误: {e}")
            return None
        except asyncio.TimeoutError:
            print("请求超时")
            return None
        except Exception as e:
            print(f"未知错误: {e}")
            return None

    # 使用与同步请求完全相同的参数
    api = 'http://10.122.31.36:8080/report/mobile/wechat/sv/afterSaleJyController/queryJyIndex'
    data = {"yearMonth": "202301", "queryType": "M"}
    result = await request_data(api, data)
    print(f"最终响应: {result}")

if __name__ == "__main__":
    asyncio.run(test()) 