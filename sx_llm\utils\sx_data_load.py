import os
import pandas as pd

# 使用相对路径的方式进行文件读取
parent_dir = '..'



# 加载大区、省份、城市的维表
city_file_path = os.path.join(parent_dir, 'data', 'location_city_name.txt')
province_file_path = os.path.join(parent_dir, 'data', 'location_province_name.txt')
rssc_file_path = os.path.join(parent_dir, 'data', 'location_rssc_name.txt')
data_city = pd.read_csv(city_file_path,header=None)[0].tolist()
data_province = pd.read_csv(province_file_path,header=None)[0].tolist()
data_rssc = pd.read_csv(rssc_file_path,header=None,sep='\t')[0].tolist()



def generate_mappings(file_path, direction):
    """将文件进行中英文映射"""
    if direction not in ('cn_to_en', 'en_to_cn'):
        raise ValueError("Invalid direction. Use 'cn_to_en' or 'en_to_cn'.")
    index = (1, 0) if direction == 'cn_to_en' else (0, 1)
    with open(file_path, 'r', encoding='utf-8') as file:
        mappings = {line.strip().split('\t')[index[0]]: line.strip().split('\t')[index[1]] for line in file}
    
    return mappings





location_file_path = os.path.join(parent_dir, 'data', 'location_name.tsv')
model_name_file_path = os.path.join(parent_dir, 'data', 'model_name_cn.tsv')
template_file_path  = os.path.join(parent_dir, 'data', 'template_name_id.tsv')


location_cn_to_en = generate_mappings(location_file_path, 'cn_to_en')
model_en_to_cn = generate_mappings(model_name_file_path, 'en_to_cn')
template_name_to_id = generate_mappings(template_file_path, 'en_to_cn')
template_id_to_name = generate_mappings(template_file_path, 'en_to_en')




# 加载向量数据
model_vector_file_path    = os.path.join(parent_dir, 'vector_store', 'model_index')
template_vector_file_path = os.path.join(parent_dir, 'vector_store', 'template_index')








if __name__ == "__main__":
    print(model_en_to_cn)
    print(location_cn_to_en)