import time
import json
import asyncio
import traceback

from utils.sx_dict import *
from utils.sx_prompt import kpi_all
from utils.clean_json import extract_json_new
from functions.warroom_data_process import get_cleaned_json_list, format_cleaned_json_list, remove_empty_from_list, filter_warroom_data_for_llm
from functions.web_search import get_web_contents_batch, format_web_contents_batch, format_output_web_contents_batch
from agents.sales.prompt import *
from agents.data_analyse.data_analyse_agent import DataAnalyseAgent
from agents.base_agent import BaseAgent


class SalesAgent(BaseAgent):
    def __init__(self):
        super().__init__()  # 调用父类初始化
        self.kpis = []
        self.categorys_kpis = {}
        self.category_kpi_api = []
        self.data_all = []
    

    def get_profiles_from_manager(self, agent: BaseAgent):
        # 从管理类获取数据
        self.client = agent.client
        self.access_token = agent.access_token
        self.input_json = agent.input_json
        self.user_id = agent.user_id
        self.conversation_id = agent.conversation_id
        self.question = agent.question
        self.is_internet_search = agent.is_internet_search
        self.is_deep_thinking = agent.is_deep_thinking
        self.language = agent.language
        self.input_time = agent.input_time
        self.history_message = agent.history_message
        self.history_question = agent.history_question
        self.conversation_all = agent.conversation_all
        self.message_id = agent.message_id
    
    
    async def decompose_question(self):
        if not self.is_deep_thinking:
            self.decompose_questions = [self.question]
            return
        # 分解用户输入的问题
        history_question = '\n'.join([str(index)+'.'+q for index, q in enumerate(self.history_question, start=1)])
        prompt = prompt_query_decomposition_add_history_template.format(current_question = self.question, history_question = history_question)
        content = [{"role": "user","content": prompt}]
        response = await self.process_llm.async_chat_by_token(content)
        
        is_thinking = True
        
        result = ''
        all_result = ''
        
        t1 = time.time()
        is_delete = False
        
        async for chunk in response:

            content = chunk.choices[0].delta.content
            content = content.replace('\n\n','\n')
            all_result += content
            
            
            if '<' in content:
                content_prefix = content.split('<')[0]
                is_delete = True
                self.thinking_content += content_prefix
                content_suffix = '<' + content.split('<')[-1]
                yield await self.format_sse_message(content=content_prefix, content_type="thinking")
                yield await self.format_sse_message(content='\n<delete>\n', content_type="thinking")
                yield await self.format_sse_message(content=content_suffix, content_type="thinking")
            elif '>' in content:
                is_thinking = False
                content_suffix = content.split(">")[-1]
                self.thinking_content += content_suffix
                result += content_suffix
                yield await self.format_sse_message(content=content, content_type="thinking")
            else:
                if not is_delete:
                    self.thinking_content += content
                yield await self.format_sse_message(content=content, content_type="thinking")
                if not is_thinking:
                    result += content
        
        t2 = time.time()
        self.save_log('销售agent- 分解问题回答',result,time_cost=t2-t1)
          
        yield await self.format_sse_message(content='\n</delete>\n', content_type="thinking")
        self.thinking_content += '\n\n**问题分解**\n'
        yield await self.format_sse_message(content='\n\n**问题分解**\n', content_type="thinking")
        
        self.decompose_questions = extract_json_new(result)['查询问题']
        # 增加原始问题到分解问题列表中
        self.decompose_questions.append(self.question)
        questions_return = '\n'.join(self.decompose_questions)
        yield await self.format_sse_message(content=f'{questions_return}\n', content_type="thinking")
        self.thinking_content += f'{questions_return}\n'
        
        self.save_log('销售agent- 添加了原始问题后的问题列表',self.decompose_questions,time_cost=t2-t1)
        
        self.save_log('销售agent- 分解问题- 完整回答',all_result,time_cost=t2-t1)


    async def get_question_json_list(self):
        prompt = prompt_internal_query_json_template.format(question_list=json.dumps(self.decompose_questions,ensure_ascii=False), today=self.today, weekday=self.weekday, kpi_all=kpi_all)
        self.save_log('销售agent- 内部查询问题转化为json的prompt',prompt)
        content = [{"role": "user","content": prompt}]
        response = await self.process_llm.async_chat_by_token(content)
        yield '<delete>'
        result = ''
        async for chunk in response:
            content = chunk.choices[0].delta.content
            yield content
            result += content
        yield '</delete>'
        self.save_log('销售agent- 内部查询问题转化为json的结果',result)
        if 'json_list' in result:
            self.json_list = json.loads(result)['json_list']
            self.json_list = remove_empty_from_list(self.json_list)


    async def get_internal_information(self, queue: asyncio.Queue):
        if self.is_deep_thinking:
            await queue.put(await self.format_sse_message('\n\n**内部数据检索**\n', content_type="thinking"))
            self.thinking_content += '\n\n**内部数据检索**\n'
            await queue.put(await self.format_sse_message('销售数据查询：\n', content_type="thinking"))
            self.thinking_content += '销售数据查询：\n'
        async for chunk in self.get_question_json_list():
            if self.is_deep_thinking:
                await queue.put(await self.format_sse_message(chunk, content_type="thinking"))
        
        self.save_log('销售agent- 从问题列表提取到的JSON列表', self.json_list)
        
        if self.json_list:
        
            cleaned_json_list = get_cleaned_json_list(self.json_list)
            self.save_log('销售agent- 清洗后的json列表', cleaned_json_list)
            
            content = format_cleaned_json_list(cleaned_json_list)
            if self.is_deep_thinking:
                await queue.put(await self.format_sse_message(f"{content}\n", content_type="thinking"))
                self.thinking_content += f"{content}\n"

            self.save_log('销售agent-清洗后限制数量的内部查询JSON列表', cleaned_json_list)
            
            t3 = time.time()
            self.warroom_data = await DataAnalyseAgent.data_query_new(cleaned_json_list)
            self.data_all.extend(self.warroom_data)
            t4 = time.time()
            self.save_log('销售agent-内部数据查询调用接口查询数据', self.warroom_data, t4 - t3)
        
        await queue.put(None)  # 结束标记


    async def get_external_information(self, queue: asyncio.Queue):
        if self.is_deep_thinking:
            self.thinking_content += '\n\n**联网搜索**\n'
            await queue.put(await self.format_sse_message('\n\n**联网搜索**\n', content_type="thinking"))
        web_contents = await get_web_contents_batch(self.decompose_questions, count=1)
        self.formatted_web_contents = format_web_contents_batch(web_contents)
        web_contents_return = format_output_web_contents_batch(web_contents[0:3])
        
        self.save_log('销售agent-查询到的网页内容', web_contents)
        if self.is_deep_thinking:
            self.thinking_content += f"{web_contents_return}\n"
            await queue.put(await self.format_sse_message(f"{web_contents_return}\n", content_type="thinking"))
        await queue.put(None)  # 结束标记



    def create_task_functions(self):
        task_functions = []
        if self.is_internet_search:
            task_functions.append(self.get_external_information)
        task_functions.append(self.get_internal_information)
        return task_functions


    def generate_chart_data(self):
        if self.warroom_data:
            for data in self.warroom_data:
                if data['type'] == 'chart' or data['type'] == 'table':
                    if data not in self.charts:
                        self.charts.append(data)


    async def event_stream_queue(self, queue: asyncio.Queue):
        try:
            async for item in self.decompose_question():
                await queue.put(item)
            task_functions = self.create_task_functions()
            if not self.is_deep_thinking:
                item = await self.format_sse_message(content=f"正在检索\"{self.question}\"相关数据... ...\n", content_type="text")
                await queue.put(item)
            async for item in self.ordered_stream(task_functions):
                await queue.put(item)
            self.generate_chart_data()
            await queue.put(None)
        except asyncio.CancelledError:
            print("捕获到 CancelledError，开始清理")
        except Exception as e:
            e_msg = traceback.format_exc()
            self.save_log('销售agent- 异常捕获',e_msg)
            await queue.put(await self.format_sse_message('对不起，由于问题过于复杂，我暂时无法回答', content_type="text", ended=True))
            print('发生异常：',e_msg )
        finally:
            print("SSE返回已结束")



if __name__ == '__main__':
    agent = SalesAgent()
    agent.question = '去年上汽大众的总销量是多少？'
    asyncio.run(agent.decompose_question())
    