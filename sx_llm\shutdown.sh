#!/bin/bash
# ----------------------------------------------------------------------
# name:         shutdown.sh
# version:      1.0
# createTime:   2024-05-13
# description:  沙盘模型服务后端关闭脚本
# author:       liang<PERSON><PERSON>
# email:        liangkai<PERSON>@csvw.com
# -------------------------------------------------------------------
# set -e

pid_file="./application.pid"
PID=$(cat $pid_file)
echo "app pid is:$PID"
if [ -z "$PID" ];then
        echo "no pid file,please kill it manually"
        exit 1
fi

# kill 10 times, then force kill
TIMEOUT=10
count=1;
while [ $TIMEOUT -lt 0 -o $count -le $TIMEOUT ]
do
        kill -0 $PID 2>/dev/null
        if [ $? -eq 0 ]; then
                echo kill $PID time [$count]
                kill $PID
                sleep 1
                ((count++))
        else
                break
        fi
done
echo "test if the process exists."
kill -0 $PID 2>/dev/null

if [ $? -eq 0 ];then
        echo kill -9 $PID
        kill -9 $PID
        sleep $TIMEOUT
        echo "force kill $PID"
else
        echo "kill $PID gracefully"
fi
