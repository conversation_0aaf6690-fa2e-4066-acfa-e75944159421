import websockets
import asyncio

async def async_input(prompt):
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, input, prompt)

async def receive_messages(websocket):
    try:
        async for message in websocket:
            print(f"收到响应: {message}")
    except websockets.exceptions.ConnectionClosed:
        print("服务器连接已关闭")

async def send_messages(websocket):
    while True:
        try:
            message = await async_input("请输入消息 (输入 'exit' 退出): ")
            if message.strip().lower() == 'exit':
                print("正在关闭连接...")
                await websocket.close()
                break
            await websocket.send(message)
            print("已发送消息")
        except websockets.exceptions.ConnectionClosed:
            print("连接已关闭，无法发送消息")
            break

async def websocket_client():
    url = "ws://127.0.0.1:8000/api/V2/chat/ws"
    
    # 设置心跳间隔和超时以保持连接活跃
    async with websockets.connect(
        url,
        ping_interval=15,
        ping_timeout=5,
        close_timeout=1
    ) as websocket:
        print("已连接到服务器")
        receiver_task = asyncio.create_task(receive_messages(websocket))
        sender_task = asyncio.create_task(send_messages(websocket))
        
        # 等待任意一个任务完成
        done, pending = await asyncio.wait(
            [receiver_task, sender_task],
            return_when=asyncio.FIRST_COMPLETED
        )
        
        # 取消未完成的任务
        for task in pending:
            task.cancel()
        # 等待任务处理取消
        await asyncio.gather(*pending, return_exceptions=True)

if __name__ == "__main__":
    asyncio.run(websocket_client())