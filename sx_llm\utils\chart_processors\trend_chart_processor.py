from utils.sx_date import answer_date
from utils.sx_dict import sx_kpi_name, sx_kpi_value_percentage_dict, sx_kpi_table_dict, sx_mixed_chart_name
from utils.chart_processors.base_chart_processor import BaseChartProcessor


no_percentage_kpis = ['wholesaleCnt','eInvoiceCnt','wholesaleCntDayAvg','eInvoiceCntDayAvg']
no_mom_kpis = ['stockTotalCnt','总部库存']


def get_kpis(kpi):
    kpi_value = sx_kpi_value_percentage_dict[kpi]["value"]
    
    if kpi in no_percentage_kpis:
        kpi_mom = sx_kpi_table_dict[kpi_value]['mom']
        kpi_yoy = sx_kpi_table_dict[kpi_value]['yoy']
        kpis = [kpi_value, kpi_mom, kpi_yoy]

    elif kpi in no_mom_kpis:
        kpi_percentage = sx_kpi_value_percentage_dict[kpi]["percentage"]
        kpis = [kpi_value, kpi_percentage]


    else:
        kpi_percentage = sx_kpi_value_percentage_dict[kpi]["percentage"]
        kpi_mom = sx_kpi_table_dict[kpi_value]['mom']
        kpi_yoy = sx_kpi_table_dict[kpi_value]['yoy']
        kpis = [kpi_value, kpi_percentage, kpi_mom, kpi_yoy]
    return kpis


class TrendChartProcessor(BaseChartProcessor):
    """Funnel chart processor"""

    def __init__(self, format_json) -> None:
        super().__init__(format_json)

    def chart_process(self, data, language):
        # 由于查询结果会对model和location进行更明细的查询，所以需要先提取可能出现的枚举值
        data_locations = list({i["location"] for i in data})
        data_models = list({i["model"] for i in data})
        chart_list = []
        data = [i for i in data if i["time"] != "total"]
        if self.json_date_type == 'week':
            data = [{**i, 'time': answer_date(self.json_date_type,i['time'],'英文')} for i in data]

        # 按指标、车型、地区拆分成多个图
        for kpi_temp in self.json_kpi:
            kpi_value = sx_kpi_value_percentage_dict[kpi_temp]["value"]

            kpis = get_kpis(kpi_temp)
            name_kpi = sx_kpi_name[kpi_value][language]

            for j in data_models:
                for k in data_locations:
                    # 按时间升序
                    kpi_data = [i for i in data if i["model"] == j and i["location"] == k and kpi_value in i["kpiValues"].keys()]
                    kpi_data = sorted(kpi_data,key=lambda i: i['time'])

                    series = [
                        {
                            "name": sx_kpi_name[key][language],
                            "data": [entry[key] for entry in kpi_data],
                            "percentage": False if key == kpis[0] else True,
                        }
                        for key in kpis
                        if {entry[key] for entry in kpi_data} != {0}
                    ]
                    xAxis = [i['time'] for i in kpi_data]

                    title = k + ' ' + j + ' ' + name_kpi
                    tab_list = [{'type': 'bar_line',"name": sx_mixed_chart_name['bar_line'][language]}]

                    title = k + ' ' + j + ' ' + name_kpi
                    tab_list = [{'type': 'bar_line',"name": sx_mixed_chart_name['bar_line'][language]}]
                    chart = {
                        "type": "chart",
                        "title": title,
                        "chart": {
                            "chart_type": "mixed",
                            "xAxis": xAxis,
                            "series": series,
                            "tab_list": tab_list,
                        },
                    }
                    chart_list.append(chart)
