@app.route("/conversation/chat", methods=['POST'])
async def chat(request: Request):
    agent = None
    try:
        agent = ChatNewAgent()
        await agent.get_input(request)
        return StreamingResponse(
            agent.event_stream(),
            media_type="text/event-stream"
        )
    except Exception as e:
        t_result = time.time()
        e_msg = traceback.format_exc()
        if agent:
            agent.save_log('异常捕获', e_msg, t_result - agent.input_time)
            answerText = error_reply[agent.language]
            await agent.ws_return_content(answerText, content_type="text", ended=True)
        return JSONResponse(
            status_code=500,
            content={"error": str(e), "message": "处理请求时发生错误"}
        )
    finally:
        if agent:
            await agent.cleanup()  # 假设有cleanup方法，如果没有需要实现

@app.route("/conversation/history", methods=['GET'])
async def get_conversation_history(request: Request):
    redis_db = None
    try:
        data = await request.json()
        conversation_id = data.get('conversation_id')
        if not conversation_id:
            return JSONResponse(
                status_code=400,
                content={"error": "conversation_id is required"}
            )
            
        redis_db = await RedisDB().connect()
        conversation_all = await redis_db.get_history_all(conversation_id)
        return JSONResponse(content=conversation_all)
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": str(e), "message": "获取历史记录时发生错误"}
        )
    finally:
        if redis_db:
            await redis_db.close()

@app.route("/user/history", methods=['GET'])
async def get_user_history(request: Request):
    redis_db = None
    try:
        data = await request.json()
        user_id = data.get('user_id')
        if not user_id:
            return JSONResponse(
                status_code=400,
                content={"error": "user_id is required"}
            )
        
        redis_db = await RedisDB().connect()
        conversation_all = await redis_db.get_history_all(user_id)
        return JSONResponse(content=conversation_all)
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": str(e), "message": "获取用户历史记录时发生错误"}
        )
    finally:
        if redis_db:
            await redis_db.close()

if __name__ == "__main__":
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8000, 
        workers=1, 
        ws_ping_interval=15, 
        ws_ping_timeout=5, 
        log_level="debug"
    ) 