import json
from utils.sx_dict import *
import os
from utils.sx_date import answer_date




def chart_select(display):
    """根据指标和提取到的数据展示类型选择图表

    Args:
        display (String): 数据展示形式

    Returns:
        chart_type: 选择的图表类型
        type: 混合图的展示内容
    """
    chart_type = 'run_chart'
    type = []
    if display == '漏斗':
        chart_type = 'funnel'
    elif display == '分布':
        chart_type = 'mixed'
        type = ["bar","line",'pie']
    elif display == '趋势':
        chart_type = 'run_chart'
    elif display == '同环比':
        chart_type = 'table'
    elif display in ['排名','对比']:
        chart_type = 'mixed'
        type = ["bar","line"]
    elif display == '目标':
        chart_type = 'target_completion'
    return chart_type,type


sx_kpi_value_percentage_dict = {"销量":{"value":"volume","percentage":"marketShare"},
                                "上险":{"value":"volume","percentage":"marketShare"},
                                "客源数":{"value":"leadsNotDuplicateCnt","percentage":"leadsTransferRate"},
                                "潜客数":{"value":"oppCnt","percentage":"leadsArrivalRate"},
                                "展厅客流数":{"value":"oppWalkinCnt","percentage":"oppWalkinTransferRate"},
                                "试乘试驾数":{"value":"oppTestdriveCnt","percentage":"testdriveRate"},
                                "订单数":{"value":"orderCnt","percentage":"orderDeliveryNotSvkRate"},
                                "发票数":{"value":"deliveryEinvoiceNotSvkCnt","percentage":"orderDeliveryNotSvkRate"},
                                "本期日均客源数":{"value":"leadsNotDuplicateCntDayAvg","percentage":"leadsTransferRate"},
                                "本期日均潜客数":{"value":"oppCntDayAvg","percentage":"leadsArrivalRate"},
                                "本期日均展厅客流数":{"value":"oppWalkinCntDayAvg","percentage":"oppWalkinTransferRate"},
                                "本期日均试乘试驾数":{"value":"oppTestdriveCntDayAvg","percentage":"testdriveRate"},
                                "本期日均订单数":{"value":"orderCntDayAvg","percentage":"orderDeliveryNotSvkRate"},
                                "本期日均发票数":{"value":"deliveryEinvoiceNotSvkCntDayAvg","percentage":"orderDeliveryNotSvkRate"},
                                "日均含大客户发票数":{"value":"eInvoiceCntDayAvg"},
                                "日均批售数":{"value":"wholesaleCntDayAvg"},
                                "批售数":{"value":"wholesaleCnt"},
                                "含大客户发票数":{"value":"eInvoiceCnt"},
                                "总库存":{"value":"stockTotalCnt","percentage":"stockTotalIndex"},
                                "总部库存":{"value":"stockTotalHqCnt","percentage":"stockTotalHqIndex"}}


sx_kpi_compare_dict = {"销量":{"value":"volume","percentage":False},
                       "市占":{"value":"marketShare","percentage":True},
                        "客源数":{"value":"leadsNotDuplicateCnt","percentage":False},
                        "潜客数":{"value":"oppCnt","percentage":False},
                        "展厅客流数":{"value":"oppWalkinCnt","percentage":False},
                        "试乘试驾数":{"value":"oppTestdriveCnt","percentage":False},
                        "订单数":{"value":"orderCnt","percentage":False},
                        "发票数":{"value":"deliveryEinvoiceNotSvkCnt","percentage":False},
                        "本期日均客源数":{"value":"leadsNotDuplicateCntDayAvg","percentage":False},
                        "本期日均潜客数":{"value":"oppCntDayAvg","percentage":False},
                        "本期日均展厅客流数":{"value":"oppWalkinCntDayAvg","percentage":False},
                        "本期日均试乘试驾数":{"value":"oppTestdriveCntDayAvg","percentage":False},
                        "本期日均订单数":{"value":"orderCntDayAvg","percentage":False},
                        "本期日均发票数":{"value":"deliveryEinvoiceNotSvkCntDayAvg","percentage":False},
                        "日均含大客户发票数":{"value":"eInvoiceCntDayAvg","percentage":False},
                        "日均批售数":{"value":"wholesaleCntDayAvg","percentage":False},
                        "线索转化率":{"value":"leadsTransferRate","percentage":True},
                        "线索到店率":{"value":"leadsArrivalRate","percentage":True}, 
                        "试乘试驾率":{"value":"testdriveRate","percentage":True}, 
                        "到店转化率":{"value":"oppWalkinTransferRate","percentage":True}, 
                        "订单成交率":{"value":"orderDeliveryNotSvkRate","percentage":True},
                        "批售数":{"value":"wholesaleCnt","percentage":False},
                        "含大客户发票数":{"value":"eInvoiceCnt","percentage":False},
                        "总库存":{"value":"stockTotalCnt","percentage":False},
                        "总部库存数":{"value":"stockTotalHqCnt","percentage":False},
                        "总库存当量":{"value":"stockTotalIndex","percentage":False},
                        "总部库存当量":{"value":"stockTotalHqIndex","percentage":False}}


sx_kpi_table_dict = {"volume":{"mom":"volumeMom","yoy":"volumeYoy"},
                     "marketShare":{"mom":"monthOnMonthChange","yoy":"yearOnYearChange"},
                     "leadsNotDuplicateCnt":{"mom":"leadsNotDuplicateCntDayAvgMom","yoy":"leadsNotDuplicateCntDayAvgYoy"},
                     "oppCnt":{"mom":"oppCntDayAvgMom","yoy":"oppCntDayAvgYoy"},
                     "oppWalkinCnt":{"mom":"oppWalkinCntDayAvgMom","yoy":"oppWalkinCntDayAvgYoy"},
                     "oppTestdriveCnt":{"mom":"oppTestdriveCntDayAvgMom","yoy":"oppTestdriveCntDayAvgYoy"},
                     "orderCnt":{"mom":"orderCntDayAvgMom","yoy":"orderCntDayAvgYoy"},
                     "deliveryEinvoiceNotSvkCnt":{"mom":"deliveryEinvoiceNotSvkCntDayAvgMom","yoy":"deliveryEinvoiceNotSvkCntDayAvgYoy"},
                     "leadsNotDuplicateCntDayAvg":{"mom":"leadsNotDuplicateCntDayAvgMom","yoy":"leadsNotDuplicateCntDayAvgYoy"},
                     "oppCntDayAvg":{"mom":"oppCntDayAvgMom","yoy":"oppCntDayAvgYoy"},
                     "oppWalkinCntDayAvg":{"mom":"oppWalkinCntDayAvgMom","yoy":"oppWalkinCntDayAvgYoy"},
                     "oppTestdriveCntDayAvg":{"mom":"oppTestdriveCntDayAvgMom","yoy":"oppTestdriveCntDayAvgYoy"},
                     "orderCntDayAvg":{"mom":"orderCntDayAvgMom","yoy":"orderCntDayAvgYoy"},
                     "deliveryEinvoiceNotSvkCntDayAvg":{"mom":"deliveryEinvoiceNotSvkCntDayAvgMom","yoy":"deliveryEinvoiceNotSvkCntDayAvgYoy"},                     
                     "leadsTransferRate":{"mom":"leadsTransferRateMom","yoy":"leadsTransferRateYoy"},
                     "leadsArrivalRate":{"mom":"leadsArrivalRateMom","yoy":"leadsArrivalRateYoy"},
                     "testdriveRate":{"mom":"testdriveRateMom","yoy":"testdriveRateYoy"},
                     "oppWalkinTransferRate":{"mom":"oppWalkinTransferRateMom","yoy":"oppWalkinTransferRateYoy"},
                     "orderDeliveryNotSvkRate":{"mom":"orderDeliveryNotSvkRateMom","yoy":"orderDeliveryNotSvkRateYoy"},
                     "wholesaleCnt":{"mom":"wholesaleCntDayAvgMom","yoy":"wholesaleCntDayAvgYoy"},
                     "eInvoiceCnt":{"mom":"eInvoiceCntDayAvgMom","yoy":"eInvoiceCntDayAvgYoy"},
                     "wholesaleCntDayAvg":{"mom":"wholesaleCntDayAvgMom","yoy":"wholesaleCntDayAvgYoy"},
                     "eInvoiceCntDayAvg":{"mom":"eInvoiceCntDayAvgMom","yoy":"eInvoiceCntDayAvgYoy"},
                     }


def cn_to_en_mappings(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        mappings = {line.strip().split('\t')[1]:line.strip().split('\t')[0] for line in file}
    return mappings

def en_to_cn_mappings(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        mappings = {line.strip().split('\t')[0]:line.strip().split('\t')[1] for line in file}
    return mappings
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
model_file_path = os.path.join(parent_dir, 'data', 'model_name_transfer.tsv')
model_en_to_cn = en_to_cn_mappings(model_file_path)
location_file_path = os.path.join(parent_dir, 'data', 'location_name.tsv')
location_cn_to_en = cn_to_en_mappings(location_file_path)




def warroom_data_language_transform(data,language):
    """沙盘数据查询结果的中英文转化

    Args:
        data (Json): 沙盘接口查询的数据
        language (String): 问题的语言

    Returns:
        data (Json): 根据问题语言转换后的沙盘数据查询结果
    """
    if language == "英文":
        for i in data:
            i["location"] = location_cn_to_en.get(i["location"], i["location"])
            i["parentLocation"] = location_cn_to_en.get(i["parentLocation"], i["parentLocation"])
    return data


def model_name_process(model):
    """车型名称处理，去除前缀

    Args:
        model (String): 车型名称

    Returns:
        String: 去除前缀后的车型名称
    """
    prefix_list = ["MAIN-","HIGH-","ID-","SK-"]
    for prefix in prefix_list:
        if model.startswith(prefix):
            model = model[len(prefix):]
            break
    return model



def location_language_transform(location_list,language):
    """清洗后Json的中英文转化

    Args:
        data_list (List): 待转化语言的List
        language (String): 问题的语言

    Returns:
        List: 进行中英文转换后的List
    """
    if language == "英文":
        location_transfrom = [location_cn_to_en.get(i, i) if i != "Nationwide" else i for i in location_list]
    elif language == "中文":
        location_transfrom = [location_cn_to_en.get(i, i) if i == "Nationwide" else i for i in location_list]
    return location_transfrom



# 图表内容展示
# 漏斗，目标按照固定内容显示；趋势，分布，排名，对比按照指定指标显示，其中趋势图多了率
# 由于返回数据包括多个车型，因此需要替换实际查询的车型
# 同环比：定义查询表格，包括值/率，上月，去年同期，同比，环比
# 图表中存在非标准的名称，需要在计算集合时排除掉
def content_show(format_json,data,chart_type,type,language):
    """图表内容展示

    Args:
        format_json (Json): 清洗后的Json
        data (List): 沙盘接口查询的数据
        chart_type (String): 选择的图表类型
        type (List): 混合图的展示内容
        language (String): 语言

    Returns:
        chart_list (List): 给沙盘前端展示的数据
    """

    data = warroom_data_language_transform(data,language)
    format_json = json.loads(format_json)

    json_kpi = format_json["kpi"]
    json_display = format_json["display"]
    json_data_dim = format_json["data_dim"]
    json_location = location_language_transform(format_json["location"],language)
    json_model = format_json["model"]
    json_today = format_json["today"]
    json_date_type = format_json["date_type"]
    json_not_total = format_json["not_total"]
    chart_list = []
    # 趋势图的value和percentage绑定在一起，需要同时从data中查出指标的值和率
    if chart_type == 'run_chart':
        # 由于查询结果会对model和location进行更明细的查询，所以需要先提取可能出现的枚举值
        data_locations = list(set([i["location"] for i in data]))
        data_models = list(set([i["model"] for i in data]))
        # 按指标、车型、地区拆分成多个图
        for kpi_temp in json_kpi:
            if kpi_temp in sx_kpi_value_percentage_dict.keys():
                if kpi_temp in ['批售数','含大客户发票数','日均批售数','日均含大客户发票数']:
                    kpi_value = sx_kpi_value_percentage_dict[kpi_temp]["value"]
                    kpi_mom = sx_kpi_table_dict[kpi_value]['mom']
                    kpi_yoy = sx_kpi_table_dict[kpi_value]['yoy']
                    # 查询标题默认返回值的名称
                    name_kpi = sx_kpi_name[kpi_value][language]
                    for j in data_models:
                        for k in data_locations:
                            # 按时间升序
                            output_dict = {i["time"]:[i["kpiValues"][kpi_value],i["kpiValues"][kpi_mom],i["kpiValues"][kpi_yoy]] 
                                           for i in data if i["model"] == j 
                                        and i["location"] == k and kpi_value in i["kpiValues"].keys()}
                            output_dict = sorted(output_dict.items(),key=lambda x:x[0])
                            if json_date_type == 'week':
                                xAxis = [answer_date(json_date_type,i[0],'英文') for i in output_dict]
                            else:
                                xAxis = [i[0] for i in output_dict]
                            data_volume = [i[1][0] for i in output_dict]
                            # data_mom = [i[1][1] for i in output_dict]
                            # data_yoy = [i[1][2] for i in output_dict]
                            # 如果值都为0则不展示
                            if list(set(data_volume)) == [0]:
                                continue
                            name_volume = sx_kpi_name[kpi_value][language]
                            # name_mom = sx_kpi_name[kpi_mom][language]
                            # name_yoy = sx_kpi_name[kpi_yoy][language]
                            title = k + ' ' + j + ' ' + name_kpi
                            tab_list = [{'type': 'bar_line',"name": sx_mixed_chart_name['bar_line'][language]}]
                            chart = {"type": "chart","title": title,"chart": {
                                    "chart_type": "mixed",
                                    "xAxis": xAxis,
                                    "series": [{"name": name_volume,"data": data_volume}
                                            #    ,{"name": name_mom,"percentage": True,"data": data_mom}
                                            #    ,{"name": name_yoy,"percentage": True,"data": data_yoy}
                                               ],"tab_list": tab_list,},
                                    }
                            chart_list.append(chart)

                elif kpi_temp in ['总库存','总部库存']:
                    kpi_value = sx_kpi_value_percentage_dict[kpi_temp]["value"]
                    kpi_percentage = sx_kpi_value_percentage_dict[kpi_temp]["percentage"]
                    # 查询标题默认返回值的名称
                    name_kpi = sx_kpi_name[kpi_value][language]
                    for j in data_models:
                        for k in data_locations:
                            # 按时间升序
                            output_dict = {i["time"]:[i["kpiValues"][kpi_value],i["kpiValues"][kpi_percentage]] for i in data if i["model"] == j 
                                        and i["location"] == k and kpi_value in i["kpiValues"].keys()}
                            output_dict = sorted(output_dict.items(),key=lambda x:x[0])
                            if json_date_type == 'week':
                                xAxis = [answer_date(json_date_type,i[0],'英文') for i in output_dict]
                            else:
                                xAxis = [i[0] for i in output_dict]
                            data_volume = [i[1][0] for i in output_dict]
                            # 如果值都为0则不展示
                            if list(set(data_volume)) == [0]:
                                continue
                            data_percentage = [i[1][1] for i in output_dict]
                            name_volume = sx_kpi_name[kpi_value][language]
                            name_percentage = sx_kpi_name[kpi_percentage][language]
                            title = k + ' ' + j + ' ' + name_kpi
                            tab_list = [{'type': 'bar_line',"name": sx_mixed_chart_name['bar_line'][language]}]
                            chart = {"type": "chart","title": title,"chart": {
                                    "chart_type": "mixed",
                                    "xAxis": xAxis,
                                    "series": [{"name": name_volume,"data": data_volume,"type": "bar"},
                                               {"name": name_percentage,"data": data_percentage,"type": "line"},],"tab_list": tab_list,},
                                    }
                            chart_list.append(chart)

                else:
                    kpi_value = sx_kpi_value_percentage_dict[kpi_temp]["value"]
                    kpi_percentage = sx_kpi_value_percentage_dict[kpi_temp]["percentage"]
                    kpi_mom = sx_kpi_table_dict[kpi_value]['mom']
                    kpi_yoy = sx_kpi_table_dict[kpi_value]['yoy']
                    # 查询标题默认返回值的名称
                    name_kpi = sx_kpi_name[kpi_value][language]
                    for j in data_models:
                        for k in data_locations:
                            # 按时间升序
                            output_dict = {i["time"]:[i["kpiValues"][kpi_value],i["kpiValues"][kpi_percentage],i["kpiValues"][kpi_mom],i["kpiValues"][kpi_yoy]] 
                                           for i in data if i["model"] == j 
                                        and i["location"] == k and kpi_value in i["kpiValues"].keys()}
                            output_dict = sorted(output_dict.items(),key=lambda x:x[0])
                            if json_date_type == 'week':
                                xAxis = [answer_date(json_date_type,i[0],'英文') for i in output_dict]
                            else:
                                xAxis = [i[0] for i in output_dict]
                            data_volume = [i[1][0] for i in output_dict]
                            # 如果值都为0则不展示
                            if list(set(data_volume)) == [0]:
                                continue
                            data_percentage = [i[1][1] for i in output_dict]
                            # data_mom = [i[1][2] for i in output_dict]
                            # data_yoy = [i[1][3] for i in output_dict]
                            name_volume = sx_kpi_name[kpi_value][language]
                            name_percentage = sx_kpi_name[kpi_percentage][language]
                            # name_mom = sx_kpi_name[kpi_mom][language]
                            # name_yoy = sx_kpi_name[kpi_yoy][language]
                            title = k + ' ' + j + ' ' + name_kpi
                            tab_list = [{'type': 'bar_line',"name": sx_mixed_chart_name['bar_line'][language]}]
                            chart = {"type": "chart","title": title,"chart": {
                                    "chart_type": "mixed",
                                    "xAxis": xAxis,
                                    "series": [{"name": name_volume,"data": data_volume,"type": "bar"}
                                               ,{"name": name_percentage,"percentage": True,"data": data_percentage,"type": "line"}
                                            #    ,{"name": name_mom,"percentage": True,"data": data_mom}
                                            #    ,{"name": name_yoy,"percentage": True,"data": data_yoy}
                                               ],"tab_list": tab_list,},
                                    }
                            chart_list.append(chart)

    # 混合图表
    elif chart_type == "mixed":
        # 根据问题语言对图形类型名称进行转换
        tab_list = [{'type': tmp_type,"name": sx_mixed_chart_name[tmp_type][language]} for tmp_type in type]
        if type == ["bar","line",'pie'] and json_display == "分布":
            # 按照地区维度分布，按车型和指标拆分成不同图
            if json_data_dim == "location":
                dim_tmp = "model"
                title_dim = "&".join(json_location)
            elif json_data_dim == "model":
                dim_tmp = "location"
                title_dim = "&".join(json_model)
            else:
                dim_tmp = "location"
                title_dim = "&".join(json_model)

            if dim_tmp == "location":
                dim_parent = "brand"
            else:
                dim_parent = "parentLocation"
            dim_parent_list = list(set([i[dim_parent] if i[dim_parent] else 'SVW-VW' for i in data]))
            dim_list = list(set([i[dim_tmp] for i in data]))
            for kpi_temp in json_kpi:
                is_percentage = sx_kpi_compare_dict[kpi_temp]["percentage"]
                if is_percentage == True:
                    tmp_tab_list = [tmp for tmp in tab_list if tmp['type'] != 'pie']
                else:
                    tmp_tab_list = tab_list
                kpi_temp_value = sx_kpi_compare_dict[kpi_temp]["value"]
                name_kpi = sx_kpi_name[kpi_temp_value][language]

                for j in dim_list:
                    for k in dim_parent_list:
                        output_dict = {model_name_process(i[json_data_dim]):i["kpiValues"][kpi_temp_value] for i in data if i[dim_tmp] == j and i['time'] == 'total' 
                                       and kpi_temp_value in i["kpiValues"].keys() and (i[dim_parent] == k or (not i[dim_parent] and k == 'SVW-VW'))
                                    and ( ( ( (i["model"].count('-') == 1 and kpi_temp not in ["销量","市占"]) or (kpi_temp in ["销量","市占"] and i['model'] != i['brand'])) and json_data_dim == 'model') 
                                            or json_data_dim != 'model') and i['location'] not in ['总计','Total'] and i['model'] not in ['总计','Total']}
                        series = [{"name": "","percentage": is_percentage,"data": list(output_dict.values())}]
                        xAxis = list(output_dict.keys())
                        title = j + ' ' + k + ' ' + name_kpi + ' ' + sx_display_name["分布"][language]
                        chart_list.append({"type": "chart","title": title,"chart": {"chart_type": "mixed","xAxis":xAxis,"series":series,"tab_list": tmp_tab_list}})


        elif type == ["bar","line"] and json_display in ["排名","对比"]:
            data_times = list(set([i["time"] for i in data]))
            data = [i for i in data if i["time"] != 'total']

            title_display = sx_display_name[json_display][language]
            type = ["bar"]
            tab_list = [{'type': tmp_type,"name": sx_mixed_chart_name[tmp_type][language]} for tmp_type in type]
            kpi = [kpi_temp for kpi_temp in json_kpi if kpi_temp in sx_kpi_compare_dict.keys()]
            
            for kpi_temp in kpi:
                kpi_temp_value = sx_kpi_compare_dict[kpi_temp]["value"]
                name_kpi = sx_kpi_name[kpi_temp_value][language]

                if len(data_times) == 1:

                    output_dict = {i["location"]+'-'+i["model"]:i["kpiValues"][kpi_temp_value] for i in data 
                                    if ( ( ( (i["model"].count('-') == 1 and kpi_temp not in ["销量","市占"]) or (kpi_temp in ["销量","市占"])) and json_data_dim == 'model') 
                                        or json_data_dim != 'model') and kpi_temp_value in i["kpiValues"].keys()
                                        and i['location'] not in ['总计','Total'] and i['model'] not in ['总计','Total']}

                else:
                    output_dict = {i["time"]+' '+i["location"]+'-'+i["model"]:i["kpiValues"][kpi_temp_value] for i in data 
                                   if ( ( ( (i["model"].count('-') == 1 and kpi_temp not in ["销量","市占"]) or (kpi_temp in ["销量","市占"])) and json_data_dim == 'model') 
                                            or json_data_dim != 'model') and kpi_temp_value in i["kpiValues"].keys()
                                            and i['location'] not in ['总计','Total'] and i['model'] not in ['总计','Total']}
                    
                output_dict = sorted(output_dict.items(),key=lambda x:x[1],reverse=True)
                output_data = [i[1] for i in output_dict]
                xAxis = [i[0] for i in output_dict]

                title_model = "&".join(json_model)
                title_location = "&".join(json_location)
                name = title_location + ' ' + title_model + ' ' + name_kpi
                title = title_location + ' ' + title_model + ' ' + name_kpi + ' ' + title_display
                series = [{"name": name,"percentage": sx_kpi_compare_dict[kpi_temp]["percentage"],"data": output_data}]
                chart_list.append({"type": "chart","title": title,"chart": {"chart_type": "mixed","xAxis":xAxis,"series":series,"tab_list": tab_list}})

    elif chart_type == "target_completion":
        if json_today == 'True':
            kpi = []
            for i in data:
                kpi = kpi + list(i['kpiValues'].keys())
            kpi = list(set(kpi))
            for kpi_temp in kpi:
                if json_data_dim == 'model':
                    model_group = list(set([i['model'] for i in data if i['model'] in ['ID','MAIN','HIGH','SK']]))
                    if model_group != []:
                        for tmp_model_group in model_group:
                            output_data = [{"completion": i['kpiValues'][kpi_temp],
                                            "name": i["location"]+'-'+i["model"],
                                            } for i in data if i['time'] == 'total' and (tmp_model_group == i["model"] or tmp_model_group+'-' in i["model"])
                                            and ((i["model"].count('-') <= 1 and json_data_dim == 'model') or json_data_dim != 'model')]
                            title_location = "&".join([i for i in json_location])
                            title_model = tmp_model_group
                            title_kpi = sx_kpi_name[kpi_temp][language]
                            title = title_location + ' ' + title_model + ' ' + title_kpi
                            output_data = sorted(output_data, key=lambda x: x["completion"], reverse=True)
                            legend = {"completion": sx_kpi_name["completion"][language]}
                            chart_list.append({"type": "chart","title": title,"chart": {"chart_type": "target_completion","data":output_data,"legend":legend}})
                    else:
                        output_data = [{"completion": i['kpiValues'][kpi_temp],
                                        "name": i["location"]+'-'+i["model"],
                                        } for i in data if i['time'] == 'total'
                                        and ((i["model"].count('-') <= 1 and json_data_dim == 'model') or json_data_dim != 'model')]
                        title_location = "&".join([i for i in json_location])
                        title_model = "&".join([i for i in json_model])
                        title_kpi = sx_kpi_name[kpi_temp][language]
                        title = title_location + ' ' + title_model + ' ' + title_kpi
                        output_data = sorted(output_data, key=lambda x: x["completion"], reverse=True)
                        legend = {"completion": sx_kpi_name["completion"][language]}
                        chart_list.append({"type": "chart","title": title,"chart": {"chart_type": "target_completion","data":output_data,"legend":legend}})                                            
                else:
                    output_data = [{"completion": i['kpiValues'][kpi_temp],
                                    "name": i["location"]+'-'+i["model"],
                                    } for i in data if i['time'] == 'total'
                                    and ((i["model"].count('-') <= 1 and json_data_dim == 'model') or json_data_dim != 'model')]
                    title_location = "&".join([i for i in json_location])
                    title_model = "&".join([i for i in json_model])
                    title_kpi = sx_kpi_name[kpi_temp][language]
                    title = title_location + ' ' + title_model + ' ' + title_kpi
                    output_data = sorted(output_data, key=lambda x: x["completion"], reverse=True)
                    legend = {"completion": sx_kpi_name["completion"][language]}
                    chart_list.append({"type": "chart","title": title,"chart": {"chart_type": "target_completion","data":output_data,"legend":legend}})
        else:
            kpi = []
            for i in data:
                kpi = kpi + list(i['kpiValues'].keys())
            kpi = list(set(kpi))
            for kpi_temp in kpi:
                output_data = [{"target": i['kpiValues'][kpi_temp]["target"],
                                "completion": i['kpiValues'][kpi_temp]["completion"],
                                "completion_rate": i['kpiValues'][kpi_temp]["completionRate"],
                                "name": i["location"]+'-'+i["model"],
                                "time_schedule": i['kpiValues'][kpi_temp]["timeSchedule"],
                                } for i in data if i['time'] == 'total'
                                and ((i["model"].count('-') <= 1 and json_data_dim == 'model') or json_data_dim != 'model')]
                title_location = "&".join([i for i in json_location])
                title_model = "&".join([i for i in json_model])
                title_kpi = sx_kpi_name[kpi_temp][language]
                title = title_location + ' ' + title_model + ' ' + title_kpi
                output_data = sorted(output_data, key=lambda x: x["completion_rate"], reverse=True)
                # 什么情况下算一个目标达成图
                legend = {"completion": sx_kpi_name["completion"][language],"target": sx_kpi_name["target"][language],"time_schedule": sx_kpi_name["time_schedule"][language]}
                chart_list.append({"type": "chart","title": title,"chart": {"chart_type": "target_completion","data":output_data,"legend":legend}})
    
    elif chart_type == "table":
        # 目前json_kpi中应该只含有1个kpi，后续可以去掉遍历处理
        data_times = list(set([i["time"] for i in data]))
        for kpi_temp in json_kpi:
            if kpi_temp in sx_kpi_value_percentage_dict.keys():
                if kpi_temp != '销量':
                    if '库存' in kpi_temp:
                        kpi_value = sx_kpi_value_percentage_dict[kpi_temp]["value"]
                        kpi_percentage = sx_kpi_value_percentage_dict[kpi_temp]["percentage"]
                        data_locations = list(set([i["location"] for i in data]))
                        data_models = list(set([i["model"] for i in data]))
                        output_data = [{sx_kpi_name["time"][language]: i["time"],
                                        sx_kpi_name["location"][language]: i["location"],
                                        sx_kpi_name["model"][language]: i["model"],
                                        sx_kpi_name[kpi_value][language]: i['kpiValues'][kpi_value],
                                        sx_kpi_name[kpi_percentage][language]: i['kpiValues'][kpi_percentage],
                                        } for i in data if kpi_value in i["kpiValues"].keys() and ((json_not_total == "True" and len(data_times) != 2) or i["time"] == 'total')]
                    else:
                        kpi_value = sx_kpi_value_percentage_dict[kpi_temp]["value"]
                        data_locations = list(set([i["location"] for i in data]))
                        data_models = list(set([i["model"] for i in data]))
                        output_data = [{sx_kpi_name["time"][language]: i["time"],
                                        sx_kpi_name["location"][language]: i["location"],
                                        sx_kpi_name["model"][language]: i["model"],
                                        sx_kpi_name[kpi_value][language]: i['kpiValues'][kpi_value],
                                        sx_kpi_name[sx_kpi_table_dict[kpi_value]['mom']][language]: str(i['kpiValues'][sx_kpi_table_dict[kpi_value]['mom']])+'%',
                                        sx_kpi_name[sx_kpi_table_dict[kpi_value]['yoy']][language]: str(i['kpiValues'][sx_kpi_table_dict[kpi_value]['yoy']])+'%',
                                        } for i in data if kpi_value in i["kpiValues"].keys() and ((json_not_total == "True" and len(data_times) != 2) or i["time"] == 'total')]
                else:
                    kpi_value = sx_kpi_value_percentage_dict[kpi_temp]["value"]
                    kpi_percentage = sx_kpi_value_percentage_dict[kpi_temp]["percentage"]
                    data_locations = list(set([i["location"] for i in data]))
                    data_models = list(set([i["model"] for i in data]))
                    output_data = [{sx_kpi_name["time"][language]: i["time"],
                                    sx_kpi_name["location"][language]: i["location"],
                                    sx_kpi_name["model"][language]: i["model"],
                                    sx_kpi_name[kpi_value][language]: i['kpiValues'][kpi_value],
                                    sx_kpi_name[sx_kpi_table_dict[kpi_value]['mom']][language]: str(i['kpiValues'][sx_kpi_table_dict[kpi_value]['mom']])+'%',
                                    sx_kpi_name[sx_kpi_table_dict[kpi_value]['yoy']][language]: str(i['kpiValues'][sx_kpi_table_dict[kpi_value]['yoy']])+'%',
                                    sx_kpi_name[kpi_percentage][language]: i['kpiValues'][kpi_percentage],
                                    sx_kpi_name[sx_kpi_table_dict[kpi_percentage]['mom']][language]: str(i['kpiValues'][sx_kpi_table_dict[kpi_percentage]['mom']])+'%',
                                    sx_kpi_name[sx_kpi_table_dict[kpi_percentage]['yoy']][language]: str(i['kpiValues'][sx_kpi_table_dict[kpi_percentage]['yoy']])+'%',
                                    } for i in data if kpi_value in i["kpiValues"].keys() and ((json_not_total == "True" and len(data_times) != 2) or i["time"] == 'total')]
                if output_data == []:
                    continue
                chart_list.append({"type": "table","table": {"data": output_data}}) 


    elif chart_type == "funnel":
        if json_kpi == ["转化"]:
            if json_today == "True" and json_data_dim != "all":
                output_data_list = []
                for i in data:
                    title_location = i["location"]
                    title_model =  i["model"]
                    title_kpi = sx_kpi_name["funnel"][language]
                    title = title_location + " " + title_model + " " + title_kpi
                    output_data =  {"leadsNotDuplicateCnt": i['kpiValues']["todayLeadsNotDuplicateCnt"],
                                    "leadsNotDuplicateCntName": sx_funnel_name["leadsNotDuplicateCnt"][language],
                                    "deliveryEinvoiceNotSvkCnt": i['kpiValues']["todayDeliveryEinvoiceNotSvkCnt"],
                                    "deliveryEinvoiceNotSvkCntName": sx_funnel_name["deliveryEinvoiceNotSvkCnt"][language],
                                    "orderCnt": i['kpiValues']["todayOrderCnt"],
                                    "orderCntName": sx_funnel_name["orderCnt"][language],
                                    "oppTestdriveCnt": i['kpiValues']["todayOppTestdriveCnt"],
                                    "oppTestdriveCntName": sx_funnel_name["oppTestdriveCnt"][language],
                                    "oppWalkinCnt": i['kpiValues']["todayOppWalkinCnt"],
                                    "oppWalkinCntName": sx_funnel_name["oppWalkinCnt"][language],
                                    "oppCnt": i['kpiValues']["todayOppCnt"],
                                    "oppCntName": sx_funnel_name["oppCnt"][language],
                                    "chartTitle": title
                                    }
                    output_data_list.append(output_data)
                chart_list.append({"type": "chart","chart": {"chart_type": "funnel","list": output_data_list}})
            else:
                output_data_list = []
                for i in data:
                    if [j[1] for j in i['kpiValues'].items() if 'target' not in j[0] and int(j[1]) != 0] == []:
                        continue
                    if json_today == "True" and i["time"] == 'total':
                        continue
                    if json_today == "True":
                        title_time = ''
                    else:
                        title_time = i["time"]
                    title_location = i["location"]
                    title_model =  i["model"]
                    title_kpi = sx_kpi_name["funnel"][language]
                    title = title_time + " " + title_location + " " + title_model + " " + title_kpi
                    output_data =  {"leadsNotDuplicateCnt": i['kpiValues']["leadsNotDuplicateCnt"],
                                    "leadsNotDuplicateCntName": sx_funnel_name["leadsNotDuplicateCnt"][language],
                                    "deliveryEinvoiceNotSvkCnt": i['kpiValues']["deliveryEinvoiceNotSvkCnt"],
                                    "deliveryEinvoiceNotSvkCntName": sx_funnel_name["deliveryEinvoiceNotSvkCnt"][language],
                                    "orderCnt": i['kpiValues']["orderCnt"],
                                    "orderCntName": sx_funnel_name["orderCnt"][language],
                                    "oppTestdriveCnt": i['kpiValues']["oppTestdriveCnt"],
                                    "oppTestdriveCntName": sx_funnel_name["oppTestdriveCnt"][language],
                                    "oppWalkinCnt": i['kpiValues']["oppWalkinCnt"],
                                    "oppWalkinCntName": sx_funnel_name["oppWalkinCnt"][language],
                                    "oppCnt": i['kpiValues']["oppCnt"],
                                    "oppCntName": sx_funnel_name["oppCnt"][language],
                                    "leadsTransferRate": i['kpiValues']["leadsTransferRate"],
                                    "leadsTransferRateName": sx_funnel_name["leadsTransferRate"][language],
                                    "leadsArrivalRate": i['kpiValues']["leadsArrivalRate"],
                                    "leadsArrivalRateName": sx_funnel_name["leadsArrivalRate"][language],
                                    "testdriveRate": i['kpiValues']["testdriveRate"],
                                    "testdriveRateName": sx_funnel_name["testdriveRate"][language],
                                    "oppWalkinTransferRate": i['kpiValues']["oppWalkinTransferRate"],
                                    "oppWalkinTransferRateName": sx_funnel_name["oppWalkinTransferRate"][language],
                                    "orderDeliveryNotSvkRate": i['kpiValues']["orderDeliveryNotSvkRate"],
                                    "orderDeliveryNotSvkRateName": sx_funnel_name["orderDeliveryNotSvkRate"][language],
                                    "leadsNotDuplicateCntTarget": i['kpiValues']["targetLeads"]["target"],
                                    "leadsNotDuplicateCompleteRate": round(i['kpiValues']["targetLeads"]["completionRate"] / 100, 4),
                                    "oppCntTarget": i['kpiValues']["targetOpportunity"]["target"],
                                    "oppCompleteRate": round(i['kpiValues']["targetOpportunity"]["completionRate"] / 100, 4),
                                    "oppWalkinCntTarget": i['kpiValues']["targetOppWalkin"]["target"],
                                    "oppWalkinCompleteRate": round(i['kpiValues']["targetOppWalkin"]["completionRate"] / 100, 4),
                                    "oppTestdriveCntTarget": i['kpiValues']["targetOppTestdrive"]["target"],
                                    "oppTestdriveCompleteRate": round(i['kpiValues']["targetOppTestdrive"]["completionRate"] / 100, 4),
                                    "orderCntTarget": i['kpiValues']["targetOrder"]["target"],
                                    "orderCompleteRate": round(i['kpiValues']["targetOrder"]["completionRate"] / 100, 4),
                                    "deliveryEinvoiceNotSvkCntTarget": i['kpiValues']["targetInvoice"]["target"],
                                    "deliveryEinvoiceNotSvkCompleteRate": round(i['kpiValues']["targetInvoice"]["completionRate"] / 100, 4),
                                    "timeSchedule": round(i['kpiValues']["targetLeads"]["timeSchedule"] / 100, 4),
                                    "finishLegendName": sx_kpi_name["completion"][language],
                                    "targetLegendName": sx_kpi_name["target"][language],
                                    "timeLineLegendName": sx_kpi_name["time_schedule"][language],
                                    "chartTitle": title
                                    }
                    output_data_list.append(output_data)
                chart_list.append({"type": "chart","chart": {"chart_type": "funnel","list": output_data_list}})
    else:
        chart_list = []

    if not chart_list:
        chart_list = []

    return chart_list



if __name__ == "__main__":
    import requests
    warroom_url = "https://sandbox-openapi.svw-volkswagen.com/robot/v3/open/adapter/vw/listCompetitionAnalyse"
    def warroom_post(data: str) -> list:
        url = warroom_url
        headers = {"Content-Type": "application/json"}
        data = json.loads(data)
        result = requests.post(url, headers=headers,data=json.dumps(data)).json()
        print(result)
        return result["data"]
    json_index = '{"start_time": "202309", "end_time": "202310", "model": ["Passat"], "location": ["华北","西北","华东","西南","东南"], "kpi": ["销量"], "display": "分布", "today": "False", "data_dim": "all"}'
    # warroom_json = '{"start_time": "202301", "end_time": "202310", "model": ["Lavida", "Sylphy"], "location": ["Nationwide"], "kpi": ["销量"], "display": "趋势", "today": "False", "data_dim": "all"}'
    # json_index = '{"start_time": "202301", "end_time": "202310", "model": ["Lavida", "Sylphy"], "location": ["Nationwide"], "kpi": ["销量"], "display": "对比", "today": "False", "data_dim": "all"}'
    # warroom_json = '{"start_time": "202309", "end_time": "202310", "model": ["Passat","Lavida"], "location": ["Nationwide"], "kpi": ["发票数"], "display": "趋势", "today": "False", "data_dim": "all"}'
    # json_index = '{"start_time": "202309", "end_time": "202310", "model": ["Passat","Lavida"], "location": ["Nationwide"], "kpi": ["发票数"], "display": "对比", "today": "False", "data_dim": "all"}'
    # chart_type,type = chart_select(json_index)
    # chart_type,type = 'run_chart',[]
    # print(chart_type)
    # print(type)
    # response_warroom = [{'time': 'total', 'location': '全国', 'model': 'Audi', 'kpiValues': {'volume': 0, 'marketShare': 0.0}}, {'time': '202309', 'location': '全国', 'model': 'Audi', 'kpiValues': {'volume': 0, 'marketShare': 0.0}}, {'time': '202310', 'location': '全国', 'model': 'Audi', 'kpiValues': {'volume': 0, 'marketShare': 0.0}}]
    # print(response_warroom)
    # language = "中文"
    # json_show = content_show(json_index,response_warroom,chart_type,type,language)
    # print(json_show)
    