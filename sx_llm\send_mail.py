# from exchangelib import Credentials, Account, Message, HTMLBody, Configuration, NTLM
# from exchangelib.protocol import Base<PERSON>rotocol, NoVerifyHTTPAdapter
# from requests import Session
# from requests.adapters import HTTPAdapter
# from requests.packages.urllib3.poolmanager import PoolManager
# import ssl
# import warnings
# warnings.filterwarnings('ignore')

# # Define a custom SSL adapter to ignore SSL certificate verification
# class NoVerifyAdapter(HTTPAdapter):
#     def init_poolmanager(self, *args, **kwargs):
#         context = ssl.create_default_context()
#         context.check_hostname = False
#         context.verify_mode = ssl.CERT_NONE
#         self.poolmanager = PoolManager(*args, **kwargs, ssl_context=context)

# # Use the custom adapter to ignore SSL certificate verification
# BaseProtocol.HTTP_ADAPTER_CLS = NoVerifyHTTPAdapter

# # Email configuration
# email_address = '<EMAIL>'
# password = 'Lk19941021o!'
# server = 'itpoc.csvw.com'

# # Create credentials and account
# credentials = Credentials(email_address, password)
# config = Configuration(server=server, credentials=credentials, auth_type=NTLM)
# account = Account(email_address, credentials=credentials, autodiscover=False, config = config)

# # Compose the email
# subject = 'Test Email'
# body = HTMLBody('<p>This is a test email sent from Python.</p>')
# to_recipients = ['<EMAIL>']

# message = Message(account=account,
#                   folder=account.sent,
#                   subject=subject,
#                   body=body,
#                   to_recipients=to_recipients)

# # Send the email
# message.send()
# print('Email sent successfully.')




# from exchangelib import Credentials, Account, Configuration, Message, DELEGATE

# # 设置凭证
# credentials = Credentials(username='<EMAIL>', password='Lk19941021o!')

# # 手动指定服务器和EWS端点
# config = Configuration(credentials=credentials, service_endpoint='https://partner.outlook.cn/owa/csvw.com/')

# # 创建帐户对象
# account = Account('<EMAIL>', config=config, autodiscover=False, access_type=DELEGATE)

# # 创建邮件
# m = Message(
#     account=account,
#     folder=account.sent,
#     subject='Test email from exchangelib with manual configuration',
#     body='This is a test email sent from Python using exchangelib with manual configuration.',
#     to_recipients=['<EMAIL>']
# )

# # 发送邮件
# m.send()


from exchangelib import Credentials, Account, Message, HTMLBody, Configuration, NTLM
from exchangelib.protocol import BaseProtocol, NoVerifyHTTPAdapter

email_address = '<EMAIL>'
password = 'Lk19941021k'
server = 'itpoc.csvw.com'

credentials = Credentials(email_address, password)
config = Configuration(server=server, credentials=credentials, auth_type=NTLM)
account = Account(email_address, credentials=credentials, autodiscover=False, config = config)
message = 'test'
subject = '沙盘问题反馈'
body = HTMLBody('<p>'+message+'</p>')
to_recipients = ['<EMAIL>']

message = Message(account=account,
                folder=account.sent,
                subject=subject,
                body=body,
                to_recipients=to_recipients)

message.send()
print("邮件发送成功")