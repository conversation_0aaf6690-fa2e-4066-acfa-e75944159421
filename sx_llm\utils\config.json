{"huggingface_models": {"m3e-base": "/root/models/m3e-base", "text2vec-large-chinese": "/root/models/text2vec-large-chinese", "nlp_gte_sentence-embedding_chinese-large": "D:/models/nlp_gte_sentence-embedding_chinese-large", "nlp_gte_sentence-embedding_chinese-large-1": "/root/models/nlp_gte_sentence-embedding_chinese-large", "nlp_gte_sentence-embedding_chinese-base": "/root/models/nlp_gte_sentence-embedding_chinese-base"}, "llm_model_configs": {"openai": {"OPENAI_AZURE_ENDPOINT": "https://openai-svw-sx-us2.openai.azure.com/", "OPENAI_API_VERSION": "2023-07-01-preview", "OPENAI_DEPLOYMENT_NAME": "GPT-4-Turbo-SX", "OPENAI_TEMPERATURE": 0, "OPENAI_SEED": 123, "OPENAI_RESPONSE_FORMAT": {"type": "json_object"}}}, "default_huggingface_model_name": "nlp_gte_sentence-embedding_chinese-large", "default_baidu_model_name": "default_baidu_model", "api_key_service_urls": {"baidu": "http://172.20.128.84:31510/account-config/v1/private/account/yiYan", "openai": "http://172.20.128.84:31510/account-config/v1/private/account/chatGPT"}}