
import json
import aiohttp
import httpx
import asyncio
import requests
from requests.adapters import HTTPA<PERSON>pter
from urllib3.util.retry import Retry
from typing import Dict, Any, Optional

# 定义重试策略
retry_strategy = Retry(
    total=3,  # 重试次数
    status_forcelist=[503, 504],  # 触发重试的状态码
    allowed_methods=["HEAD", "GET", "OPTIONS", "POST"],  # 触发重试的请求方式
    backoff_factor=1  # 重试间隔时间基数
)

# 创建一个自定义的HTTPAdapter
adapter = HTTPAdapter(max_retries=retry_strategy)

# 创建一个session
http = requests.Session()
http.mount("http://", adapter)
http.mount("https://", adapter)

# request请求
def request_with_retries(url, data):
    """带有重试的request请求

    Args:
        url (string): 请求地址
        data (_type_): _description_

    Returns:
        _type_: _description_
    """
    headers = {"Content-Type": "application/json"}
    data = json.loads(data)
    data = json.dumps(data)
    try:
        response = http.post(url, headers=headers, data=data, timeout=60)  # 设置超时为60秒
        response.raise_for_status()  # 如果响应状态码不是200，抛出异常
        return response.json()["data"]
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return None


async def async_request_with_retries(url: str, data: str):
    """
    Args:
        url (string): 请求地址
        data (string): 请求数据

    Returns:
        list: 沙盘接口返沪的数据
    """
    headers = {"Content-Type": "application/json"}
    data = json.loads(data)
    data = json.dumps(data)
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, data=data, timeout=60) as response:  # 设置超时为60秒
                response.raise_for_status()  # 如果响应状态码不是200，抛出异常
                json_response = await response.json()
                return json_response["data"]
    except aiohttp.ClientError as e:
        print(f"请求失败: {e}")
        return None


class AsyncApiClient:
    """通用异步API客户端，支持GET和POST请求"""
    
    def __init__(self, base_url: str, auth_token: str = None, timeout: float = 30.0):
        self.base_url = base_url
        self.auth_token = auth_token
        self.timeout = timeout
        self.default_headers = {
            'User-Agent': 'python-requests/2.32.3',
            'Accept-Encoding': 'gzip, deflate',
            'Accept': '*/*',
            'Connection': 'keep-alive'
        }
        if auth_token:
            self.default_headers['Authorization'] = auth_token
    
    async def request(self, 
                     method: str, 
                     endpoint: str, 
                     params: Dict = None, 
                     data: Dict = None, 
                     json_data: Dict = None,
                     headers: Dict = None,
                     result_key: str = None,
                     debug: bool = True) -> Optional[Dict[str, Any]]:
        """
        发送请求并返回解析后的JSON结果
        
        参数:
            method: 请求方法，'GET' 或 'POST'
            endpoint: API端点（相对路径）
            params: URL查询参数（GET请求）
            data: 表单数据（POST请求）
            json_data: JSON数据（POST请求）
            headers: 自定义请求头
            debug: 是否打印调试信息
        
        返回:
            解析后的JSON数据或None（如果请求失败）
        """
        # 构建完整URL
        url = f"{self.base_url}/{endpoint}" if not endpoint.startswith('http') else endpoint
        url = url.rstrip('/')
        
        # 合并请求头
        request_headers = {**self.default_headers}
        if headers:
            request_headers.update(headers)
        
        if debug:
            print(f"请求方法: {method}")
            print(f"请求URL: {url}")
            if params:
                print(f"查询参数: {params}")
            if data:
                print(f"表单数据: {data}")
            if json_data:
                print(f"JSON数据: {json_data}")
            print(f"请求头: {request_headers}")
        
        try:
            # 创建异步客户端
            async with httpx.AsyncClient(
                timeout=self.timeout,
                verify=False,  # 禁用SSL验证，生产环境可能需要启用
                follow_redirects=True  # 允许重定向
            ) as client:
                if debug:
                    print("发送请求...")
                
                # 根据方法发送不同类型的请求
                if method.upper() == 'GET':
                    response = await client.get(url, params=params, headers=request_headers)
                elif method.upper() == 'POST':
                    # 选择使用表单数据还是JSON数据
                    if json_data is not None:
                        response = await client.post(url, params=params, json=json_data, headers=request_headers)
                    else:
                        response = await client.post(url, params=params, data=data, headers=request_headers)
                else:
                    raise ValueError(f"不支持的请求方法: {method}")
                
                if debug:
                    print(f"状态码: {response.status_code}")
                    print(f"响应头: {response.headers}")
                
                # 处理响应
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    # 添加空响应保护
                    if not response.text:
                        if debug:
                            print("响应内容为空")
                        return None
                    if debug:
                        print(f"内容类型: {content_type}")
                        print(f"响应内容前100个字符: {response.text[:100] if response.text else 'N/A'}")
                     
                    # 尝试解析JSON
                    try:
                        json_response = response.json()
                        if debug:
                            print(f"成功解析JSON数据")
                        # 添加result_key存在性检查
                        if result_key:
                            return json_response.get(result_key) 
                        return json_response
                    except json.JSONDecodeError as e:
                        if debug:
                            print(f"JSON解析错误: {e}")
                            if '<html' in response.text.lower():
                                print("服务器返回了HTML而不是JSON")
                                if 'login' in response.text.lower():
                                    print("发现登录页面，可能需要重新认证")
                        
                        # 尝试直接解析文本内容为JSON
                        try:
                            if response.text and response.text.strip():
                                text = response.text.strip()
                                if text.startswith('{') and text.endswith('}'):
                                    return json.loads(text)
                        except:
                            pass
                        
                        return None
                else:
                    if debug:
                        print(f"请求失败，状态码: {response.status_code}")
                    return None
        
        except httpx.RequestError as e:
            if debug:
                print(f"请求错误: {e}")
            return None
        except Exception as e:
            if debug:
                print(f"未知错误: {type(e).__name__}: {e}")
            return None



if __name__ == "__main__":
    # 测试请求
    warroom_url = "https://sandbox-openapi.svw-volkswagen.com/robot/v3/open/adapter/vw/listCompetitionAnalyse"
    data = '{"time": "202401-202401", "start_time": "202401", "end_time": "202401", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["销量"], "display": "趋势", "today": "False", "data_dim": "all", "not_total": "False", "template_id": ""}'
    async def test():
        # response = await async_request(url=warroom_url, data=data, response_key="data")
        async def request_data(data, endpoint):
            client = AsyncApiClient(
                    base_url="http://10.122.31.36:8080",
                    auth_token="NvGW4h-YTGQAFVoQKylHQ5PMIvjCvnk0",
                )
            return await client.request(params=data,endpoint=endpoint,method='GET',debug=False)

        response = await request_data(
                data={"yearMonth": "202301", "queryType": "M"},
                endpoint="/report/mobile/wechat/sv/afterSaleJyController/queryJyIndex")
        print(response)
    import asyncio
    asyncio.run(test())
    # response = request_with_retries(warroom_url, data)
    # if response:
    #     print(response)
    # else:
    #     print("请求未成功")
