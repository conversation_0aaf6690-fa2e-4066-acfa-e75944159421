import csv
from tqdm import tqdm
import os

import sys
sys.path.append('D:/code/gpt/warroom_llm/sx_llm/')
from api import *

parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
file = os.path.join(parent_dir, 'data', 'auto_test_json_extract_data.csv')

result = []


# 打开CSV文件
with open(file, 'r', encoding='utf-8') as f:
    # 创建CSV读取器
    csv_reader = csv.reader(f, delimiter='\t')

    # 跳过标题行
    # headers = next(csv_reader)
    # print(f'列名: {headers}')

    # 逐行读取CSV内容
    for row in tqdm(csv_reader):
        question_type = row[0]
        question = row[1]
        answer = row[2]

        warroom_json = json.loads(format_json)
        if warroom_json["display"] not in ["趋势"] and warroom_json["today"] != "True":
            if warroom_json["display"] == "同环比":
                if "销量" in warroom_json["kpi"] or "市占" in warroom_json["kpi"]:
                    warroom_json["kpi"] = ["上险"]
                else:
                    warroom_json["kpi"] = ["转化"]
            warroom_json["display"] = "分布"
        else:
            warroom_json["display"] = "趋势"
        warroom_json = json.dumps(warroom_json, ensure_ascii=False)
        sx_log.debug(f"client：{self.client} - 用户userId：{self.userId} - 问题：{process_log(self.questionText)} - 日志类型：调用数据查询接口的warroom_json - 日志内容：{process_log(warroom_json)}")


        # 沙盘数据查询和处理
        t1 = time.time()
        response_warroom_no_clean = warroom_post(warroom_json)
        response_warroom = warroom_data_clean(response_warroom_no_clean)