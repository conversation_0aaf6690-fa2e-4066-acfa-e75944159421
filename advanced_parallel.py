import asyncio
import time
from universal_async_client import AsyncApi<PERSON>lient
from typing import List, Dict, Any, Optional

async def fetch_data_with_rate_limit(
    client: AsyncApiClient,
    endpoints: List[Dict[str, Any]], 
    max_concurrent: int = 5
) -> List[Dict[str, Any]]:
    """
    使用信号量控制并发请求数量
    
    参数:
        client: API客户端实例
        endpoints: 请求配置列表，每个元素是一个包含请求参数的字典
        max_concurrent: 最大并发请求数
    """
    # 创建信号量限制并发数
    semaphore = asyncio.Semaphore(max_concurrent)
    results = []
    
    async def fetch_with_limit(config):
        # 使用信号量控制并发
        async with semaphore:
            try:
                method = config.get("method", "GET")
                endpoint = config["endpoint"]
                params = config.get("params")
                data = config.get("data")
                json_data = config.get("json_data")
                
                # 添加请求ID方便识别
                request_id = config.get("id", endpoint)
                print(f"正在请求: {request_id}")
                
                # 发送请求
                start_time = time.time()
                response = await client.request(
                    method=method,
                    endpoint=endpoint,
                    params=params,
                    data=data,
                    json_data=json_data,
                    debug=False
                )
                elapsed = time.time() - start_time
                
                # 构建结果
                result = {
                    "id": request_id,
                    "success": response is not None,
                    "data": response,
                    "elapsed": elapsed,
                    "error": None
                }
                
                # 简单睡眠模拟处理时间
                await asyncio.sleep(0.1)
                return result
            
            except Exception as e:
                # 捕获所有异常并返回错误信息
                return {
                    "id": config.get("id", config.get("endpoint", "unknown")),
                    "success": False,
                    "data": None,
                    "elapsed": 0,
                    "error": str(e)
                }
    
    # 创建所有任务
    tasks = [fetch_with_limit(config) for config in endpoints]
    
    # 并行执行并等待所有结果
    results = await asyncio.gather(*tasks)
    return results

async def process_data_sequentially(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """处理并行请求的结果，返回一个合并后的数据"""
    # 模拟一些数据处理
    processed = {}
    
    for result in results:
        if result["success"]:
            # 成功的请求，处理数据
            request_id = result["id"]
            data = result["data"]
            processed[request_id] = data
            
            # 模拟处理时间
            await asyncio.sleep(0.05)
        else:
            # 失败的请求，记录错误
            print(f"请求 {result['id']} 失败: {result['error']}")
    
    return processed

async def complex_workflow():
    """复杂工作流示例：串行和并行请求的组合"""
    # 创建客户端
    client = AsyncApiClient(
        base_url="http://10.122.31.36:8080/report/mobile/wechat",
        auth_token="SYAAkjENRNIAFVoQKylHQ51nZRNeX12-",
        timeout=15.0  # 较长的超时时间
    )
    
    print("开始执行工作流...")
    total_start = time.time()
    
    # 第一阶段：获取基础数据（串行）
    print("\n第一阶段：获取基础参数...")
    init_data = await client.request(
        method="GET",
        endpoint="sv/afterSaleJyController/queryJyIndex",
        params={"yearMonth": "202301", "queryType": "Y"}
    )
    
    if not init_data:
        print("初始化数据获取失败，终止流程")
        return
    
    print(f"初始化数据获取成功: {init_data}")
    
    # 第二阶段：根据第一阶段结果构建并行请求
    print("\n第二阶段：并行获取多个月份数据...")
    # 从初始数据提取信息（实际中可能需要从第一阶段结果中提取相关参数）
    months = ["202301", "202302", "202303", "202304"]
    
    # 构建请求配置
    requests_config = [
        {
            "id": f"月度数据_{month}",
            "method": "GET",
            "endpoint": "sv/afterSaleJyController/queryJyIndex",
            "params": {"yearMonth": month, "queryType": "M"}
        }
        for month in months
    ]
    
    # 添加一些其他类型的请求
    requests_config.extend([
        {
            "id": "POST请求_1",
            "method": "POST",
            "endpoint": "sv/afterSaleJyController/somePostEndpoint",
            "json_data": {"key": "value"}
        },
        {
            "id": "POST请求_2",
            "method": "POST",
            "endpoint": "sv/afterSaleJyController/anotherEndpoint",
            "data": {"field": "value"}
        }
    ])
    
    # 执行带限流的并行请求
    parallel_start = time.time()
    results = await fetch_data_with_rate_limit(client, requests_config, max_concurrent=3)
    parallel_end = time.time()
    
    print(f"并行请求完成，耗时: {parallel_end - parallel_start:.2f}秒")
    print(f"成功率: {sum(1 for r in results if r['success'])}/{len(results)}")
    
    # 第三阶段：处理结果
    print("\n第三阶段：处理结果...")
    processed_data = await process_data_sequentially(results)
    
    # 第四阶段：根据处理结果执行后续请求
    print("\n第四阶段：最终数据汇总...")
    
    # 假设我们需要根据前面的结果再做一个汇总请求
    summary_data = {
        "results": processed_data,
        "total_requests": len(results),
        "successful_requests": sum(1 for r in results if r['success'])
    }
    
    # 这里可以进行最后的数据处理或请求
    
    total_end = time.time()
    print(f"\n工作流程执行完成，总耗时: {total_end - total_start:.2f}秒")
    return summary_data

# 实现一个全功能的数据收集示例
async def main():
    try:
        # 执行复杂工作流
        result = await complex_workflow()
        
        if result:
            print("\n最终结果汇总:")
            print(f"- 总请求数: {result['total_requests']}")
            print(f"- 成功请求数: {result['successful_requests']}")
            print(f"- 数据项数: {len(result['results'])}")
        
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
    
    print("\n程序执行完毕")

if __name__ == "__main__":
    asyncio.run(main()) 