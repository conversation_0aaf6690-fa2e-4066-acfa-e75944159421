import os
from fastapi import FastAPI, WebSocket
import uvicorn
from langchain.chains import RetrievalQA, LLMChain
from utils.llm import *
from utils.sx_prompt import *
from utils.clean_json import extract_json_from_markdown, clean_json, extract_and_clean_json, extract_and_clean_json_ui, clean_kpi
from utils.sx_index import vector_index
from utils.sx_chart_process import chart_select, content_show
from utils.env_config import *
from utils.sx_dict import *
from utils.sx_date import *
from utils.sx_request import *
import traceback
import copy
import re
import asyncio

import warnings
warnings.filterwarnings('ignore')

import ast

from utils.sx_log import sx_log, process_log



def content_to_str(content):
    """非字符串类型内容转为字符串

    Args:
        content (Any): 任何内容

    Returns:
        String: 内容的字符串形式
    """
    if not isinstance(content, str):
        content = json.dumps(content,ensure_ascii=False)
    return content


def warroom_data_clean(data: list) -> list:
    """清洗沙盘接口返回的异常数据

    Args:
        data (list): 沙盘接口返回的数据

    Returns:
        list: 清洗后沙盘接口返回的数据
    """
    # 不展示未知
    data_clean = [i for i in data if i["location"] != "未知" and i["model"] != "未知" and i["model"] is not None]
    return data_clean


def json_to_list(format_json, kpi):
    """将Json转成List，调用不同的数据

    Args:
        format_json (Dict): 格式化后的JSON
        kpi (List): 查询的指标

    Returns:
        List: 接口调用List
    """
    if kpi == []:
        format_json_list = []
    else:
        if isinstance(format_json, dict):
            format_json_list = []
            tmp_json_list = []
            format_json_copy = copy.deepcopy(format_json)
            del format_json_copy['time']
            for k in kpi:
                tmp_kpi_json_list = []
                if format_json['time'] == []:
                    format_json['time'] = [' - ']
                for t in format_json['time']:
                    if '-' not in t:
                        t = t + '-' + t
                    format_json_copy['start_time'],format_json_copy['end_time'] = t.split('-')
                    format_json_clean = clean_json(format_json_copy,[k])
                    if format_json_clean == '{}' or format_json_clean in tmp_json_list:
                        continue
                    tmp_json_list.append(format_json_clean)
                    tmp_kpi_json_list.append({'json_origin':copy.deepcopy(format_json_copy),'json_clean':format_json_clean})
                if tmp_kpi_json_list != []:
                    k_display = json.loads(tmp_kpi_json_list[0]['json_clean'])['display']
                    k_today = json.loads(tmp_kpi_json_list[0]['json_clean'])['today']
                    format_json_list.append({'kpi':k,'display':k_display,'today':k_today,'json_list':tmp_kpi_json_list})
        else:
            format_json_list = []
    return format_json_list



def answer_warroom_compare(time_list,kpi,language):
    """根据语言和问题意图输出文字回复

    Args:
        kpi (List): 数据查询指标
        language (String): 问题的语言

    Returns:
        answer (String): 数据查询的文字回复
    """
    t_list = []
    for i in time_list:
        if answer_date(i[2],i[0],language) == answer_date(i[2],i[1],language):
            t_list.append(answer_date(i[2],i[0],language))
        else:
            t_list.append(answer_date(i[2],i[0],language)+ sx_date_name['to'][language] + answer_date(i[2],i[1],language))

    time_str = ' & '.join(t_list)

    if kpi in ["销量","市占"]:
        intention = sx_answer_source[language]["上险"]
    else:
        intention = sx_answer_source[language]["转化"]
    answer = time_str + intention
    return answer


def answer_warroom(format_json,kpi,language):
    """根据语言和问题意图输出文字回复

    Args:
        format_json (String): 进行数据接口查询的json
        kpi (String): 数据查询指标
        language (String): 问题的语言

    Returns:
        answer (String): 数据查询的文字回复
    """
    format_json = json.loads(format_json)
    today = format_json["today"]
    start_time = format_json["start_time"]
    end_time = format_json["end_time"]
    date_type = format_json["date_type"]
    if today == "True":
        answer = sx_warroom_reply["实时数据"][language].format(time.strftime('%Y-%m-%d %H:%M:%S',time.localtime()))
    else:
        if kpi in ["销量","市占","上险"]:
            intention = sx_answer_source[language]["上险"]
        else:
            intention = sx_answer_source[language]["转化"]
        if start_time == end_time:
            answer = answer_date(date_type,start_time,language) + ' ' + intention
        else:
            answer = sx_date_name['from'][language] + answer_date(date_type,start_time,language)+ sx_date_name['to'][language] + answer_date(date_type,end_time,language) + intention
    return answer


def warroom_json_generate(format_json):
    """将清洗后的Json转为可以进行数据查询的Json，以便查询想要的数据

    Args:
        format_json (String): 清洗后的Json

    Returns:
        String: 进行沙盘数据查询的Json
    """
    # 接口json转化
    warroom_json = json.loads(format_json)
    # 除了今日或者趋势查询，其余都使用分布进行统计数据查询
    if warroom_json["display"] in ["趋势"] or (warroom_json["today"] == "True" and warroom_json["kpi"] == ['转化'] and warroom_json["data_dim"] == "all"):
        warroom_json["display"] = "趋势"
    else:
        if warroom_json["display"] == "同环比":
            if "销量" in warroom_json["kpi"] or "市占" in warroom_json["kpi"]:
                warroom_json["kpi"] = ["上险"]
            elif "批售数" in warroom_json["kpi"] or "含大客户发票数" in warroom_json["kpi"] or "日均批售数" in warroom_json["kpi"] or "日均含大客户发票数" in warroom_json["kpi"]:
                warroom_json["kpi"] = ["批售"]
            elif "总库存" in warroom_json["kpi"] or "总部库存" in warroom_json["kpi"]:
                warroom_json["kpi"] = ["库存"]
            else:
                warroom_json["kpi"] = ["转化"]
        warroom_json["display"] = "分布"
    warroom_json = json.dumps(warroom_json, ensure_ascii=False)
    return warroom_json


app = FastAPI()
# todo：后续增加两个prompt，使用不同llm调用不同prompt
llm = llm_dict["wenxin"]
# llm_qa = llm_dict["wenxinchat"]
llm_qa = llm_dict["wenxinstream"]


class ChatWarroomAssistant:
    """沙盘对话助手
    """
    def __init__(self):
        self.websocket = None
        self.input_json = None
        self.userId = None
        self.sessionId = None
        self.questionText = None
        self.category = None
        self.client = None
        self.language = '英文'
        self.input_text = None
        self.intention = None
        self.kpi = None
        self.t_input = None
        self.history_json = None
        # 历史问题
        self.history_question = ""
        # 意图判断变量，记录上次用户查询意图：如果上次用户查询意图与本次相同，则不进行意图判断。1：不相同；0：相同
        self.memory_intention_judge = 1
        # 历史对话
        self.history_chat = [{"role": "user", "content": "你好"},{"role": "assistant", "content": "我是上汽大众（SVW）Copilot，有什么能帮您的吗？"}]
        # 对话轮数
        self.turn = 1
        # 分布数据是是否为汇总值：'False'，汇总值；'True'，非汇总值
        self.not_total = 'False'
        # 获取日期相关信息
        self.date_info = get_date_info()

    # 接收ws输入
    async def ws_receive(self, websocket):
        self.input_json = await websocket.receive_json()
        self.t_input = time.time()
        self.userId = self.input_json["userId"]
        self.sessionId = self.input_json["sessionId"]
        self.questionText = self.input_json["questionText"]
        self.category = self.input_json.get("category", "")
        self.client = self.input_json.get("client", "")
        self.save_log('输入',self.questionText)


    def save_log(self,log_type,log_content,time_cost=0):
        """记录日志

        Args:
            log_type (String): 日志类型
            log_content (Any): 日志记录内容
        """
        log_content = process_log(log_content)
        sx_log.debug(f"client：{self.client} - 用户userId：{self.userId} - 问题：{process_log(self.questionText)} - 日志类型：{log_type} - 日志内容：{log_content} - 处理时间：{time_cost}")

    def history_process(self, content):
        """历史对话处理，仅保留上一次问答历史

        Args:
            content (String): 待记录的历史对话内容
        """
        content = content_to_str(content)
        if len(self.history_chat) in [2,4]:
            self.history_chat.append({"role": "user", "content": content})
        elif len(self.history_chat) in [3,5]:
            self.history_chat.append({"role": "assistant", "content": content})
        else:
            self.history_chat = self.history_chat[0:2] + self.history_chat[4:6]
            self.history_chat.append({"role": "user", "content": content})

    def input_process(self, llm):
        """输入预处理：语言识别，问题完善，意图识别，指标提取

        Args:
            llm (Object): 大语言模型对象
        
        Returns:
            history_question (String)：结合上一个输入的问题（已进行问题完善），完善的当前的问题，替换历史问题
        """
        chain_language_question_intention = LLMChain(llm=llm, prompt=prompt_language_question_intention_kpi)
        t1 = time.time()
        response_language_question_intention = chain_language_question_intention.run(current_question=self.questionText,kpi_all=kpi_all).replace("。","")
        t2 = time.time()
        self.save_log('意图识别结果',response_language_question_intention,t2-t1)

        # 处理返回值并进行初步清洗，只取前三行，防止有多余的描述
        response_parts = [line for line in response_language_question_intention.split('\n') if line]
        self.language = response_parts[0].split('：')[1]
        self.intention = response_parts[1].split('：')[1]
        self.kpi = response_parts[2].split('：')[1]

        # 非中文语言都转为英文
        self.language = "英文" if self.language != "中文" else self.language

        # 新增对问题中by SVR的处理，防止无法按照地区维度查询
        self.questionText = self.questionText.replace('by SVR','by region').replace('by svr','by region')

        # 保存历史问题
        self.history_question = self.questionText

        # 问题中的 同比/环比 转化为同环比，防止出现 同比/环比时，后续时间提取不准
        self.input_text = self.questionText.replace('同比','同环比').replace('环比','同环比').replace('同同','同')

        # kpi清洗
        # LLM没有按格式提取kpi，数据非list结构，字符串包含、
        self.kpi = ast.literal_eval(self.kpi) if "[" in self.kpi else self.kpi.split("、")
        self.kpi = clean_kpi(self.kpi)
        self.save_log('清洗后的指标',self.kpi)
        
        # 清洗后超过5个kpi，则转为未知意图查询
        if len(self.kpi) > 5:
            self.intention = '未知意图'

        # 如果为"数据查询"或"数据分析"，且清洗后指标为空，则意图变量转为0
        if (self.kpi == [] or self.kpi == [""]) and (self.intention in "数据查询"):
            self.memory_intention_judge = 1 - self.memory_intention_judge


    async def ws_send_message(self, response, is_finish=False):
        """给前端发送消息

        Args:
            response (List): 查询结果
            is_finish (Bool): 是否最后一条消息
        """
        await self.websocket.send_json({"answerText": response,"turn": self.turn,"answerType": "result","finished": is_finish})


    async def ws_return_response_stream(self, question):
        """流式返回

        Args:
            question (List): 含历史对话的问题

        Returns:
            String: 回复的完整内容
        """
        qa_result = llm_qa.chat_by_token(question)
        tmp_result = ''
        answer_src = sx_answer_source[self.language]["LLM"]
        for line in qa_result.iter_lines():
            try:
                # received_message = await self.websocket.receive_json(timeout=0.1)
                received_message = await asyncio.wait_for(self.websocket.receive_json(), timeout=0.1)
                if received_message["longTextStopFlag"]:
                    break
            except asyncio.TimeoutError:
                tmp_line_str = line.decode("UTF-8")
                if tmp_line_str != '':
                    if 'Prompt tokens too long' in tmp_line_str or 'the max input characters' in tmp_line_str:
                        response = [{"type": "longText","longText": {"content": llm_error_reply[self.language],"ended": False}}]
                        tmp_result = tmp_result + llm_error_reply[self.language]
                        await self.ws_send_message(response,False)
                        break
                    result_match = re.search(r'"result":"(.*?)","need_clear_history"', tmp_line_str, re.DOTALL)
                    if result_match:
                        tmp_result_value = result_match.group(1)
                        tmp_result_value = tmp_result_value.replace('\\n','\n').replace('\\t','\t').replace('\\"','\"')
                    else:
                        tmp_result_value = ''
                    tmp_result = tmp_result + tmp_result_value
                    response = [{"type": "longText","longText": {"content": tmp_result_value,"ended": False}}]
                    await self.ws_send_message(response,False)
        response = [{"type": "longText","longText": {"content": answer_src,"ended": True}}]
        await self.ws_send_message(response,True)
        tmp_result = tmp_result + answer_src
        return tmp_result


    async def ws_return_intention(self):
        """返回问题的意图"""

        answerText = sx_intention_reply[self.language][self.intention]
        answerText = [{"type": "text","text": {"content": answerText}}]
        await self.websocket.send_json({"answerText": answerText,"turn": self.turn,"answerType": "message","finished": False})
        t2 = time.time()
        self.save_log('给用户返回的意图',answerText,t2-self.t_input)

    async def intent_act_data(self):
        """数据查询意图处理

        Returns:
            response (List): 回复内容
        """
        # 关键词提取
        chain_warroom = LLMChain(llm=llm, prompt=prompt_extract_args)
        t1 = time.time()
        response_json = chain_warroom.run(today=self.date_info['today_date_cn'], question=self.questionText, 
                                          weekday=self.date_info['today_weekday'], kpi_all=kpi_all,
                                          this_year_first_day=self.date_info['this_year_first_day_date_str'], 
                                          this_year_last_day=self.date_info['this_year_last_day_date_str'])
        t2 = time.time()
        self.save_log('关键词提取json',response_json,t2-t1)

        t1 = time.time()
        # json格式化
        format_json = extract_json_from_markdown(response_json)
        # json清洗并转为list
        format_json_list = json_to_list(format_json, self.kpi)
        t2 = time.time()
        self.save_log('格式化后的Json',format_json,t2-t1)
        self.save_log('清洗后的Json列表',format_json_list,t2-t1)

        # 初始化是否最后回复变量
        is_finish = False

        if format_json_list == []:
            if self.intention == '数据查询':
                if self.language == "英文":
                    question = "请用英语回答问题：" + self.input_text
                else:
                    question = self.input_text

                parent_dir = os.path.dirname(os.path.abspath(__file__))
                file_path = os.path.join(parent_dir, 'vector_store', 'knowledge_index')
                # 知识库查询
                context = vector_index(file_path,self.input_text,top_k=5)[1]
                tmp_prompt_knowledge_search = prompt_knowledge_search.format(question=question, context=context)
                # 记录当前问题
                self.history_process(tmp_prompt_knowledge_search)
                t1 = time.time()
                tmp_result = await self.ws_return_response_stream(self.history_chat)
                t2 = time.time()
                # 对话历史记录当前回答
                self.history_process(tmp_result)
                self.save_log('json为空的历史对话记录（含当前回答）',tmp_result,t2-t1)
                response = [{"type": "text","text": {"content": tmp_result}}]
            else:
                response = []
            return response

        response = []
        return_num = 1
        for i in format_json_list:
            if return_num == len(format_json_list) and self.intention == '数据查询':
                is_finish = True
            else:
                return_num = return_num + 1

            tmp_kpi = i['kpi']
            tmp_display = i['display']
            tmp_today = i['today']

            response_kpi = []

            # 图表展示选择
            chart_type,type = chart_select(tmp_display)
            self.save_log('数据的图表类型chart_type和type',chart_type + ' ' + ' '.join(type))

            # 对比图放在一张图展示
            if tmp_display in ['对比','排名'] or (tmp_display == '漏斗' and tmp_today != "True"):
                json_show = []
                warroom_data_list = []
                time_list = []
                for j in i['json_list']:
                    tmp_json_clean = j['json_clean']
                    tmp_kpi = json.loads(tmp_json_clean)['kpi'][0]

                    # 沙盘查询json处理
                    t1 = time.time()
                    # 对比情况不需要转化，可以去掉？
                    warroom_json = warroom_json_generate(tmp_json_clean)
                    t2= time.time()
                    self.save_log('调用数据查询接口的warroom_json',warroom_json,t2-t1)

                    # 沙盘数据查询和处理
                    t1 = time.time()
                    response_warroom_no_clean = request_with_retries(warroom_url, warroom_json)
                    t2 = time.time()
                    self.save_log('数据查询接口返回response_warroom_no_clean',response_warroom_no_clean,t2-t1)

                    if response_warroom_no_clean == []:
                        continue
                    # 清洗沙盘接口查询的数据
                    t1 = time.time()
                    response_warroom = warroom_data_clean(response_warroom_no_clean)
                    t2 = time.time()
                    self.save_log('清洗后的数据查询接口返回response_warroom',response_warroom,t2-t1)


                    # 沙盘数据只保留汇总值
                    for tmp_data in response_warroom:
                        if tmp_data['time'] == 'total':
                            if json.loads(tmp_json_clean)['start_time'] == json.loads(tmp_json_clean)['end_time']:
                                tmp_data['time'] = json.loads(tmp_json_clean)['start_time']
                            else:
                                tmp_data['time'] = json.loads(tmp_json_clean)['start_time'] + '-' + json.loads(tmp_json_clean)['end_time']
                            tmp_time_list = [json.loads(tmp_json_clean)['start_time'],json.loads(tmp_json_clean)['end_time'],json.loads(tmp_json_clean)['date_type']]
                            if tmp_time_list not in time_list:
                                time_list.append(tmp_time_list)
                            warroom_data_list.append(tmp_data)
                
                
                self.save_log('汇总后的数据查询结果warroom_data_list',warroom_data_list)
                if warroom_data_list == []:
                    qa_answer = sx_warroom_response_reply[self.language].format(sx_kpi_name[tmp_kpi][self.language])
                    text_show = [{"type": "text","text": {"content": qa_answer}}]
                    await self.ws_send_message(text_show,is_finish)
                else:
                    # 输出数据格式转化
                    json_show = content_show(tmp_json_clean,warroom_data_list,chart_type,type,self.language)

                    if json_show == []:
                        qa_answer = sx_warroom_response_reply[self.language].format(sx_kpi_name[tmp_kpi][self.language])
                        text_show = [{"type": "text","text": {"content": qa_answer}}]
                        await self.ws_send_message(text_show,is_finish)
                    else:
                        qa_answer = answer_warroom_compare(time_list,tmp_kpi,self.language)
                        text_show = [{"type": "text","text": {"content": qa_answer}}]
                        await self.ws_send_message(text_show + json_show,is_finish)
                        response = response + text_show + json_show

            else:
                tmp_count = 1
                for j in i['json_list']:
                    if tmp_count == len(i['json_list']) and is_finish == True:
                        tmp_is_finish = True
                    else:
                        tmp_is_finish = False
                        tmp_count = tmp_count + 1
                    tmp_json_clean = j['json_clean']
                    # 如果存在多个指标，只保留数值类指标作为返回话术的指标显示
                    if len(json.loads(tmp_json_clean)['kpi']) > 1:
                        tmp_kpi = [tmp for tmp in json.loads(tmp_json_clean)['kpi'] if tmp not in not_main_kpi][0]
                    else:
                        tmp_kpi = json.loads(tmp_json_clean)['kpi'][0]
                    t1 = time.time()
                    warroom_json = warroom_json_generate(tmp_json_clean)
                    t2= time.time()
                    self.save_log('调用数据查询接口的warroom_json',warroom_json,t2-t1)

                    # 沙盘数据查询和处理
                    t1 = time.time()
                    response_warroom_no_clean = request_with_retries(warroom_url, warroom_json)
                    t2 = time.time()
                    self.save_log('数据查询接口返回response_warroom_no_clean',response_warroom_no_clean,t2-t1)

                    if response_warroom_no_clean == []:
                        continue
                    else:
                        # 清洗沙盘接口查询的数据
                        t1 = time.time()
                        response_warroom = warroom_data_clean(response_warroom_no_clean)
                        t2 = time.time()
                        self.save_log('清洗后的数据查询接口返回response_warroom',response_warroom,t2-t1)
                        if response_warroom == []:
                            continue

                        # 输出数据格式转化
                        json_show = content_show(tmp_json_clean,response_warroom,chart_type,type,self.language)
                        # 如果查询数据为空
                        if json_show == []:
                            continue
                        # 如果查询不为空
                        else:
                            qa_answer = answer_warroom(tmp_json_clean,tmp_kpi,self.language)
                            text_show = [{"type": "text","text": {"content": qa_answer}}]
                            await self.ws_send_message(text_show + json_show,tmp_is_finish)
                            response_kpi = response_kpi + text_show + json_show
                            response = response + text_show + json_show

                # 如果某个指标都查不到数据，就返回默认话术
                if response_kpi == []:
                    qa_answer = sx_warroom_response_reply[self.language].format(sx_kpi_name[tmp_kpi][self.language])
                    text_show = [{"type": "text","text": {"content": qa_answer}}]
                    await self.ws_send_message(text_show,is_finish)
        
        if response != []:
            # 对话历史记录当前问题
            self.history_process(self.input_text)
            # 对话历史记录当前回答
            self.history_process(response)

        return response


    async def intent_act_data_analysis(self, response):
        # 提取当前显示数据
        response_data = [i for i in response if i["type"] != "text"]
        if self.language == "英文":
            question = "请用英语回答问题：" + self.questionText
        else:
            question = self.questionText
        tmp_prompt_data_analysis = prompt_data_analysis.format(question=question, data=response_data, today=self.date_info['today_date_cn'])
        self.history_process(tmp_prompt_data_analysis)
        t1 = time.time()
        tmp_result = await self.ws_return_response_stream(self.history_chat)
        t2 = time.time()
        self.history_process(tmp_result)
        self.save_log('数据分析结果',tmp_result,t2-t1)
        response = [{"type": "text","text": {"content": tmp_result}}]
        self.save_log('数据分析最终结果',response,t2-t1)
        return response


    async def intent_act_kb(self):
        if self.language == "英文":
            question = "请用英语回答问题：" + self.input_text
        else:
            question = self.input_text
        parent_dir = os.path.dirname(os.path.abspath(__file__))
        file_path = os.path.join(parent_dir, 'vector_store', 'knowledge_index')
        # 知识库查询
        docsearch,knowledge_index = vector_index(file_path,self.input_text,top_k=5)
        knowledge_chain = RetrievalQA.from_llm(llm=llm,retriever=docsearch,prompt=prompt_knowledge_search)
        knowledge_chain.return_source_documents = True
        search_result = knowledge_chain({"query": question})["result"]
        response = [{"type": "text","text": {"content": search_result + sx_answer_source[self.language]["知识库"]}}]
        await self.ws_send_message(response,True)
        return response


    async def intent_act_ui(self):
        #调用UI交互接口
        chain_time_filter = LLMChain(llm=llm, prompt=prompt_ui)
        response_json = chain_time_filter.run(today=self.date_info['today_date_cn'], question=self.input_text)
        formatted_json = extract_and_clean_json_ui(response_json)
        if formatted_json != '{}':
            formatted_json = json.loads(formatted_json)
            action = formatted_json["action"]
            if action == "筛选":
                start_time = formatted_json["start_time"]
                end_time = formatted_json["end_time"]
                response = [{"type": "command","command": {"command_type": "time_filter","start_time": start_time,"end_time": end_time}}]
            else:
                response = [{"type": "command","command": {"command_type": "back_to_home"}}]
                
        else:
            response = [{"type": "text","text": {"content": "Can't respond to the question '" + self.input_text + "'"}}]
        await self.ws_send_message(response,True)
        return response


    # 一般问答处理
    async def intent_act_qa(self):

        if self.language == "英文":
            question = "请用英语回答问题：" + self.input_text
        else:
            question = self.input_text
        self.save_log('一般QA问答的问题',question)
        parent_dir = os.path.dirname(os.path.abspath(__file__))
        file_path = os.path.join(parent_dir, 'vector_store', 'knowledge_index')
        # 知识库查询
        context = vector_index(file_path,self.input_text,top_k=5)[1]
        tmp_prompt_knowledge_search = prompt_knowledge_search.format(question=question, context=context)
        # 记录当前问题
        self.history_process(tmp_prompt_knowledge_search)
        t1 = time.time()
        tmp_result = await self.ws_return_response_stream(self.history_chat)
        t2 = time.time()
        #记录当前问题回复
        self.history_process(tmp_result)
        self.save_log('一般QA问答的当前回答',tmp_result,t2-t1)
        response = [{"type": "text","text": {"content": tmp_result}}]
        self.save_log('一般QA问答的历史对话（含当前回答）',self.history_chat,t2-t1)
        return response


    async def intent_act(self):
        """根据不同意图进行回答

        Returns:
            response (List): 问题的回答
        """
        if "数据查询" in self.intention:
            response = await self.intent_act_data()
        elif "数据分析" in self.intention:
            response = await self.intent_act_data()
            await self.intent_act_data_analysis(response)
        elif "知识库查询" in self.intention:
            response = await self.intent_act_kb()
        elif "UI交互" in self.intention:
            response = await self.intent_act_ui()
        else:
            response = await self.intent_act_qa()
        t_result = time.time()
        self.save_log('问题最终回答',response,t_result - self.t_input)






@app.websocket("/api/V1/chat/ws")
async def stream_chat(websocket: WebSocket):
    """提供给沙盘的ws接口，用于进行问题回复

    Args:
        websocket (WebSocket): websocket连接
    """


    # 初始话一次对话实例
    chat_ass = ChatWarroomAssistant()
    chat_ass.websocket = websocket
    # 建立ws连接
    await websocket.accept()
    await chat_ass.ws_send_message("我是AI大模型助手，有什么能帮您的吗？")
    while True:
        try:
            # 解析前端输入
            await chat_ass.ws_receive(websocket)
            # ws输入处理
            chat_ass.input_process(llm)
            # 如果意图变量为0，则不进行后续处理，结束单次对话，让用户输入查询指标
            # TODO：每次查询均已判断意图，因此意图变量memory_intention_judge的判断不会简化查询流程，后续可考虑优化
            if chat_ass.memory_intention_judge == 0:
                chat_ass.memory_intention_judge = 1 - chat_ass.memory_intention_judge
                content = sx_guide_reply[chat_ass.language]
                response = [{"type": "text","text": {"content": content}}]
                await chat_ass.ws_send_message(response,True)
            else:
                # 返回问题类型
                await chat_ass.ws_return_intention()
                # 返回最终答案
                await chat_ass.intent_act()

        # 捕获异常，返回异常回复
        except Exception as e:
            t_result = time.time()
            e_msg = traceback.format_exc()
            chat_ass.save_log('异常捕获',e_msg,t_result - chat_ass.t_input)
            answerText = [{"type": "text","text": {"content": error_reply[chat_ass.language]}}]
            await chat_ass.ws_send_message(answerText,True)
        chat_ass.turn += 1



if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, workers=1, ws_ping_interval=15, ws_ping_timeout=5, log_level="debug")