import asyncio
import json
import traceback
from typing import Optional
from contextlib import AsyncExitStack
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from openai import OpenAI

from utils.sx_date import get_date_info
from utils.sx_log import save_process_log
from utils.config import VOLCENGINE_WARROOM_API_KEY, VOLCENGINE_PROXY_URL, VOLCENGINE_DEEPSEEK_V3_0324_END_POINT

class MCPClient:
    def __init__(self, token: str = None):
        # Initialize session and client objects
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.client = OpenAI(
            api_key=VOLCENGINE_WARROOM_API_KEY,
            base_url=f"{VOLCENGINE_PROXY_URL}/api/v3"
        )
        self.model = VOLCENGINE_DEEPSEEK_V3_0324_END_POINT
        self.date_info = get_date_info()
        self.today = self.date_info['today_date_cn']
        self.messages = [
            {
                "role": "system",
                "content": f"你是一个人工智能助手，你的任务是根据用户的问题，选择合适的工具并调用，获取足够的信息，直到不需要使用工具时退出循环。\n#要求\n1.当前日期是{self.today}，用户问题中的时间请以当前日期进行转换。\n2.不要查询未来时间的数据。\n3.如果调用工具报错，请再仔细检查应该使用的工具和工具参数，重新调用工具。"
            }
        ]
        self.available_tools = []
        self.token = token
    
    @staticmethod
    def convert_custom_object(obj):
        """
        将自定义对象转换为字典
        """
        if hasattr(obj, "__dict__"):  # 如果对象有 __dict__ 属性，直接使用
            return obj.__dict__
        elif isinstance(obj, (list, tuple)):  # 如果是列表或元组，递归处理
            return [MCPClient.convert_custom_object(item) for item in obj]
        elif isinstance(obj, dict):  # 如果是字典，递归处理值
            return {key: MCPClient.convert_custom_object(value) for key, value in obj.items()}
        else:  # 其他类型（如字符串、数字等）直接返回
            return obj
        
    async def connect_to_server(self):
        """Connect to an MCP server
        
        Args:
            server_script_path: Path to the server script (.py or .js)
        """
        
        import os
        parent_dir = os.path.dirname(os.path.abspath(__file__))
        server_script_path = os.path.join(parent_dir, 'aftersales_data_mcp_server.py')
        
        is_python = server_script_path.endswith('.py')
        is_js = server_script_path.endswith('.js')
        if not (is_python or is_js):
            raise ValueError("Server script must be a .py or .js file")
            
        command = "python" if is_python else "node"
        server_params = StdioServerParameters(
            command=command,
            args=[server_script_path],
            env=None
        )
        
        stdio_transport = await self.exit_stack.enter_async_context(stdio_client(server_params))
        self.stdio, self.write = stdio_transport
        self.session = await self.exit_stack.enter_async_context(ClientSession(self.stdio, self.write))
        
        await self.session.initialize()
        
        # List available tools
        response = await self.session.list_tools()
        tools = response.tools
        print("\nConnected to server with tools:", [tool.name for tool in tools])


    async def process_query(self, query: str) -> str:
        """Process a query with multi-turn tool calling support"""
        # Add user query to message history
        self.messages.append({
            "role": "user",
            "content": query
        })
        # Get available tools if not already set
        if not self.available_tools:
            response = await self.session.list_tools()
            self.available_tools = [{
                "type": "function",
                "function": {
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": tool.inputSchema
                }
            } for tool in response.tools]
        current_response = self.client.chat.completions.create(
            model=self.model,
            messages=self.messages,
            tools=self.available_tools,
            stream=False,
            temperature=0.1
        )
        
        # Print initial response if exists
        if current_response.choices[0].message.content:
            print("\n  AI:", current_response.choices[0].message.content)
        
        data = []
        
        # 直到下一次交互 AI 没有选择调用工具时退出循环
        while current_response.choices[0].message.tool_calls:
            # AI 一次交互中可能会调用多个工具
            for tool_call in current_response.choices[0].message.tool_calls:
                tool_name = tool_call.function.name
                try:
                    tool_args = json.loads(tool_call.function.arguments)
                except json.JSONDecodeError:
                    tool_args = {}
                print(f"\n  调用工具 {tool_name}")
                save_process_log("调用工具", tool_name)
                print(f"  参数: {tool_args}")
                save_process_log("参数", tool_args)
                
                tool_args["token"] = self.token
                
                # Execute tool call
                result = await self.session.call_tool(tool_name, tool_args)
                print(f"\n工具结果: {result}")
                save_process_log("工具结果", result)
                # Add AI message and tool result to history
                self.messages.append(current_response.choices[0].message)
                
                self.messages.append({
                    "role": "tool",
                    "tool_call_id": tool_call.id,
                    "content": '\n'.join([content.text for content in result.content])
                })
                if tool_name not in ['get_region_code','get_province_code','get_agency_code','get_business_manager_code','get_city_code','get_dealer_code']:
                    tmp_data = [json.loads(content.text) if 'Error' not in content.text else content.text for content in result.content]
                    tmp_data.append({k: v for k,v in tool_args.items() if k != 'token'})
                    data.append(tmp_data)
            # Get next response
            current_response = self.client.chat.completions.create(
                model=self.model,
                messages=self.messages,
                tools=self.available_tools,
                stream=False
            )

        # 关闭session链接，避免异常退出报错
        await self.exit_stack.aclose()
        return data

    async def chat_loop(self):
        """Run an interactive chat loop"""
        print("\nMCP Client Started!")
        print("Type your queries or 'quit' to exit.")
        
        while True:
            try:
                query = input("\nCommend: ").strip()
                
                if query.lower() == 'quit':
                    break
                    
                response = await self.process_query(query)
                print("\n AI: ", response)
                    
            except Exception as e:
                print(f"\nError occurs: {e}")
                traceback.print_exc()
    
    async def cleanup(self):
        """Clean up resources"""
        await self.exit_stack.aclose()


async def main():
        
    client = MCPClient()
    try:
        await client.connect_to_server()
        await client.chat_loop()
    finally:
        await client.cleanup()


if __name__ == "__main__":
    asyncio.run(main())