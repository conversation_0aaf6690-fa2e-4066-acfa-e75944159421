


# import re

# def extract_digits(text):
#     """提取字符串中的数值部分

#     Args:
#         text (String): 需要提取数值的字符串

#     Returns:
#         String: 字符串中的数值部分
#     """
#     if not text:
#         return ''
#     digits = re.findall(r'\d+', text)
#     return digits[0] if digits else ''


# def clean_time(t):
#     today_date_str = '20240312'
#     t_digits = extract_digits(t)
#     if not t_digits or len(t_digits) != 8 or not t_digits.isdigit():
#         t_digits = today_date_str

#     if t_digits > today_date_str:
#         t_digits = today_date_str

#     return t_digits


# def clean_time_list(t_list):
#     if not t_list:
#         clean_t_list = [clean_time('')+'-'+clean_time('')]
#     else:
#         if isinstance(t_list, list):
#             clean_t_list = list(set([[clean_time(i.split('-')[0]),clean_time(i.split('-')[1])] if clean_time(i.split('-')[1]) > clean_time(i.split('-')[0]) 
#                             else [clean_time(i.split('-')[0]),clean_time(i.split('-')[0])] for i in t_list]))
#         elif isinstance(t_list, str):
#             clean_t_list = list(set([clean_time(t_list.split('-')[0]),clean_time(t_list.split('-')[1])] if clean_time(t_list.split('-')[1]) > clean_time(t_list.split('-')[0]) else [clean_time(t_list.split('-')[0]),clean_time(t_list.split('-')[0])]))
#         else:
#             clean_t_list = [clean_time('')+'-'+clean_time('')]
#     return clean_t_list



# t1 = ['20230131-********','********-20240131']
# t2 = ['20230131-********']
# t3 = '********-20240131'
# t4 = ['********-20230131','********-20240131']
# t5 = ['********-20230131']
# t6 = '********-20230131'

# print(t1,clean_time_list(t1))
# print(t2,clean_time_list(t2))
# print(t3,clean_time_list(t3))
# print(t4,clean_time_list(t4))
# print(t5,clean_time_list(t5))
# print(t6,clean_time_list(t6))











# import json, copy
# def warroom_json_generate(format_json):
#     # 接口json转化
#     warroom_json = json.loads(format_json)
#     # 除了今日或者趋势查询，其余都使用分布进行统计数据查询
#     if warroom_json["display"] not in ["趋势"] and warroom_json["today"] != "True":
#         if warroom_json["display"] == "同环比":
#             tmp_kpi = []
#             if "销量" in warroom_json["kpi"] or "市占" in warroom_json["kpi"]:
#                 tmp_kpi.append("上险")
#             if "批售数" in warroom_json["kpi"] or "含大客户发票数" in warroom_json["kpi"]:
#                 tmp_kpi.append("批售")
#             if "库存" in warroom_json["kpi"]:
#                 tmp_kpi.append("库存")
#             if len(set(warroom_json["kpi"]) - set(["销量","市占","批售数","含大客户发票数","库存","上险"])) > 0:
#                 tmp_kpi.append("转化")
#             warroom_json["kpi"] = tmp_kpi
#         warroom_json["display"] = "分布"
#     else:
#         warroom_json["display"] = "趋势"
#     warroom_json_copy = copy.deepcopy(warroom_json)
#     del warroom_json_copy['time']
#     warroom_json_list = []
#     for i in warroom_json['time']:
#         warroom_json_copy['start_time'] = i[0]
#         warroom_json_copy['end_time'] = i[1]
#         warroom_json_list.append(json.dumps(warroom_json_copy, ensure_ascii=False))
#     return warroom_json_list

# format_json1 = '{"time": [[""], "date_type": "month", "model": ["Passat"], "location": ["Nationwide"], "kpi": ["转化"], "display": "趋势", "today": "False", "data_dim": "all"}'






# import os
# import pandas as pd

# parent_dir = os.path.dirname(os.path.abspath(__file__))

# file_path_city = os.path.join(parent_dir, 'data', 'location_city_name.txt')
# file_path_province = os.path.join(parent_dir, 'data', 'location_province_name.txt')
# file_path_rssc = os.path.join(parent_dir, 'data', 'location_rssc_name.txt')
# data_city = pd.read_csv(file_path_city,header=None)[0].tolist()
# data_province = pd.read_csv(file_path_province,header=None)[0].tolist()
# data_rssc = pd.read_csv(file_path_rssc,header=None,sep='\t')[0].tolist()
# # mappings_rssc = {j[0]:j[1] for i,j in data_rssc.iterrows()}
# # mappings_province = {j[0]:j[1] for i,j in data_province.iterrows()}
# # mappings_city = {j[0]:j[1] for i,j in data_city.iterrows()}



# # 地理位置清洗，判断地理位置维表是否存在于location中
# # 依次从城市、省份、大区的顺序进行匹配，匹配不到，则匹配下一级；否则停止匹配
# # 全国、各大区、各省份、各城市等关键词需要进行转换
# # 默认为Nationwide（全国）
# def clean_location(location):
#     if location == [''] or location == []:
#         return ["Nationwide"]
#     else:
#         location_map = []
#         if '全国' in location or 'Nationwide' in location:
#             location_map.append('Nationwide')
#         [location_map.append(i) for i in location if i in data_city]
#         [location_map.append(i) for i in location if i in data_province]
#         [location_map.append(j) for i in location for j in data_rssc if j in i.replace('大区','')]
#         if location_map == []:
#             location_map = ["Nationwide"]
#     return location_map


# location1 = ['全国']
# location2 = ['全国','吉林省']
# location3 = ['吉林市','吉林省']
# location4 = ['全国','华中','华北大区']

# print(location1,clean_location(location1))
# print(location2,clean_location(location2))
# print(location3,clean_location(location3))
# print(location4,clean_location(location4))



# from utils.clean_json import extract_json_from_markdown, clean_json, extract_and_clean_json, extract_and_clean_json_ui, clean_kpi
# import copy, json

# format_json = {'time': ['20230101-20230131', '20230301-20230331'], 'date_type': 'month', 'location': ['全国'], 'model': [], 'display': '对比', 'data_dim': 'all', 'today': 'False'}
# kpi = ["发票数"]


# def json_to_list(format_json, kpi):
#     """将Json转成List，调用不同的数据

#     Args:
#         format_json (Dict): 格式化后的JSON
#         kpi (List): 查询的指标

#     Returns:
#         List: 接口调用List
#     """
#     if isinstance(format_json, dict):
#         format_json_list = []
#         format_json_copy = copy.deepcopy(format_json)
#         del format_json_copy['time']
#         for k in kpi:
#             for t in format_json['time']:
#                 format_json_copy['start_time'],format_json_copy['end_time'] = t.split('-')
#                 format_json_clean = clean_json(format_json_copy,k)
#                 format_json_list.append(format_json_clean)
#     else:
#         format_json_list = [json.dumps({}, ensure_ascii=False)]
#     format_json_list = list(set(format_json_list))
#     return format_json_list

# print(json_to_list(format_json, kpi))


# kpi_name_dict = {"上险":"销量","市占":"市占",
#                  "客源数":"客源数","潜客数":"潜客数","展厅客流数":"展厅客流数","到店数":"展厅客流数","试乘试驾数":"试乘试驾数","订单数":"订单数",
#                  "发票数":"含大客户发票数","交车数":"含大客户发票数","销量":"含大客户发票数","含大客户发票数":"含大客户发票数",
#                  "库存":"库存","库存数":"库存数",
#                  "线索转化率":"线索转化率（LTO）","线索到店率":"线索到店率","试乘试驾率":"试乘试驾率","到店转化率":"到店转化率","订单成交率":"订单成交率",
#                  "客源目标":"客源目标","客源任务完成":"客源目标","客源进度":"客源目标",
#                  "潜客目标":"潜客目标","潜客任务完成":"潜客目标","潜客进度":"潜客目标",
#                  "展厅客流目标":"展厅客流目标","展厅客流任务完成":"展厅客流目标","展厅客流进度":"展厅客流目标","到店目标":"展厅客流目标","到店任务完成":"展厅客流目标","到店进度":"展厅客流目标",
#                  "试乘试驾目标":"试乘试驾目标","试乘试驾任务完成":"试乘试驾目标","试乘试驾进度":"试乘试驾目标",
#                  "订单目标":"订单目标","订单任务完成":"订单目标","订单进度":"订单目标",
#                  "发票目标":"含大客户发票目标","发票任务完成":"含大客户发票目标","发票进度":"含大客户发票目标",
#                  "交车目标":"含大客户发票目标","交车任务完成":"含大客户发票目标","交车进度":"含大客户发票目标",
#                  "销量目标":"含大客户发票目标","销量任务完成":"含大客户发票目标","销量进度":"含大客户发票目标",
#                  "含大客户发票目标":"含大客户发票目标","含大客户发票任务完成":"含大客户发票目标","含大客户发票进度":"含大客户发票目标",
#                  "零售发票目标":"发票目标","零售发票任务完成":"发票目标","零售发票进度":"发票目标",
#                  "批售目标":"批售目标","批售任务完成":"批售目标","批售进度":"批售目标",
#                  "转化":"转化","实时指标":"转化","转化漏斗":"转化","实时数据":"转化"}




# def clean_kpi(kpi):
#     """指标清洗

#     Args:
#         kpi (List): LLM提取到的指标

#     Returns:
#         List: 清洗后的指标
#     """
#     if isinstance(kpi, list):
#         cleaned_kpi = []
#         if '销售表现' in kpi:
#             print('y')
#             cleaned_kpi = ['转化','上险']
#         for i in kpi:
#             kpi_temp = i.split('（')[0]
#             print(kpi_temp)
#             if kpi_temp in kpi_name_dict.keys():
#                 cleaned_kpi.append(kpi_name_dict[kpi_temp])
#     else:
#         cleaned_kpi = []
#     return cleaned_kpi
    

# kpi_list = ['销量（Volume）', '销售表现']
# print(clean_kpi(kpi_list))


# input_text = '去年每月销量'

# for i in ['每月','每周','每日','分月','分周','分日']:
#     if i in input_text:
#         not_total = 'True'
#         break

# print(not_total)
# print('y')


# from datetime import datetime
 
# def is_date(string):
#     try:
#         datetime.strptime(string, "%Y%m%d")
#         return True
#     except ValueError:
#         return False
 
# # 使用示例
# print(is_date("2023-03-25"))  # 应该输出 True
# print(is_date("2023/03/25"))  # 应该输出 False，因为格式不匹配
# print(is_date("not-a-date"))  # 应该输出 False，因为不是有效日期格式

# import re
# def extract_digits(text):
#     """提取字符串中的数值部分

#     Args:
#         text (String): 需要提取数值的字符串

#     Returns:
#         String: 字符串中的数值部分
#     """
#     if not text:
#         return ''
#     digits = re.findall(r'\d+', text)
#     return digits[0] if digits else ''

# print(type(extract_digits('0210312text4531254')))


# import requests
# def get_warroom_latest_date():
#     url = "https://sandbox-openapi.svw-volkswagen.com/robot/v3/open/adapter/queryLastDataDate"
#     headers = {"Content-Type": "application/json"}
#     result = requests.get(url, headers=headers).json()
#     result = result["data"]
#     result['lastDataDateByDate'] = str(result['lastDataDateByDate'])
#     result['lastDataDateByWeek'] = str(result['lastDataDateByWeek'])
#     result['lastDataDateByMonth'] = str(result['lastDataDateByMonth'])
#     return result

# print(get_warroom_latest_date())


# import json, copy
# from utils.clean_json import *
# def json_to_list(format_json, kpi):
#     """将Json转成List，调用不同的数据

#     Args:
#         format_json (Dict): 格式化后的JSON
#         kpi (List): 查询的指标

#     Returns:
#         List: 接口调用List
#     """
#     if kpi == []:
#         format_json_list = []
#     else:
#         if isinstance(format_json, dict):
#             format_json_list = []
#             tmp_json_list = []
#             format_json_copy = copy.deepcopy(format_json)
#             del format_json_copy['time']
#             for k in kpi:
#                 tmp_kpi_json_list = []
#                 for t in format_json['time']:
#                     if '-' not in t:
#                         t = t + '-' + t
#                     format_json_copy['start_time'],format_json_copy['end_time'] = t.split('-')
#                     format_json_clean = clean_json(format_json_copy,[k])
#                     if format_json_clean == '{}' or format_json_clean in tmp_json_list:
#                         continue
#                     tmp_json_list.append(format_json_clean)
#                     tmp_kpi_json_list.append({'json_origin':format_json_copy,'json_clean':format_json_clean})
#                 if tmp_kpi_json_list != []:
#                     k_display = json.loads(tmp_kpi_json_list[0]['json_clean'])['display']
#                     format_json_list.append({'kpi':k,'display':k_display,'json_origin':format_json_copy,'json_clean':format_json_clean})
#         else:
#             format_json_list = []
#     return format_json_list

# kpi = ['销量','市占','转化']
# format_json = {'time': ['20230101-********'], 'date_type': 'year', 'location': ['全国'], 'model': [], 'display': '趋势', 'data_dim': 'all', 'today': 'False', 'not_total': 'False'}
# print(json_to_list(format_json, kpi))




# data = [{'time': 'total', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230301', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230302', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230303', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230304', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230305', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230306', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230307', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230308', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230309', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230310', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230311', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230312', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230313', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230314', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230315', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230316', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230317', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230318', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230319', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230320', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230321', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230322', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230323', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230324', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230325', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230326', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230327', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230328', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230329', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230330', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}, {'time': '20230331', 'location': '全国', 'model': 'SVW-VW', 'kpiValues': {'eInvoiceCnt': 0}}]
# warroom_data_list = []
# for tmp_data in data:
#     if tmp_data['time'] == 'total':
#         tmp_data['time'] == '11111'
#         warroom_data_list.append(tmp_data)

# print(warroom_data_list)


# import json
# def warroom_json_generate(format_json):
#     """将清洗后的Json转为可以进行数据查询的Json，以便查询想要的数据

#     Args:
#         format_json (String): 清洗后的Json

#     Returns:
#         String: 进行沙盘数据查询的Json
#     """
#     # 接口json转化
#     warroom_json = json.loads(format_json)
#     # 除了今日或者趋势查询，其余都使用分布进行统计数据查询
#     if warroom_json["display"] in ["趋势"] or (warroom_json["today"] == "True" and warroom_json["kpi"] == '转化'):
#         warroom_json["display"] = "趋势"
#     else:
#         if warroom_json["display"] == "同环比":
#             if "销量" in warroom_json["kpi"] or "市占" in warroom_json["kpi"]:
#                 warroom_json["kpi"] = "上险"
#             elif "批售数" in warroom_json["kpi"] or "含大客户发票数" in warroom_json["kpi"]:
#                 warroom_json["kpi"] = "批售"
#             elif "库存" in warroom_json["kpi"]:
#                 warroom_json["kpi"] = "库存"
#             else:
#                 warroom_json["kpi"] = "转化"
#         warroom_json["display"] = "分布"
#     warroom_json = json.dumps(warroom_json, ensure_ascii=False)
#     return warroom_json


# format_json = '{"time": "202403-202403", "start_time": "202403", "end_time": "202403", "date_type": "day", "model": ["SVW-VW"], \
#                 "location": ["Nationwide"], "kpi": ["转化"], "display": "漏斗", "today": "True", "data_dim": "all", "not_total": "False"}'


# print(warroom_json_generate(format_json))



# data = [{'kpi': '转化', 'display': '漏斗', 'json_list': [{'json_origin': {'date_type': 'day', 'location': ['全国'], 'model': [], 'display': '', 'data_dim': 'all', 'today': 'True', 'not_total': 'False', 'start_time': '20240328', 'end_time': '20240328'}, 
#                                                       'json_clean': '{"time": "202403-202403", "start_time": "202403", "end_time": "202403", "date_type": "day", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["转化"], "display": "漏斗", "today": "True", "data_dim": "all", "not_total": "False"}'}]}]


# print(len(data))







# import datetime
# def week_cal(date_str):
#     """将日期转换为周（含年）

#     Args:
#         date_str (String): 日期：202312, ********

#     Returns:
#         date_week (String): 周，例如：202311（2023年11周）
#     """
#     if len(date_str) == 4:
#         date_object = datetime.datetime.strptime(date_str, "%Y")
#     elif len(date_str) == 6:
#         date_object = datetime.datetime.strptime(date_str, "%Y%m")
#     elif len(date_str) == 8:
#         date_object = datetime.datetime.strptime(date_str, "%Y%m%d")
#     else:
#         date_object = datetime.datetime.strptime('202403', "%Y%m")

#     year = date_object.isocalendar()[0]
#     week_number = date_object.isocalendar()[1]

#     if week_number < 10:
#         week_number = '0' + str(week_number)

#     date_week = str(year) + str(week_number)
#     return date_week


# print(week_cal('202403'))



# import os
# def template_name_to_id_mappings(file_path):
#     with open(file_path, 'r', encoding='utf-8') as file:
#         mappings = {line.strip().split('\t')[1]:line.strip().split('\t')[0] for line in file}
#     return mappings

# parent_dir = os.path.dirname(os.path.abspath(__file__))
# template_file_path = os.path.join(parent_dir, 'data', 'template_name_id.tsv')
# template_name_to_id = template_name_to_id_mappings(template_file_path)

# def clean_template(template):
#     if isinstance(template,str):
#         template = template.replace('模板','')
#         print(template)
#         template = template_name_to_id.get(template, '')
#     else:
#         template = ''
#     return template


# tempplate_1 = 'PHEV'
# tempplate_2 = 'B NB'
# print(clean_template(tempplate_1))
# print(clean_template(tempplate_2))

# import json
# def warroom_json_generate(format_json):
#     """将清洗后的Json转为可以进行数据查询的Json，以便查询想要的数据

#     Args:
#         format_json (String): 清洗后的Json

#     Returns:
#         String: 进行沙盘数据查询的Json
#     """
#     # 接口json转化
#     warroom_json = json.loads(format_json)
#     # 除了今日或者趋势查询，其余都使用分布进行统计数据查询
#     if warroom_json["display"] in ["趋势"] or (warroom_json["today"] == "True" and warroom_json["kpi"] == ['转化']):
#         warroom_json["display"] = "趋势"
#     else:
#         if warroom_json["display"] == "同环比":
#             print('111')
#             if "销量" in warroom_json["kpi"] or "市占" in warroom_json["kpi"]:
#                 warroom_json["kpi"] = ["上险"]
#             elif "批售数" in warroom_json["kpi"] or "含大客户发票数" in warroom_json["kpi"]:
#                 warroom_json["kpi"] = ["批售"]
#             elif "总库存" in warroom_json["kpi"] or "总部库存" in warroom_json["kpi"]:
#                 print('222')
#                 warroom_json["kpi"] = ["库存"]
#             else:
#                 warroom_json["kpi"] = ["转化"]
#         warroom_json["display"] = "分布"
#     warroom_json = json.dumps(warroom_json, ensure_ascii=False)
#     return warroom_json

# format_json = '{"time": "202403-202403", "start_time": "202403", "end_time": "202403", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["总库存", "总库存当量"], "display": "同环比", "today": "False", "data_dim": "all", "not_total": "True", "template_id": ""}'

# print(warroom_json_generate(format_json))



# import requests

# def wenxin_ak():
#     url = "http://172.20.128.84:31510/account-config/v1/private/account/yiYan"
#     headers = {"Content-Type": "application/json"}
#     result = requests.get(url, headers=headers).json()
#     api_key = result["data"]["api-key"]
#     secret_key = result["data"]["secret-key"]
#     return api_key,secret_key
# BAIDU_CLIENT_ID,BAIDU_CLIENT_SECRET = wenxin_ak()

# import pandas as pd
# a = [[1,2,3],[2,3,4],[3,4,5]]
# a = pd.DataFrame(a,columns=['col1','col2','col3'])
# a['col3'] = a['col3']*2
# print(a)


# data = [{'json_origin': {'date_type': 'month', 'location': ['全国'], 'model': [], 'display': '同环比', 'data_dim': 'all', 'today': 'False', 'not_total': 'True', 'template': '', 'start_time': '********', 'end_time': '********'}, 
#          'json_clean': '{"time": "202301-202301", "start_time": "202301", "end_time": "202301", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["含大客户发票数", "含大客户发票数日均环比", "含大客户发票数日均同比"], "display": "同环比", "today": "False", "data_dim": "all", "not_total": "False", "template_id": ""}'}, 
#          {'json_origin': {'date_type': 'month', 'location': ['全国'], 'model': [], 'display': '同环比', 'data_dim': 'all', 'today': 'False', 'not_total': 'True', 'template': '', 'start_time': '********', 'end_time': '********'}, 'json_clean': '{"time": "202302-202302", "start_time": "202302", "end_time": "202302", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["含大客户发票数", "含大客户发票数日均环比", "含大客户发票数日均同比"], "display": "同环比", "today": "False", "data_dim": "all", "not_total": "False", "template_id": ""}'}, {'json_origin': {'date_type': 'month', 'location': ['全国'], 'model': [], 'display': '同环比', 'data_dim': 'all', 'today': 'False', 'not_total': 'True', 'template': '', 'start_time': '********', 'end_time': '********'}, 'json_clean': '{"time": "202303-202303", "start_time": "202303", "end_time": "202303", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["含大客户发票数", "含大客户发票数日均环比", "含大客户发票数日均同比"], "display": "同环比", "today": "False", "data_dim": "all", "not_total": "False", "template_id": ""}'}, {'json_origin': {'date_type': 'month', 'location': ['全国'], 'model': [], 'display': '同环比', 'data_dim': 'all', 'today': 'False', 'not_total': 'True', 'template': '', 'start_time': '********', 'end_time': '********'}, 'json_clean': '{"time": "202304-202304", "start_time": "202304", "end_time": "202304", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["含大客户发票数", "含大客户发票数日均环比", "含大客户发票数日均同比"], "display": "同环比", "today": "False", "data_dim": "all", "not_total": "False", "template_id": ""}'}, {'json_origin': {'date_type': 'month', 'location': ['全国'], 'model': [], 'display': '同环比', 'data_dim': 'all', 'today': 'False', 'not_total': 'True', 'template': '', 'start_time': '********', 'end_time': '********'}, 'json_clean': '{"time": "202305-202305", "start_time": "202305", "end_time": "202305", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["含大客户发票数", "含大客户发票数日均环比", "含大客户发票数日均同比"], "display": "同环比", "today": "False", "data_dim": "all", "not_total": "False", "template_id": ""}'}, {'json_origin': {'date_type': 'month', 'location': ['全国'], 'model': [], 'display': '同环比', 'data_dim': 'all', 'today': 'False', 'not_total': 'True', 'template': '', 'start_time': '********', 'end_time': '********'}, 'json_clean': '{"time": "202306-202306", "start_time": "202306", "end_time": "202306", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["含大客户发票数", "含大客户发票数日均环比", "含大客户发票数日均同比"], "display": "同环比", "today": "False", "data_dim": "all", "not_total": "False", "template_id": ""}'}, {'json_origin': {'date_type': 'month', 'location': ['全国'], 'model': [], 'display': '同环比', 'data_dim': 'all', 'today': 'False', 'not_total': 'True', 'template': '', 'start_time': '********', 'end_time': '********'}, 'json_clean': '{"time": "202307-202307", "start_time": "202307", "end_time": "202307", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["含大客户发票数", "含大客户发票数日均环比", "含大客户发票数日均同比"], "display": "同环比", "today": "False", "data_dim": "all", "not_total": "False", "template_id": ""}'}, {'json_origin': {'date_type': 'month', 'location': ['全国'], 'model': [], 'display': '同环比', 'data_dim': 'all', 'today': 'False', 'not_total': 'True', 'template': '', 'start_time': '********', 'end_time': '********'}, 'json_clean': '{"time": "202308-202308", "start_time": "202308", "end_time": "202308", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["含大客户发票数", "含大客户发票数日均环比", "含大客户发票数日均同比"], "display": "同环比", "today": "False", "data_dim": "all", "not_total": "False", "template_id": ""}'}, {'json_origin': {'date_type': 'month', 'location': ['全国'], 'model': [], 'display': '同环比', 'data_dim': 'all', 'today': 'False', 'not_total': 'True', 'template': '', 'start_time': '********', 'end_time': '********'}, 'json_clean': '{"time": "202309-202309", "start_time": "202309", "end_time": "202309", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["含大客户发票数", "含大客户发票数日均环比", "含大客户发票数日均同比"], "display": "同环比", "today": "False", "data_dim": "all", "not_total": "False", "template_id": ""}'}, {'json_origin': {'date_type': 'month', 'location': ['全国'], 'model': [], 'display': '同环比', 'data_dim': 'all', 'today': 'False', 'not_total': 'True', 'template': '', 'start_time': '********', 'end_time': '********'}, 'json_clean': '{"time": "202310-202310", "start_time": "202310", "end_time": "202310", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["含大客户发票数", "含大客户发票数日均环比", "含大客户发票数日均同比"], "display": "同环比", "today": "False", "data_dim": "all", "not_total": "False", "template_id": ""}'}, {'json_origin': {'date_type': 'month', 'location': ['全国'], 'model': [], 'display': '同环比', 'data_dim': 'all', 'today': 'False', 'not_total': 'True', 'template': '', 'start_time': '********', 'end_time': '********'}, 'json_clean': '{"time": "202311-202311", "start_time": "202311", "end_time": "202311", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["含大客户发票数", "含大客户发票数日均环比", "含大客户发票数日均同比"], "display": "同环比", "today": "False", "data_dim": "all", "not_total": "False", "template_id": ""}'}, {'json_origin': {'date_type': 'month', 'location': ['全国'], 'model': [], 'display': '同环比', 'data_dim': 'all', 'today': 'False', 'not_total': 'True', 'template': '', 'start_time': '********', 'end_time': '********'}, 'json_clean': '{"time": "202312-202312", "start_time": "202312", "end_time": "202312", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["含大客户发票数", "含大客户发票数日均环比", "含大客户发票数日均同比"], "display": "同环比", "today": "False", "data_dim": "all", "not_total": "False", "template_id": ""}'}]



# print(len(data))


# import copy
# format_json = {'time': ['20230101-20230131', '20230201-20230229', '20230301-20230331', '20230401-20230430', '20230501-20230531', '20230601-20230630', '20230701-20230731', '20230801-20230831', '20230901-20230930', '20231001-20231031', '20231101-20231130', '********-********'], 'date_type': 'month', 'location': ['全国'], 'model': [], 'display': '同环比', 'data_dim': 'all', 'today': 'False', 'not_total': 'True', 'template': ''}
# kpi = ['含大客户电子发票']
# import json

# if isinstance(format_json, dict):
#     format_json_list = []
#     tmp_json_list = []
#     format_json_copy = copy.deepcopy(format_json)
#     del format_json_copy['time']
#     for k in kpi:
#         tmp_kpi_json_list = []
#         for t in format_json['time']:
#             print(t)
#             if '-' not in t:
#                 t = t + '-' + t
#             format_json_copy['start_time'],format_json_copy['end_time'] = t.split('-')
#             print(format_json_copy)
#         #     format_json_clean = 
#         #     tmp_json_list.append(format_json_clean)
#             tmp_kpi_json_list.append({'json_origin':format_json_copy})
#             print(tmp_kpi_json_list)
#             print('------------------')
            
#         # if tmp_kpi_json_list != []:
#         #     k_display = json.loads(tmp_kpi_json_list[0]['json_clean'])['display']
#         #     format_json_list.append({'kpi':k,'display':k_display,'json_list':tmp_kpi_json_list})
        
# else:
#     format_json_list = []

# import json, requests
# def warroom_post(data: str) -> list:
#     url = 'https://openapi.svw-volkswagen.com/robot/v3/open/adapter/vw/listCompetitionAnalyse'
#     headers = {"Content-Type": "application/json"}
#     data = json.loads(data)
#     result = requests.post(url, headers=headers,data=json.dumps(data)).json()
#     return result["data"]

# data = '{"time": "20240401-20240414", "start_time": "202404", "end_time": "202404", "date_type": "month", "model": ["SVW-VW"], "location": ["Nationwide"], "kpi": ["客源数"], "display": "分布", "today": "False", "data_dim": "model", "not_total": "False", "template_id": ""}'

# response = warroom_post(data)
# print(response[0]['brand'])
# print(type(response[0]['brand']))
# data_brands = list(set([i["brand"] for i in response]))
# if data_brands == [None]:
#     print(True)

# format_json = {'time':[]}
# if format_json['time'] == []:
#     format_json['time'] = [' - ']
# for t in format_json['time']:
#     a,b = t.split('-')
#     print('a',a)
#     print('b',b)


# prompt = """[Requirement]
# 1. Return using JSON format, output does not contain any other content or analysis process, do not return ``` JSON ```, only JSON text. The content of JSON is generated as follows:
# 2.1. time:
# 2.1.1. Extract explicit times that appear in user input and save them in the form of a list.
# 2.1.2. The relative time is converted based on the benchmark date of {today}, using the complete natural year, the complete natural quarter, and the complete natural month.
# 2.1.3. Identify the start time and end time of the time range, strictly using the format of yyyymmdd-yyyymmdd.
# 2.1.4. If the input time information cannot be parsed or does not exist, return an empty list [].
# 2.2. date_type: Extract the type of query time input by the user, return "day" by day, "week" by week, "month" by month, and "year" by year; If the information does not exist, use "" as the value.
# 2.3. location: Extract the regions, provinces, and cities that appear in user input without any analysis and inference, and supplement keywords "region", "province", and "city". Save results in a list format, such as ["Nationwide"], ["Huadong region"], ["Beijing city", "Shanghai city"]; If there is no relevant information, return ["Nationwide"]; By default, "by region" or "by SVR" return to [National].
# 2.4. model: Extract the vehicle brand, vehicle category, or vehicle model name from the user input, as well as multi value fields, and save them with a list structure in square brackets; If the information does not exist, please use [] as the value.
# 2.5. display: Extract possible data display forms from user input, only return keywords "trend", "distribution", "ranking", "comparison", "year-on-year (yoy)", "month on month (mom)"; If the information does not exist, return "".
# 2.6. data_dim:
# 2.6.1. "by SVR" equivalent to "by region".
# 2.6.2. If the user input contains keywords "by region", "by province", "by city", or explicitly mentions drilling down on "region", "province", or "city", return "location".
# 2.6.3. If the user input contains keywords "by brand", "by model", or explicitly mentions drilling down on "brand" or "model", return "model".
# 2.6.4. If the user input does not explicit mentions about drill down on region or model, even if user input contains specified region or model information, do not analyse or inference, only return "all".
# 2.7. today: If the user input only involves today's data or real-time data, return "True"; otherwise, return "False".
# 2.8.not_total: If the user inputs monthly, weekly, or daily data, return "True"; otherwise, return "False".
# 2.9. template: If the user explicitly mentions using a template for querying, extract the name of the template, save result as a string; If the information does not exist, return "".

# [User input]:
# {question}
# [Output]: """

# prompt = prompt.format(question='How was the sales of Passat last year', today='April 26, 2024')

import requests
import json
import time

def get_access_token():
    """
    使用 API Key，Secret Key 获取access_token，替换下列示例中的应用API Key、应用Secret Key
    """
        
    # url = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=4UeHb4PaemCIaEQGFUthzgGE&client_secret=ppDaYf0WYZjAUlBjoA3L8ebEs8Khqpqw"
    url = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=fW1PBg9AVBXSUdFigdNdsdtt&client_secret=UCy0s691b118gKGdZoGL9cwEDEPyK0nv"
    
    payload = json.dumps("")
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    
    response = requests.request("POST", url, headers=headers, data=payload)
    return response.json().get("access_token")


def wenxin_request(prompt):
        
    # url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro?access_token=" + get_access_token()
    # url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-4.0-8k-0329?access_token=" + get_access_token()
    url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-4.0-8k-0104?access_token=" + get_access_token()
    
    payload = json.dumps({
        "messages": [{"role": "user", "content": prompt}],
        "stream": False,
        "temperature": 0.01,
    })
    headers = {
        'Content-Type': 'application/json'
    }
    t1 = time.time()
    response = requests.request("POST", url, headers=headers, data=payload, stream=False)

    response_json = json.loads(response.text)
    return response_json


prompt = """[要求]
1.以JSON格式返回，输出不包含其他内容或分析过程，不要返回```json```，只有JSON文本
2.JSON的内容按如下方式生成：
2.1.time：
2.1.1.提取用户输入中出现的明确时间，并以列表(list)形式保存。
2.1.2.相对时间以基准日期2024年5月14日进行转化，相对时间使用完整自然年、自然季度、自然月。
2.1.3.识别时间范围的开始和结束时间，严格使用yyyymmdd-yyyymmdd的格式。
2.1.4.如果输入的时间信息无法解析或不存在，则返回一个空列表 []。
2.2.date_type：提取用户输入查询时间的类型，按天返回"day"，按周返回"week"，按月返回"month"，按年返回"year"; 如果信息不存在, 请使用 "" 作为值。
2.3.location：提取用户输入中出现的大区、省份、城市，无需进行分析和推理，并补充"大区"、"省"、"市"关键字，多值字段，用中括号 list 结构保存，如["全国"]，["华东大区"]，["北京市","上海市"]；无相关信息，则使用 ["全国"] 作为值；"各大区" 默认返回 ["全国"]。
2.4.model：提取用户输入中的车辆品牌或车辆类别或车型名称, 多值字段, 用中括号 list 结构保存；如果信息不存在, 请使用 [] 作为值。
2.5.display：提取用户输入中隐含的数据展示形式，单值字段，仅包括"趋势","分布","排名","对比","同环比（yoy and mom）","同比（mom）","环比（yoy）"，无需添加其他不在范围的词汇; 如果信息不存在, 请使用 "" 作为值。
2.6.data_dim：
2.6.1."by SVR"表示"按大区维度下钻"的含义。
2.6.2.如果用户输入明确说明按地区维度下钻或出现"各大区"、"各省份"、"各城市"，返回"location"；如果用户输入明确说明按车型维度下钻或出现"各车型"，返回"model"。
2.6.3.如果用户输入没有明确说明按地区或车型维度下钻，即使含有地区或车型相关信息，也不返回"location"或"model"，只返回"all"。
2.7.today：如果用户输入仅涉及今日数据或实时数据，返回"True"，否则，返回"False"。
2.8.not_total：如果用户输入需要每月、每周、每日的数据，返回"True"，否则，返回"False"。
2.9.template：如果用户明确提到使用模板查询进行查询，提取模板的名称，单值字段，字符串; 如果信息不存在, 请使用 "" 作为值。

[用户输入]：
本月各区域市占率同环比
[输出]："""


# prompt = """[要求]
# 1.以文本格式返回下方内容，结果不包含分析过程或解释或说明。
# 2.[指标]："上险（Insurance）", "市占（Market Share or M.S.）", "转化（Conversion）", "漏斗（Funnel）", "实时数据", "销量（Volume）", "销售表现", "客源数（Leads）", "潜客数（NPC）", "展厅客流数（Showroom Traffic）", "到店数", "试乘试驾数（Test Drive）", "订单数（NCO）", "交车数", "发票数（Invoice）", "零售发票数（Retail Invoice）", "批售数（Whole sale）", "含大客户发票数（Invoice KA Included）", "库存", "库存数（Stock）", "经销商库存数（Dealer Stock）", "总部库存数（HQ Stock）", "库存当量（Stock Factor）", "总部库存当量（HQ Stock Factor）", "经销商存数当量（Dealer Stock Factor）", "线索转化率（LTO）", "线索到店率（Leads to ST）", "试乘试驾率（ST to TD）", "到店转化率（ST to NCO）", "订单成交率（NCO to Invoice）", "客源目标（Leads Target）", "客源任务完成（Leads Achieved）", "客源进度", "潜客目标（NPC Target）", "潜客任务完成（NPC Achieved）", "潜客进度", "展厅客流目标（Showroom Traffic Target）", "展厅客流任务完成（Showroom Traffic Achieved）", "展厅客流进度", "到店目标", "到店任务完成", "到店进度", "试乘试驾目标（Test Drive Target）", "试乘试驾任务完成（Test Drive Achieved）", "试乘试驾进度", "订单目标（NCO Target）", "订单任务完成（NCO Achieved）", "订单进度", "发票目标（Invoice Target）", "发票任务完成（Invoice Achieved）", "发票进度", "交车目标", "交车任务完成", "交车进度", "销量目标", "销量任务完成", "销量进度", "含大客户发票目标（Invoice KA Included Target）", "含大客户发票任务完成（Invoice KA Included Achieved）", "含大客户发票进度", "零售发票目标（Retail Invoice Target）", "零售发票任务完成（Retail Invoice Achieved）", "零售发票进度", "批售目标（Whole sale Target）", "批售任务完成（Whole sale Achieved）", "批售进度"
# 3.文本的内容按如下方式生成：
# 3.1.[语言]：识别用户输入的语言，包括"英文"和"中文"。
# 3.2.[意图]：识别用户输入的意图，包括：
# 3.2.1.数据查询：如果用户输入涉及指标结果查询，请返回"数据查询"。
# 3.2.2.数据分析：如果涉及数据结果原因分析或对数据的进一步提问，包括哪一个(which)，哪些(which ones)，谁(who)，什么时间(when)，请返回"数据分析"。
# 3.2.3.页面交互：如果用户输入明确需要进行"时间筛选"、"页面返回"、"页面关闭"、"页面退出"，请返回"页面交互"。
# 3.2.4.如果数据查询不包含指标，或不是上述三种意图的其中一种，请返回"未知意图"。
# 3.3.[查询指标]：如果意图为"数据查询"或"数据分析"，识别查询的指标，并返回指标完整名称，多值字段, 用中括号 list 结构保存，例如["订单数（NCO）"],["销量（Volume）","市占（Market Share or M.S.）"]; 如果信息不存在, 请使用 [] 作为值。

# [用户输入]：今年河南和河北帕萨特和朗逸发票数同环比

# [语言]：
# [意图]：
# [查询指标]："""



# 上月ID.3和海豚在各区域的上险
# Which model sold best in February?

print(wenxin_request(prompt))

# import pandas as pd
# from tqdm import tqdm

# if __name__ == '__main__':
#     results = []
#     for i in tqdm(range(1)):
#         result = wenxin_request()
#         print(result)
#     #     results.append(result)
#     # df = pd.DataFrame(results)
#     # df.to_excel('results_20240425.xlsx')

# from http import HTTPStatus
# import dashscope
# def call_with_messages(content):
#     messages = [{'role': 'user', 'content': content}]
#     response = dashscope.Generation.call(
#         # model='llama3-70b-instruct',
#         model='qwen1.5-110b-chat',
#         temperature = 0,
#         messages=messages,
#         result_format='text',  
#         api_key = 'sk-6WntUpS8uA'
#     )
#     if response.status_code == HTTPStatus.OK:
#         return response['output']['text']
#     else:
#         print('Request id: %s, Status code: %s, error code: %s, error message: %s' % (
#             response.request_id, response.status_code,
#             response.code, response.message
#         ))


# prompt_all_template = """[要求]
# 1.以JSON格式返回，输出不包含其他内容或分析过程，不要返回json，只有JSON文本
# 2.JSON的内容按如下方式生成：
# {sub_prompt}

# [用户输入]：{question}
# [输出]："""


# prompt_time_template = """2.1.time：
# 2.1.1.识别用户输入中出现的一个或多个绝对或相对时间描述，忽略时间间隔描述。
# 2.1.2.相对时间以{today}为基准进行转化，相对时间使用完整自然年、自然季度、自然月。
# 2.1.3.每个时间时间段严格使用yyyymmdd-yyyymmdd的格式进行返回，不进行区间的拆分。
# 2.1.4.结果以列表(list)形式返回，如果输入的时间信息无法解析或不存在，则返回一个空列表 []。"""
# today = '2024年4月30日'
# prompt_time = prompt_time_template.format(today=today)
# prompt_date_type = """2.1.date_type：提取用户输入查询时间的类型，按天返回"day"，按周返回"week"，按月返回"month"，按年返回"year"; 如果信息不存在, 请使用 "" 作为值。"""
# prompt_location = """2.1.location：提取用户输入中出现的大区、省份、城市，无需进行分析和推理，并补充"大区"、"省"、"市"关键字，多值字段，用中括号 list 结构保存，如["全国"]，["华东大区"]，["北京市","上海市"]；无相关信息，则使用 ["全国"] 作为值；"各大区" 默认返回 ["全国"]。"""
# prompt_model = """2.1.model：提取用户输入中的车辆品牌或车辆类别或车型名称, 多值字段, 用中括号 list 结构保存；如果信息不存在, 请使用 [] 作为值。"""
# prompt_display = """2.1.display：提取用户输入中隐含的数据展示形式，单值字段，仅包括"趋势","分布","排名","对比","同环比（yoy and mom）","同比（mom）","环比（yoy）"，无需添加其他不在范围的词汇; 如果信息不存在, 请使用 "" 作为值。"""
# prompt_data_dim = """2.1.data_dim：
# 2.1.1.如果用户输入仅提及特定层级（包括：大区，省份，城市，车型），但没有明确说明下钻到更细致的层级，则返回"all"；否则转入下一步。
# 2.1.2.如果用户输入明确包含想要对地区维度进行下钻，或出现"各大区"、"各省份"、"各城市"等含义，返回"location"；如果用户输入明确包含想要对车型维度进行下钻，或出现"各车型"等含义，返回"model"。
# 2.1.3.如果用户输入出现"by SVR"代表"各大区"的含义。"""
# prompt_today = """2.1.today：如果用户输入仅涉及今日数据或实时数据，返回"True"，否则，返回"False"。"""
# prompt_not_total = """2.1.not_total：如果用户输入需要每月、每周、每日的数据，返回"True"，否则，返回"False"。"""
# prompt_template = """2.1.template：如果用户明确提到使用模板查询进行查询，提取模板的名称，单值字段，字符串; 如果信息不存在, 请使用 "" 作为值。"""

# sub_prompt_dicts = {'time': prompt_time, 'date_type': prompt_date_type, 'location': prompt_location, 'model': prompt_model, 
#                     'display': prompt_display, 'data_dim': prompt_data_dim, 'today': prompt_today, 'not_total': prompt_not_total, 
#                     'template': prompt_template}

# import time
# question = '去年各省份帕萨特的销量分布情况'
# df = []
# for i in sub_prompt_dicts.keys():
#     prompt = prompt_all_template.format(sub_prompt=sub_prompt_dicts[i], question=question)
#     print(prompt)
#     t1 = time.time()
#     response_json = call_with_messages(prompt)
#     t2 = time.time()
#     df.append([i, response_json,t2-t1])
#     time.sleep(60)
#     # break
# print(df)





# import pandas as pd
# def get_date_table():
#     from chinese_calendar.constants import holidays
#     from chinese_calendar import is_holiday, is_workday, is_in_lieu
#     from chinese_calendar import get_holiday_detail

#     min_year, max_year = min(holidays.keys()).year, max(holidays.keys()).year
#     first_date = pd.to_datetime(f"{min_year}0101", format="%Y%m%d")
#     end_date = pd.to_datetime(f"{max_year}1231", format="%Y%m%d")
#     cnt_date = (end_date-first_date).days + 1
#     df = pd.DataFrame(
#         {"date": [first_date + pd.Timedelta(i, "d") for i in range(cnt_date)]}
#     )
#     df["year_week_number_day"] = df["date"].apply(lambda x: x.isocalendar())
#     df["week_number"] = df["date"].apply(lambda x: x.isocalendar()[1])
#     df["weekday"] = (df["date"].apply(lambda x: x.weekday()) + 1)/7
#     df["is_holiday"] = df["date"].apply(lambda x: is_holiday(x))
#     df["is_workday"] = df["date"].apply(lambda x: is_workday(x))
#     df["is_in_lieu"] = df["date"].apply(lambda x: is_in_lieu(x))
#     df["holidaydetail"] = df["date"].apply(lambda x: get_holiday_detail(x))
#     df["holiday"] = df["holidaydetail"].apply(lambda x: x[1] if x[0] else None)
#     df["date"] = df["date"].dt.strftime('%Y-%m-%d')
#     df.insert(0, "month", df["date"].str[:7])
#     df.insert(0, "year", df["date"].str[:4])
#     df["is_chinese_new_year"] = (("Spring Festival"==df["holiday"]) & df["is_holiday"])
#     df["is_general_holiday"] = df["is_holiday"] & (~df["is_chinese_new_year"])
#     df["date_type"] = df[["is_workday", "is_general_holiday", "is_chinese_new_year"]].apply(
#         lambda x: "workday" if x[0] else ("general_holiday" if x[1] else "chinese_new_year"), axis=1
#     )
#     df["workday"] = df["is_workday"].astype(int)
#     df["general_holiday"] = df["is_general_holiday"].astype(int)
#     df["chinese_new_year"] = df["is_chinese_new_year"].astype(int)
#     df["monday"] = (1==df["weekday"]).astype(int)
#     df["tuesday"] = (2==df["weekday"]).astype(int)
#     df["wednesday"] = (3==df["weekday"]).astype(int)
#     df["thursday"] = (4==df["weekday"]).astype(int)
#     df["friday"] = (5==df["weekday"]).astype(int)
#     df["saturday"] = (6==df["weekday"]).astype(int)
#     df["sunday"] = (7==df["weekday"]).astype(int)
#     df["monthday"] = df['date'].str[-2:].astype(int)/31
#     return df

# df_date = get_date_table()
# dic = df_date.groupby(["month"]).size().to_dict()



# def gen_feature_month():
#     df = get_date_table()
#     df = df.groupby(["month"], as_index=False)[["workday", "general_holiday", "chinese_new_year"]].sum()
#     df.rename(columns={
#         "workday": "cnt_workday", 
#         "general_holiday": "cnt_general_holiday", 
#         "chinese_new_year": "cnt_chinese_new_year"
#         }, inplace=True)
#     df["month_workday"] = df["cnt_workday"]/31
#     df["month_general_holiday"] = df["cnt_general_holiday"]/31
#     df["month_chinese_new_year"] = df["cnt_chinese_new_year"]/31
#     df["yearmonth"] = df["month"].str[-2:].astype(int)/12
#     print(df["month"].str[-2:])
#     return df 

# print(gen_feature_month())

# def gen_feature_date():
#     df_date = get_date_table()
#     df_month = gen_feature_month()
#     df = pd.merge(df_date, df_month, how="inner", on="month")
#     return df


# def gen_feature_leads_order(dataframe, groupby_column="month", sum_column="cnt"):
#     df_month = pd.DataFrame(index=dataframe[groupby_column].drop_duplicates()).sort_index()
#     for data_type in settings.data_type:
#         df_month_data_type = dataframe.query(f"data_type=='{data_type}'").groupby([groupby_column])[[sum_column]].sum()  # /settings.dic_data_type_norm_max[data_type]
#         df_month_data_type.rename(columns={sum_column:data_type}, inplace=True)
#         df_month = df_month.join(df_month_data_type)
#         for channel_1 in settings.channel_1:
#             df_month_data_type_channel_1 = dataframe.query(
#                 f"data_type=='{data_type}' and channel_1=='{channel_1}'"
#                 ).groupby([groupby_column])[[sum_column]].sum()  # /settings.dic_data_type_norm_max[data_type]
#             df_month_data_type_channel_1.columns = [f"{data_type}_{channel_1}"]
#             df_month = df_month.join(df_month_data_type_channel_1)
#     df_month.reset_index(inplace=True)
#     return df_month.fillna(0)


# import pandas as pd
# min_date = '2021-01-01'
# max_date = '2024-05-06'

# first_date = pd.to_datetime(f"{min_date}", format="%Y-%m-%d")
# end_date = pd.to_datetime(f"{max_date}", format="%Y-%m-%d")
# cnt_date = (end_date-first_date).days + 1
# df = pd.DataFrame(
#     {"date": [first_date + pd.Timedelta(i, "d") for i in range(cnt_date)]}
# )



# df["date"] = df["date"].dt.strftime('%Y-%m-%d')
# df_data_type = pd.DataFrame({"data_type":["leads", "instore", "td", "order"]})
# df_channel_1 = pd.DataFrame({"channel_1":["垂媒", "媒体落地页", "线下", "经销商新媒体", "自建平台及电商"]})

# df = pd.merge(df, df_data_type, how="cross")
# df = pd.merge(df, df_channel_1, how="cross")

# print(df)
# print(df_data_type)
# print(df_channel_1)

# import pandas as pd
# dataframe = [['2024-01','leads','自建平台及电商','53.136077'],
# ['2024-02','instore','线下','92.504755'],
# ['2024-02','order','垂媒','10.672918'],
# ['2024-01','order','自建平台及电商','8.307410'],
# ['2024-02','leads','媒体落地页','21.915493'],
# ['2024-02','leads','经销商新媒体','149.054680'],
# ['2024-01','instore','媒体落地页','2.650704'],
# ['2024-01','instore','自建平台及电商','8.410229'],
# ['2024-02','order','媒体落地页','1.363636'],
# ['2024-02','instore','经销商新媒体','10.138514'],
# ['2024-01','instore','垂媒','33.197612'],
# ['2024-01','order','经销商新媒体','8.599676'],
# ['2024-01','order','垂媒','17.216404'],
# ['2024-01','order','线下','52.192282'],
# ['2024-01','leads','垂媒','742.221256'],
# ['2024-02','order','经销商新媒体','5.686649'],
# ['2024-01','instore','经销商新媒体','15.110843'],
# ['2024-02','leads','线下','179.961842'],
# ['2024-02','order','线下','29.000884'],
# ['2024-02','leads','垂媒','701.361980'],
# ['2024-01','leads','媒体落地页','21.031306'],
# ['2024-01','leads','经销商新媒体','145.812606'],
# ['2024-02','instore','媒体落地页','2.485366'],
# ['2024-02','leads','自建平台及电商','42.094396'],
# ['2024-02','instore','自建平台及电商','7.719728'],
# ['2024-02','order','自建平台及电商','7.255858'],
# ['2024-01','td','线下','67.567114'],
# ['2024-01','td','垂媒','13.741634'],
# ['2024-01','td','经销商新媒体','6.613828'],
# ['2024-01','td','媒体落地页','1.505747'],
# ['2024-02','td','垂媒','10.320457'],
# ['2024-02','td','媒体落地页','1.316901'],
# ['2024-02','td','自建平台及电商','4.986458'],
# ['2024-02','instore','垂媒','23.692926'],
# ['2024-01','leads','线下','277.295876'],
# ['2024-01','order','媒体落地页','1.583799'],
# ['2024-01','instore','线下','129.388922'],
# ['2024-02','td','经销商新媒体','4.554479'],
# ['2024-02','td','线下','53.055973'],
# ['2024-01','td','自建平台及电商','5.382305']]
# dataframe = pd.DataFrame(dataframe)
# dataframe.columns = ["month", "data_type", "channel_1", "norm_cnt"]
# dataframe['norm_cnt'] = dataframe['norm_cnt'].astype(float)


# def gen_feature_leads_order(dataframe, groupby_column="month", sum_column="norm_cnt"):
#     """dataframe包含"month", "data_type", "channel_1", "norm_cnt"
#     """
#     data_type = ["leads", "instore", "td", "order"]
#     channel_1 = ["垂媒", "媒体落地页", "线下", "经销商新媒体", "自建平台及电商"]
#     df_month = pd.DataFrame(index=dataframe[groupby_column].drop_duplicates()).sort_index()
#     for data_type in data_type:
#         df_month_data_type = dataframe.query(f"data_type=='{data_type}'").groupby([groupby_column])[[sum_column]].sum()  # /settings.dic_data_type_norm_max[data_type]
#         df_month_data_type.rename(columns={sum_column:data_type}, inplace=True)
#         df_month = df_month.join(df_month_data_type)
#         for channel_1 in channel_1:
#             df_month_data_type_channel_1 = dataframe.query(
#                 f"data_type=='{data_type}' and channel_1=='{channel_1}'"
#                 ).groupby([groupby_column])[[sum_column]].sum()  # /settings.dic_data_type_norm_max[data_type]
#             df_month_data_type_channel_1.columns = [f"{data_type}_{channel_1}"]
#             df_month = df_month.join(df_month_data_type_channel_1)
#     df_month.reset_index(inplace=True)
#     return df_month.fillna(0)


# df_month_sts_norm = gen_feature_leads_order(dataframe, groupby_column="month", sum_column="norm_cnt")
# print(df_month_sts_norm)
# print(df_month_sts_norm.columns)



# from llm2json.prompts import Templates