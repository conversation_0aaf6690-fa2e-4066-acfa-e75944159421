客源定义：去重后的新增客源数
潜客定义：当期建卡的潜客数量
到店定义：首次到店时间为当期的潜客
试乘试驾定义：当期进行过试乘试驾并签订线上试驾合同的潜客数量
订单定义：当期新增的订单数
零售发票定义：当期新增的零售电子发票数（不含大客户）
发票（含大客户）定义：当期新增的电子发票数（含大客户）
建卡率定义：当期新增有效客源中（包含到店、网电、外拓、二网等渠道）建卡客户比例
建卡率计算公式：当期新增潜客数/当期新增客源数
邀约到店率定义：当期新增网电及外拓线索建卡客户中，首次邀约到店潜客所占的比例
邀约到店率计算公式：当期邀约到店数/当期新增潜客数(不含展厅-到店和二网)
试乘试驾率定义：当期首次试乘试驾的客户数占当期首次到店的客户数的比例
试乘试驾率计算公式：当期试乘试驾数/当期到店潜客（全渠道，包含首次到展厅新增潜客、DCC邀约到店、外拓邀约到店等）
潜客转化率定义：当期新增订单数占当期新增潜客的比例
新增潜客转化率计算公式：当期新增订单数/当期新增潜客数
订单成交率定义：当期新增的零售电子发票数（不含大客户）占当期新增订单的比例
订单成交率计算公式：零售发票数/新增订单数
日均环比定义：月度环比表现，本月数据为MTD日均数据，上月数据为上月全月日均数据
MoM计算公式：本期/上期-1
日均同比定义：月度同比表现，本月数据为MTD的日均数据，去年同期数据为去年同期全月日均数据
YOY计算公式：本期/去年同期-1
The definition of Leads: Leads after deduplication
The definition of NPC: New potential customer who have build card in this period
The definition of In-store: First-time-come-to-store NPC in this period
The definition of Test Drive: NPC who has tested drive with e-contract in this period
The definition of NCO: New customer order in this period
The definition of Retail Invoice: New Retail Invoice(KA not included) in this period
The definition of Invoice (KA included): New Invoice (KA Included) in this period
The definition of Leads to NPC: Percentage of customer who have build card in effective leads (including online/offline channel)
Calculation formula for Leads to NPC: NPC / Leads after deduplication
The definition of Invited to Store: Percentage of first-time-invited-to-store customer among the online and external extension channel NPC
Calculation formula for Invited to Store: First-time-invited-to-store customer / NPC (excluding showroom-walkin & second network)
The definition of In-Store to TD: Percentage of customer who has tested drive this period in first-time-come-to-store NPC
Calculation formula for Test drive rate: Test drive this period / first-time-come-to-store NPC this period
The definition of NPC to NCO: Proportion of NCO to NPC in this period
Calculation formula for NPC to NCO: NCO / NPC
The definition of NCO to Invoice: Proportion of Retail Invoice (KA not included) to NCO in this period
Calculation formula for NCO to Invoice: Retail Invoice / NCO
The definition of MOM: Data for the current month is the MTD average daily data, Data for the previous month is the whole month average daily data.
Calculation formula for MoM: Data of current month / data of previous month - 1
The definition of YOY: Data for the current month is the MTD average daily data, Data for the same month last year is the whole month average daily data.
Calculation formula for YOY: Data of current month / data of the same month last year - 1
6块大屏各自的内容：第一屏，全国转化总览（Funnel Overview）；第二屏，区域及车型转化（Funnel by Region and Model）；第三屏，区域视图（Region View）；第四屏，车型视图（Model View）；第五屏，线索及线索渠道贡献（Leads Channel Contribution）；第六屏，售后（After Service）
全国转化总览（Funnel Overview）屏幕内容：第一屏，展示全国销售漏斗，及分大区/车型的发票目标达成情况
区域及车型转化（Funnel by Region and Model）屏幕内容：第二屏，展示营销大区及重点车型的销售转化、目标达成、价差盈利、库存及市占表现
区域视图（Region View）屏幕内容：第三屏，在第一、二屏的基础上，支持查看特定营销大区的销售漏斗情况及分车型表现
车型视图（Model View）屏幕内容：第四屏，在第一、二屏的基础上，支持查看特定车型的销售漏斗情况及分营销大区表现
线索及线索渠道贡献（Leads Channel Contribution）屏幕内容：第五屏，展示全国线索、首触点到店及首触点线索转化情况，支持渠道及车型交叉分析
售后（After Service）屏幕内容：第六屏，展示全国售后进站台次、产值数据及同比，展示全国配附件批售目标完成情况
大众品牌车型：帕萨特、朗逸、凌渡、途观、桑塔纳、Polo、途岳、途铠、途昂、途安、威然、辉昂、ID.4 X、ID.6 X、ID.3
数据问题负责人：梁凯智（SX部门），邮箱：<EMAIL>
销量（上险）数据更新周期：当月更新上月数据，一般每月10号左右更新
SVW Copilot数据查询范围介绍：1.数据查询：\n1.1.范围：SVW Copilot可查询大众品牌转化、目标、库存及市场竞品上险数据。\n1.2.要素：查询需明确指标、时间（默认本月）和地区（默认全国），可选品牌或车型（默认SVW-VW）。\n1.3.结果：提供文字、图表、同环比等多种形式的查询反馈。\n2.数据分析：在查询基础上加"分析"，SVW Copilot会结合数据给出分析结果。\n3.非数据问题：对于常识等问题，SVW Copilot依据大语言模型的历史语料库进行回答。
Introduction to the data query scope of SVW Copilot: 1. Data query:\n1.1. Scope: SVW Copilot can query SVW-VW brand conversion, targets, stock, and competitor insurance data.\n1.2. Element: The query should specify the indicators, time (default for this month), and region (default for the whole country), and the brand or model is optional (default for SVW-VW).\n1.3. Results: Provide various forms of query feedback such as text, charts, and table.\n2. Data analysis: Add "analysis" to the query sentence, and SVW Copilot will provide analysis results based on the data.\n3. Non data questions: For common sense and other questions, SVW Copilot answers based on the historical corpus of the large language model.