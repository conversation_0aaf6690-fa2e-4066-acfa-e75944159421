import os
from fastapi import FastAPI, WebSocket, Request, HTTPException
from fastapi.responses import StreamingResponse, JSONResponse
import uvicorn
from langchain.chains import <PERSON><PERSON>hain
from utils.llm import *
from utils.sx_prompt import *
from utils.clean_json import extract_json_from_markdown, extract_and_clean_json_ui, clean_kpi
from utils.sx_index import vector_index
from utils.sx_chart_process import chart_select, content_show
from utils.env_config import *
from utils.sx_dict import *
from utils.sx_date import *
from utils.sx_request import *
from utils.wrappers import *
from functions.web_search import format_web_contents_batch
from functions.warroom_data_process import *
from agents.data_analyse.data_analyse_agent import DataAnalyseAgent
from agents.chat.chat_agent import ChatAgent
from agents.chat_new.chat_new_agent import ChatNewAgent
from agents.manage.manage_agent import ManageAgent

from database.redis_db import RedisDB
import traceback
import asyncio

import warnings
warnings.filterwarnings('ignore')

import ast

from utils.sx_log import sx_log, process_log

async def llm_generate(llm, prompt_template, **kwargs) -> str:
    """使用llm异步生成回答"""

    prompt = prompt_template.format(**kwargs)
    response = await llm.async_chat_by_token(prompt)
    
    return response['result']


def content_to_str(content):
    """非字符串类型内容转为字符串

    Args:
        content (Any): 任何内容

    Returns:
        String: 内容的字符串形式
    """
    if not isinstance(content, str):
        content = json.dumps(content,ensure_ascii=False)
    return content




app = FastAPI()
# todo：后续增加两个prompt，使用不同llm调用不同prompt
llm = llm_dict["wenxin"]
# llm_qa = llm_dict["wenxinchat"]
llm_qa = llm_dict["deepseekr1stream"]


class ChatWarroomAssistant:
    """沙盘对话助手
    """
    def __init__(self, websocket):
        self.websocket = websocket
        self.input_json = None
        self.userId = None
        self.sessionId = None
        self.questionText = None
        self.category = None
        self.client = None
        self.language = '英文'
        self.input_text = None
        self.intention = None
        self.kpi = None
        self.t_input = time.time()
        self.history_json = None
        # 历史问题
        self.history_question = ""
        # 意图判断变量，记录上次用户查询意图：如果上次用户查询意图与本次相同，则不进行意图判断。1：不相同；0：相同
        self.memory_intention_judge = 1
        # 历史对话
        self.history_chat = [{"role": "user", "content": "你好"},{"role": "assistant", "content": "我是上汽大众（SVW）Copilot，有什么能帮您的吗？"}]
        # 对话轮数
        self.turn = 1
        # 分布数据是是否为汇总值：'False'，汇总值；'True'，非汇总值
        self.not_total = 'False'
        # 获取日期相关信息
        self.date_info = get_date_info()

    # 接收ws输入
    async def ws_receive(self):
        self.input_json = await self.websocket.receive_json()
        self.t_input = time.time()
        self.userId = self.input_json["userId"]
        self.sessionId = self.input_json["sessionId"]
        self.questionText = self.input_json["questionText"]
        self.category = self.input_json.get("category", "")
        self.client = self.input_json.get("client", "")
        self.save_log('输入',self.questionText)


    def save_log(self,log_type,log_content,time_cost=0):
        """记录日志

        Args:
            log_type (String): 日志类型
            log_content (Any): 日志记录内容
        """
        log_content = process_log(log_content)
        sx_log.debug(f"client：{self.client} - 用户userId：{self.userId} - 问题：{process_log(self.questionText)} - 日志类型：{log_type} - 日志内容：{log_content} - 处理时间：{time_cost}")

    def history_process(self, content):
        """历史对话处理，仅保留上一次问答历史

        Args:
            content (String): 待记录的历史对话内容
        """
        content = content_to_str(content)
        if len(self.history_chat) in [2,4]:
            self.history_chat.append({"role": "user", "content": content})
        elif len(self.history_chat) in [3,5]:
            self.history_chat.append({"role": "assistant", "content": content})
        else:
            self.history_chat = self.history_chat[0:2] + self.history_chat[4:6]
            self.history_chat.append({"role": "user", "content": content})

    async def input_process(self, llm):
        """输入预处理：语言识别，问题完善，意图识别，指标提取

        Args:
            llm (Object): 大语言模型
            history_question (String)：上一个输入的问题（已进行问题完善）
        
        Returns:
            history_question (String)：结合上一个输入的问题（已进行问题完善），完善的当前的问题，替换历史问题
        """
        t1 = time.time()
        response = await llm_generate(llm=llm, prompt_template=prompt_language_question_intention_kpi, current_question=self.questionText,kpi_all=kpi_all)
        print('response', response)
        t2 = time.time()
        response = response.replace("。","")
        self.save_log('意图识别结果',response,t2-t1)

        # 处理返回值并进行初步清洗，只取前三行，防止有多余的描述
        response_parts = [line for line in response.split('\n') if line]
        self.language = response_parts[0].split('：')[1]
        self.intention = response_parts[1].split('：')[1]
        self.kpi = response_parts[2].split('：')[1]

        # 增加非英文语言处理，防止有其他语言输入时，后续代码报错
        if self.language != "中文":
            self.language = "英文"
        # 新增对问题中by SVR的处理，防止无法按照地区维度查询
        self.questionText = self.questionText.replace('by SVR','by region').replace('by svr','by region')
        self.input_text = self.questionText
        # 转化后的完整问题作为历史问题保存在history_question变量
        self.history_question = self.questionText
        # 问题中的 同比/环比 转化为同环比，防止出现 同比/环比时，后续时间提取不准
        self.input_text = self.input_text.replace('同比','同环比').replace('环比','同环比').replace('同同','同')
        # LLM没有按格式提取kpi，数据非list结构，字符串包含、
        if "[" in self.kpi:
            self.kpi = ast.literal_eval(self.kpi)
        else:
            self.kpi = self.kpi.split("、")
        self.kpi = clean_kpi(self.kpi)
        self.save_log('清洗后的指标',self.kpi)
        # 清洗后超过5个kpi，则转为未知意图查询
        if len(self.kpi) > 5:
            self.intention = '未知意图'
        # 如果为"数据查询"或"数据分析"，且清洗后指标为空，则意图变量转为0
        if (self.kpi == [] or self.kpi == [""]) and (self.intention in "数据查询"):
            self.memory_intention_judge = 1 - self.memory_intention_judge


    async def ws_return_response(self, response, is_finish=False):
        """给前端返回查询结果

        Args:
            response (List): 查询结果
        """
        await self.websocket.send_json({"answerText": response,"turn": self.turn,"answerType": "result","finished": is_finish})


    async def ws_return_response_stream(self, question):
        """流式返回

        Args:
            question (List): 含历史对话的问题

        Returns:
            String: 回复的完整内容
        """
        qa_result = await llm_qa.async_chat_by_token(question)
        tmp_result = ''
        answer_src = sx_answer_source[self.language]["LLM"]
        
        ### deepseek模型流式输出
        response = [{"type": "longText","longText": {"content": data_analysis_prefix[self.language],"ended": False}}]
        await self.ws_return_response(response,False)
        content_num = 0
        # is_thinking = True
        async for chunk in qa_result:
            try:
                received_message = await asyncio.wait_for(self.websocket.receive_json(), timeout=0.1)
                if received_message["longTextStopFlag"]:
                    break
            except asyncio.TimeoutError:
                # 火山云流式输出
                if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
                    tmp_result_value = chunk.choices[0].delta.reasoning_content
                else:
                    if content_num == 0:
                        response = [{"type": "longText","longText": {"content": data_analysis_suffix[self.language],"ended": False}}]
                        await self.ws_return_response(response,False)
                        content_num += 1
                    tmp_result_value = chunk.choices[0].delta.content
                
                response = [{"type": "longText","longText": {"content": tmp_result_value,"ended": False}}]
                await self.ws_return_response(response,False)
                tmp_result = tmp_result + tmp_result_value


        response = [{"type": "longText","longText": {"content": answer_src,"ended": True}}]
        await self.ws_return_response(response,True)
        tmp_result = tmp_result + answer_src
        return tmp_result


    async def ws_return_intention(self):
        """根据用户问题，返回问题的意图
        """
        # 输入预处理
        answerText = sx_intention_reply[self.language][self.intention]
        answerText = [{"type": "text","text": {"content": answerText}}]
        await self.websocket.send_json({"answerText": answerText,"turn": self.turn,"answerType": "message","finished": False})
        t2 = time.time()
        self.save_log('给用户返回的意图',answerText,t2-self.t_input)

    async def intent_act_data(self):
        """数据查询意图处理

        Returns:
            response (List): 回复内容
        """
        # 关键词提取
        t1 = time.time()
        response_json = await llm_generate(llm=llm, prompt_template=prompt_extract_args, today=self.date_info['today_date_cn'], question=self.questionText, 
                                          weekday=self.date_info['today_weekday'], kpi_all=kpi_all,
                                          this_year_first_day=self.date_info['this_year_first_day_date_str'], 
                                          this_year_last_day=self.date_info['this_year_last_day_date_str'])
        t2 = time.time()
        self.save_log('关键词提取json',response_json,t2-t1)

        t1 = time.time()
        # json格式化
        format_json = extract_json_from_markdown(response_json)
        # json清洗并转为list
        format_json_list = json_to_list(format_json, self.kpi)
        t2 = time.time()
        self.save_log('格式化后的Json',format_json,t2-t1)
        self.save_log('清洗后的Json列表',format_json_list,t2-t1)

        # 初始化是否最后回复变量
        is_finish = False

        if format_json_list == []:
            if self.intention == '数据查询':
                if self.language == "英文":
                    question = "请用英语回答问题：" + self.input_text
                else:
                    question = self.input_text

                parent_dir = os.path.dirname(os.path.abspath(__file__))
                file_path = os.path.join(parent_dir, 'vector_store', 'knowledge_index')
                # 知识库查询
                context = vector_index(file_path,self.input_text,top_k=5)[1]
                tmp_prompt_knowledge_search = prompt_knowledge_search.format(question=question, context=context)
                # 记录当前问题
                self.history_process(tmp_prompt_knowledge_search)
                t1 = time.time()
                tmp_result = await self.ws_return_response_stream(self.history_chat)
                t2 = time.time()
                # 对话历史记录当前回答
                self.history_process(tmp_result)
                self.save_log('json为空的历史对话记录（含当前回答）',tmp_result,t2-t1)
                response = [{"type": "text","text": {"content": tmp_result}}]
            else:
                response = []
            return response

        response = []
        return_num = 1
        for i in format_json_list:
            if return_num == len(format_json_list) and self.intention == '数据查询':
                is_finish = True
            else:
                return_num = return_num + 1

            tmp_kpi = i['kpi']
            tmp_display = i['display']
            tmp_today = i['today']

            response_kpi = []

            # 图表展示选择
            chart_type,type = chart_select(tmp_display)
            self.save_log('数据的图表类型chart_type和type',chart_type + ' ' + ' '.join(type))

            # 对比图放在一张图展示
            if tmp_display in ['对比','排名'] or (tmp_display == '漏斗' and tmp_today != "True"):
                self.save_log('多图展示类型','一次性展示')
                json_show = []
                warroom_data_list = []
                time_list = []
                for j in i['json_list']:
                    tmp_json_clean = j['json_clean']
                    tmp_kpi = json.loads(tmp_json_clean)['kpi'][0]

                    # 沙盘查询json处理
                    t1 = time.time()
                    # 对比情况不需要转化，可以去掉？
                    warroom_json = warroom_json_generate(tmp_json_clean)
                    t2= time.time()
                    self.save_log('调用数据查询接口的warroom_json',warroom_json,t2-t1)

                    # 沙盘数据查询和处理
                    t1 = time.time()
                    response_warroom_no_clean = await async_request_with_retries(warroom_url, warroom_json)
                    t2 = time.time()
                    self.save_log('数据查询接口返回response_warroom_no_clean',response_warroom_no_clean,t2-t1)

                    if response_warroom_no_clean == []:
                        continue
                    # 清洗沙盘接口查询的数据
                    t1 = time.time()
                    response_warroom = warroom_data_clean(response_warroom_no_clean)
                    t2 = time.time()
                    self.save_log('清洗后的数据查询接口返回response_warroom',response_warroom,t2-t1)

                    tmp_json_clean_obj = json.loads(tmp_json_clean)
                    start_time = tmp_json_clean_obj['start_time']
                    end_time = tmp_json_clean_obj['end_time']
                    date_type = tmp_json_clean_obj['date_type']
                    
                    # 沙盘数据只保留汇总值
                    for tmp_data in response_warroom:
                        if tmp_data['time'] == 'total':
                            if start_time == end_time:
                                tmp_data['time'] = start_time
                            else:
                                tmp_data['time'] = start_time + '-' + end_time
                            tmp_time_list = [start_time,end_time,date_type]
                            if tmp_time_list not in time_list:
                                time_list.append(tmp_time_list)
                            warroom_data_list.append(tmp_data)
                
                
                self.save_log('汇总后的数据查询结果warroom_data_list',warroom_data_list)
                if warroom_data_list == []:
                    qa_answer = sx_warroom_response_reply[self.language].format(sx_kpi_name[tmp_kpi][self.language])
                    text_show = [{"type": "text","text": {"content": qa_answer}}]
                    await self.ws_return_response(text_show,is_finish)
                else:
                    # 输出数据格式转化
                    json_show = content_show(tmp_json_clean,warroom_data_list,chart_type,type,self.language)
                    self.save_log('json_show',json_show)

                    if json_show == []:
                        qa_answer = sx_warroom_response_reply[self.language].format(sx_kpi_name[tmp_kpi][self.language])
                        text_show = [{"type": "text","text": {"content": qa_answer}}]
                        await self.ws_return_response(text_show,is_finish)
                    else:
                        qa_answer = answer_warroom_compare(time_list,tmp_kpi,self.language)
                        text_show = [{"type": "text","text": {"content": qa_answer}}]
                        await self.ws_return_response(text_show + json_show,is_finish)
                        response = response + text_show + json_show

            else:
                self.save_log('多图展示类型','分多次展示')
                tmp_count = 1
                for j in i['json_list']:
                    if tmp_count == len(i['json_list']) and is_finish == True:
                        tmp_is_finish = True
                    else:
                        tmp_is_finish = False
                        tmp_count = tmp_count + 1
                    tmp_json_clean = j['json_clean']
                    tmp_json_clean_obj = json.loads(tmp_json_clean)
                    # 如果存在多个指标，只保留数值类指标作为返回话术的指标显示
                    if len(tmp_json_clean_obj['kpi']) > 1:
                        tmp_kpi = [tmp for tmp in tmp_json_clean_obj['kpi'] if tmp not in not_main_kpi][0]
                    else:
                        tmp_kpi = tmp_json_clean_obj['kpi'][0]
                    t1 = time.time()
                    warroom_json = warroom_json_generate(tmp_json_clean)
                    t2= time.time()
                    self.save_log('调用数据查询接口的warroom_json',warroom_json,t2-t1)

                    # 沙盘数据查询和处理
                    t1 = time.time()
                    response_warroom_no_clean = await async_request_with_retries(warroom_url, warroom_json)
                    t2 = time.time()
                    self.save_log('数据查询接口返回response_warroom_no_clean',response_warroom_no_clean,t2-t1)

                    if response_warroom_no_clean == []:
                        continue
                    else:
                        # 清洗沙盘接口查询的数据
                        t1 = time.time()
                        response_warroom = warroom_data_clean(response_warroom_no_clean)
                        t2 = time.time()
                        self.save_log('清洗后的数据查询接口返回response_warroom',response_warroom,t2-t1)
                        if response_warroom == []:
                            continue

                        # 输出数据格式转化
                        json_show = content_show(tmp_json_clean,response_warroom,chart_type,type,self.language)
                        # 如果查询数据为空
                        if json_show == []:
                            continue
                        # 如果查询不为空
                        else:
                            qa_answer = answer_warroom(tmp_json_clean,tmp_kpi,self.language)
                            text_show = [{"type": "text","text": {"content": qa_answer}}]
                            await self.ws_return_response(text_show + json_show,tmp_is_finish)
                            response_kpi = response_kpi + text_show + json_show
                            response = response + text_show + json_show

                # 如果某个指标都查不到数据，就返回默认话术
                if response_kpi == []:
                    qa_answer = sx_warroom_response_reply[self.language].format(sx_kpi_name[tmp_kpi][self.language])
                    text_show = [{"type": "text","text": {"content": qa_answer}}]
                    await self.ws_return_response(text_show,is_finish)
        
        if response != []:
            # 对话历史记录当前问题
            self.history_process(self.input_text)
            # 对话历史记录当前回答
            self.history_process(response)

        return response

    async def intent_act_data_analysis(self, response, web_contents, warroom_data, is_internet=True):
        # 提取当前显示数据
        response_data = [i for i in response if i["type"] != "text"]
        if self.language == "英文":
            question = "请用英语回答问题：" + self.questionText
        else:
            question = self.questionText
        if is_internet:
            # web_contents = await DataAnalyseAgent.execute_full_flow(self.questionText)
            # web_contents = get_web_contents(self.questionText)
            formatted_contents = format_web_contents_batch(web_contents)
        else:
            formatted_contents = ''
        tmp_prompt_data_analysis = prompt_data_analysis.format(question=question, data=response_data, warroom_data=warroom_data, web_contents=formatted_contents, today=self.date_info['today_date_cn'])
        self.save_log('数据分析prompt',tmp_prompt_data_analysis)
        self.history_process(tmp_prompt_data_analysis)
        t1 = time.time()
        tmp_result = await self.ws_return_response_stream(self.history_chat)
        t2 = time.time()
        self.history_process(tmp_result)
        self.save_log('数据分析结果',tmp_result,t2-t1)
        response = [{"type": "text","text": {"content": tmp_result}}]
        self.save_log('数据分析最终结果',response,t2-t1)
        return response


    async def intent_act_ui(self):
        #调用UI交互接口
        chain_time_filter = LLMChain(llm=llm, prompt=prompt_ui)
        response_json = chain_time_filter.run(today=self.date_info['today_date_cn'], question=self.input_text)
        formatted_json = extract_and_clean_json_ui(response_json)
        if formatted_json != '{}':
            formatted_json = json.loads(formatted_json)
            action = formatted_json["action"]
            if action == "筛选":
                start_time = formatted_json["start_time"]
                end_time = formatted_json["end_time"]
                response = [{"type": "command","command": {"command_type": "time_filter","start_time": start_time,"end_time": end_time}}]
            else:
                response = [{"type": "command","command": {"command_type": "back_to_home"}}]
                
        else:
            response = [{"type": "text","text": {"content": "Can't respond to the question '" + self.input_text + "'"}}]
        await self.ws_return_response(response,True)
        return response


    # 一般问答处理
    async def intent_act_qa(self):

        if self.language == "英文":
            question = "请用英语回答问题：" + self.input_text
        else:
            question = self.input_text
        self.save_log('一般QA问答的问题',question)
        parent_dir = os.path.dirname(os.path.abspath(__file__))
        file_path = os.path.join(parent_dir, 'vector_store', 'knowledge_index')
        # 知识库查询
        context = vector_index(file_path,self.input_text,top_k=5)[1]
        tmp_prompt_knowledge_search = prompt_knowledge_search.format(question=question, context=context)
        # 记录当前问题
        self.history_process(tmp_prompt_knowledge_search)
        t1 = time.time()
        tmp_result = await self.ws_return_response_stream(self.history_chat)
        t2 = time.time()
        #记录当前问题回复
        self.history_process(tmp_result)
        self.save_log('一般QA问答的当前回答',tmp_result,t2-t1)
        response = [{"type": "text","text": {"content": tmp_result}}]
        self.save_log('一般QA问答的历史对话（含当前回答）',self.history_chat,t2-t1)
        return response


    async def intent_act(self):
        """根据不同意图进行处理

        Returns:
            response (List): 问题的回答
        """
        if "数据查询" in self.intention:
            response = await self.intent_act_data()
        elif "数据分析" in self.intention:
            response, web_contents, warroom_data = await asyncio.gather(
                self.intent_act_data(),
                DataAnalyseAgent.get_web_contents_agent(self.questionText),
                DataAnalyseAgent.get_warroom_data_agent(self.questionText)
            )
            await self.intent_act_data_analysis(response, web_contents, warroom_data)
        elif "UI交互" in self.intention:
            response = await self.intent_act_ui()
        else:
            response = await self.intent_act_qa()
        t_result = time.time()
        self.save_log('问题最终回答',response,t_result - self.t_input)


@app.websocket("/api/V1/chat/ws/new")
async def stream_chat_new(websocket: WebSocket):
    await websocket.accept()
    agent = ChatAgent(websocket)
    while True:
        try:
            await agent.get_input()
            await agent.classify_question()
            await agent.decompose_question()
            if agent.is_data_question == '是':
                await agent.ordered_stream()
                await agent.get_analysis_result()
            else:
                await agent.get_external_information_non_queue()
                await agent.get_analysis_result()

        # 捕获异常，返回异常回复
        except Exception as e:
            t_result = time.time()
            e_msg = traceback.format_exc()
            agent.save_log('异常捕获',e_msg,t_result - agent.input_time)
            answerText = error_reply[agent.language]
            await agent.ws_return_content(answerText, content_type="text", ended=True)
    

@app.route("/conversation/chat", methods=['POST'])
async def chat(request: Request):
    try:
        print(request.headers)
        agent = ManageAgent()
        await agent.get_input(request)
        return StreamingResponse(
            agent.event_stream(),
            media_type="text/event-stream"
        )
    # 捕获异常，返回异常回复
    except Exception as e:
        t_result = time.time()
        e_msg = traceback.format_exc()
        agent.save_log('异常捕获',e_msg,t_result - agent.input_time)
        # 返回错误响应
        error_message = error_reply.get(agent.language if agent else 'default')
        raise HTTPException(
            status_code=500,
            detail=error_message
        )


@app.route("/feedback/operate", methods=['POST'])
async def feedback_operate(request: Request):
    request_json = await request.json()
    message_id = request_json.get('message_id')
    feedback_type = request_json.get('feedback_type')
    feedback_content = request_json.get('feedback_content')
    feedback_message = {"feedback_type": feedback_type, "feedback_content": feedback_content}
    redis_db = RedisDB()
    try:
        await redis_db.connect()
        await redis_db.set_key_value(f'message:{message_id}', json.dumps(feedback_message,ensure_ascii=False))
        return JSONResponse(
            content={"message": True},
            status_code=200)
    except Exception as e:
        # 记录错误并返回适当的错误响应
        return JSONResponse(
            content={"error": f"点赞点踩操作失败: {str(e)}"},
            status_code=500
        )
    finally:
        await redis_db.close()


async def get_conversation_all_history(conversation_id):
    key = f"conversation:{conversation_id}"
    redis_db = RedisDB()
    result = []
    try:
        await redis_db.connect()
        history_all = await redis_db.get_history_all(key)
        for item in history_all:
            item_dict = json.loads(item)
            if 'message_id' in item_dict:
                message_id = item_dict['message_id']
                if await redis_db.get_key(f'message:{message_id}') is not None:
                    feedback_message = json.loads(await redis_db.get_key(f'message:{message_id}'))
                    merged_dict = {**item_dict, **feedback_message}
                    result.append(json.dumps(merged_dict, ensure_ascii=False))
                else:
                    merged_dict = {**item_dict, **{'feedback_type': None, 'feedback_content': None}}
                    result.append(json.dumps(merged_dict, ensure_ascii=False))
            else:
                result.append(item)
        
        return result
        
    except Exception as e:
        # 记录错误并返回适当的错误响应
        e_msg = traceback.format_exc()
        raise HTTPException(
            status_code=500,
            detail=f"获取历史记录失败: {e_msg}"
        )
    finally:
        await redis_db.close()
    

@app.route("/conversation/history", methods=['GET'])
async def get_conversation_history(request: Request):
    conversation_id = request.query_params.get('conversation_id')
    try:
        result = await get_conversation_all_history(conversation_id)
        return JSONResponse(
        content={"history": result},
        status_code=200
    )
    except Exception as e:
        # 记录错误并返回适当的错误响应
        e_msg = traceback.format_exc()
        raise HTTPException(
            status_code=500,
            detail=f"获取历史记录失败: {e_msg}"
        )


def get_conversation_history_by_message_ids(all_conversation_history, message_ids):
    """
    根据消息ID列表查找对应的对话历史

    Args:
        data (list): 全部对话历史。
        message_ids (list): 要查询的消息ID列表。

    Returns:
        list: 对应消息ID的对话历史。
    """
    all_conversation_history = [json.loads(item) for item in all_conversation_history]
    
    assistant_messages_map = {
        msg['message_id']: msg
        for msg in all_conversation_history 
        if 'message_id' in msg
    }

    user_map = {
        msg['order']: msg 
        for msg in all_conversation_history 
        if msg.get('role') == 'user' and 'message_id' not in msg
    }

    results = []
    for message_id in message_ids:
        
        assistant_message = assistant_messages_map.get(message_id)
        
        if not assistant_message:
            continue
        
        order = assistant_message.get('order')
        if order and order >= 2:
            user_message = user_map.get((order - 1))
            if user_message:
                found_pair = [user_message, assistant_message]
        results.extend(found_pair)

    return results

def create_share_id():
    import uuid, base62
    uuid_as_int = uuid.uuid4().int
    share_id = base62.encode(uuid_as_int)
    return share_id

@app.route("/conversation/share/create", methods=['POST'])
async def create_share_conversation(request: Request):
    share_id = create_share_id()
    redis_db = RedisDB()
    request_json = await request.json()
    conversation_id = request_json.get('conversation_id')
    message_ids = request_json.get('message_ids')
    watermark = request_json.get('watermark')
    print('request_json', request_json)
    print('conversation_id', conversation_id)
    print('message_ids', message_ids)
    
    try:
        all_history = await get_conversation_all_history(conversation_id)
        print('all_history', all_history)
        result = get_conversation_history_by_message_ids(all_history, message_ids)
        content = {"watermark": watermark, "history": result, "time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())}
        print('content',content)
        await redis_db.connect()
        await redis_db.set_key_value(f'share:{share_id}', json.dumps(content,ensure_ascii=False))
        
        return JSONResponse(
        content={"share_id": share_id},
        status_code=200
    )
    except Exception as e:
        # 记录错误并返回适当的错误响应
        e_msg = traceback.format_exc()
        return JSONResponse(
            content={"error": f"创建分享对话历史记录失败: {e_msg}"},
            status_code=500
        )
    finally:
        await redis_db.close()


@app.route("/conversation/share/query", methods=['GET'])
async def query_share_conversation(request: Request):

    share_id = request.query_params.get('share_id')
    key = f"share:{share_id}"
    redis_db = RedisDB()
    try:
        await redis_db.connect()
        share_conversation = await redis_db.get_key(key)
        if share_conversation:
            print('share_conversation', share_conversation)
            share_conversation = json.loads(share_conversation)
            # 增加有效期处理
            stored_time = datetime.datetime.strptime(share_conversation['time'], "%Y-%m-%d %H:%M:%S")
            expire_time = stored_time + datetime.timedelta(minutes=30)
            current_time = datetime.datetime.now()
            if current_time > expire_time:
                content = {}
            else:
                content = {key: value for key, value in share_conversation.items() if key != 'time'}
        else:
            content = {}
        return JSONResponse(
        content=content,
        status_code=200
    )
    except Exception as e:
        # 记录错误并返回适当的错误响应
        e_msg = traceback.format_exc()
        return JSONResponse(
            content={"error": f"获取分析对话历史记录失败: {e_msg}"},
            status_code=500
        )
    finally:
        await redis_db.close()



@app.route("/user/history", methods=['GET'])
async def get_user_history(request: Request):
    user_id = request.query_params.get('user_id')
    key = f"user:{user_id}"
    redis_db = RedisDB()
    try:
        await redis_db.connect()
        history_all = await redis_db.get_history_all(key)
        return JSONResponse(
        content={"history": history_all},
        status_code=200
    )
    except Exception as e:
        # 记录错误并返回适当的错误响应
        return JSONResponse(
            content={"error": f"获取历史记录失败: {str(e)}"},
            status_code=500
        )
    finally:
        await redis_db.close()



@app.websocket("/api/V1/chat/ws")
async def stream_chat(websocket: WebSocket):
    """提供给沙盘的ws接口，用于进行问题回复

    Args:
        websocket (WebSocket): websocket连接
    """


    # 初始化一次对话实例
    chat_ass = ChatWarroomAssistant(websocket)
    # 建立ws连接
    await websocket.accept()
    await chat_ass.ws_return_response("我是AI大模型助手，有什么能帮您的吗？")
    while True:
        try:
            # 解析前端输入
            await chat_ass.ws_receive()
            # ws输入处理
            await chat_ass.input_process(llm)
            # 如果意图变量为0，则不进行后续处理，结束单次对话，让用户输入查询指标
            if chat_ass.memory_intention_judge == 0:
                chat_ass.memory_intention_judge = 1 - chat_ass.memory_intention_judge
                content = sx_guide_reply[chat_ass.language]
                response = [{"type": "text","text": {"content": content}}]
                await chat_ass.ws_return_response(response,True)
            else:
                # 返回问题类型
                await chat_ass.ws_return_intention()
                # 返回最终答案
                await chat_ass.intent_act()

        # 捕获异常，返回异常回复
        except Exception as e:
            t_result = time.time()
            e_msg = traceback.format_exc()
            chat_ass.save_log('异常捕获',e_msg,t_result - chat_ass.t_input)
            answerText = [{"type": "text","text": {"content": error_reply[chat_ass.language]}}]
            await chat_ass.ws_return_response(answerText,True)
        chat_ass.turn += 1



if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, workers=1, ws_ping_interval=15, ws_ping_timeout=5, log_level="debug")