import asyncio
from universal_async_client import AsyncApiClient
import time

async def parallel_requests_demo():
    # 创建客户端实例
    client = AsyncApiClient(
        base_url="http://10.122.31.36:8080/report/mobile/wechat",
        auth_token="SYAAkjENRNIAFVoQKylHQ51nZRNeX12-"
    )
    
    print("开始执行并行请求...")
    start_time = time.time()
    
    # 定义多个请求任务
    async def task1():
        # 第一个月份的数据
        result = await client.request(
            method="GET",
            endpoint="sv/afterSaleJyController/queryJyIndex",
            params={"yearMonth": "202301", "queryType": "M"},
            debug=False  # 设置为True会显示详细日志
        )
        return {"name": "一月数据", "result": result}
    
    async def task2():
        # 第二个月份的数据
        result = await client.request(
            method="GET",
            endpoint="sv/afterSaleJyController/queryJyIndex",
            params={"yearMonth": "202302", "queryType": "M"},
            debug=False
        )
        return {"name": "二月数据", "result": result}
    
    async def task3():
        # 第三个月份的数据
        result = await client.request(
            method="GET",
            endpoint="sv/afterSaleJyController/queryJyIndex",
            params={"yearMonth": "202303", "queryType": "M"},
            debug=False
        )
        return {"name": "三月数据", "result": result}
    
    async def task4():
        # POST请求示例
        result = await client.request(
            method="POST",
            endpoint="sv/afterSaleJyController/somePostEndpoint",  # 替换为实际的POST端点
            json_data={"key1": "value1", "key2": "value2"},
            debug=False
        )
        return {"name": "POST请求", "result": result}
    
    # 使用gather并行执行所有任务
    results = await asyncio.gather(
        task1(),
        task2(),
        task3(),
        task4(),
        return_exceptions=True  # 允许单个任务失败而不影响其他任务
    )
    
    # 处理结果
    for i, result in enumerate(results, 1):
        if isinstance(result, Exception):
            print(f"任务{i}失败: {result}")
        else:
            print(f"任务{i} - {result['name']}完成")
            print(f"结果: {result['result']}")
            print("-" * 30)
    
    end_time = time.time()
    print(f"所有请求完成，总耗时: {end_time - start_time:.2f}秒")

    # 另一种批量请求方式 - 使用列表推导式动态生成多个请求
    print("\n批量请求示例...")
    
    # 准备多个月份的参数
    months = ["202301", "202302", "202303", "202304", "202305"]
    
    # 使用列表推导创建请求任务
    tasks = [
        client.request(
            method="GET",
            endpoint="sv/afterSaleJyController/queryJyIndex",
            params={"yearMonth": month, "queryType": "M"},
            debug=False
        )
        for month in months
    ]
    
    # 并行执行
    batch_start = time.time()
    batch_results = await asyncio.gather(*tasks, return_exceptions=True)
    batch_end = time.time()
    
    # 处理结果
    for i, (month, result) in enumerate(zip(months, batch_results), 1):
        if isinstance(result, Exception):
            print(f"月份 {month} 请求失败: {result}")
        else:
            print(f"月份 {month} 请求成功: {result}")
    
    print(f"批量请求完成，总耗时: {batch_end - batch_start:.2f}秒")
    
    # 演示如何处理混合请求类型（GET和POST）
    print("\n混合类型请求示例...")
    
    # 创建不同类型的请求
    mixed_tasks = [
        # GET请求
        client.request("GET", "sv/afterSaleJyController/queryJyIndex", 
                      params={"yearMonth": "202301", "queryType": "M"}),
        # POST请求 - 表单数据
        client.request("POST", "sv/afterSaleJyController/somePostEndpoint",
                      data={"field1": "value1"}),
        # POST请求 - JSON数据
        client.request("POST", "sv/afterSaleJyController/anotherEndpoint", 
                      json_data={"key1": "value1"})
    ]
    
    # 执行混合请求
    mixed_results = await asyncio.gather(*mixed_tasks, return_exceptions=True)
    
    # 输出结果
    request_types = ["GET查询", "POST表单", "POST JSON"]
    for req_type, result in zip(request_types, mixed_results):
        print(f"{req_type} 结果: {result}")

if __name__ == "__main__":
    asyncio.run(parallel_requests_demo()) 