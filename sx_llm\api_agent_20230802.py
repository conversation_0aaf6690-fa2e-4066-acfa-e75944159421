from langchain.agents import Tool, AgentExe<PERSON>or, BaseMultiActionAgent, AgentType
from langchain.memory import ConversationBufferMemory
from langchain.agents import initialize_agent
from langchain.llms import ChatGLM
from langchain.agents import tool
import requests
import json
from llm import *
from langchain.agents import Tool, AgentExecutor, BaseSingleActionAgent
from langchain.schema import AgentAction, AgentFinish, OutputParserException, HumanMessage
from langchain.prompts import StringPromptTemplate, BaseChatPromptTemplate
from langchain.agents import Tool, AgentExecutor, LLMSingleActionAgent, AgentOutputParser
from langchain.agents.conversational.base import ConversationalAgent
import re
from typing import List, Tuple, Any, Union
import langchain
from sx_prompt import *
import smtplib
from email.mime.text import MIMEText
from pydantic import BaseModel, Field
from langchain.tools import StructuredTool
from typing import Optional, Type
from langchain.tools import BaseTool, StructuredTool, Tool, tool

from langchain.callbacks.manager import (
    AsyncCallbackManagerForToolRun,
    CallbackManagerForToolRun,
)


langchain.debug = True

@tool
def post_message(query: str) -> str:
    """Sends a POST request to the given url with the given body and parameters."""
    url = "http://*************:8080/api/V1/chat/embedding/json"
    headers = {"Content-Type": "application/json"}
    data = {
            "userId": "11",
            "sessionId": "11",
            "prompt": "prompt_V6",
            "llm": "wenxin",
            "questionText": query
        }
    result = requests.post(url, headers=headers,data=json.dumps(data)).json()
    return result["answerText"]

@tool
def post_message_realtime() -> str:
    """Sends a POST request to the given url with the given body and parameters."""
    url = "http://dev-ngaswagger.svwsx.cn/vd-report/api/board/vdv2/conversion/statisticNumber"
    headers = {"Content-Type": "application/json", "reqsource": "wr-ai"}
    data = {
        "dateValue":"202308,202308",
        "orgIdList":["VWSA"],
        "seriesIds": None,
        "includeToday":True
        }
    result = requests.post(url, headers=headers,data=json.dumps(data)).json()
    result = result["retData"]

    leads_cnt = result["todayLeadsNotDuplicateCnt"]
    opp_cnt = result["todayOppCnt"]
    walkin_cnt = result["todayOppWalkinCnt"]
    testdrive_cnt = result["todayOppTestdriveCnt"]
    order_cnt = result["todayOrderCnt"]
    invoice_cnt = result["todayDeliveryEinvoiceCnt"]

    result_json = {
        "线索":leads_cnt,
        "潜客":opp_cnt,
        "到店":walkin_cnt,
        "试驾":testdrive_cnt,
        "订单":order_cnt,
        "发票":invoice_cnt
    }
    # result_json = json.dumps(result_json)

    return result_json



# class MessageInput(BaseModel):
#     message: str = Field(description="需要发送邮件的正文内容")
#     mail_to: str = Field(description="需要发送邮件的邮箱")

def mail_send(message: str, mail_to: str) -> str:
    """提供邮件发送所需的正文内容message，和接收邮件的邮箱mail_to，并发送邮件"""
    
    print(mail_to)
    print(message)
    print("*********")

    smtp_server = 'itpoc.csvw.com'
    smtp_username = '<EMAIL>'
    smtp_password = 'Lk19941021k'

    smtp_client = smtplib.SMTP(smtp_server)
    smtp_client.starttls()
    smtp_client.login(smtp_username, smtp_password)

    msg = MIMEText(message)
    msg['Subject'] = '数据异常通知'
    msg['From'] = '<EMAIL>'
    msg['To'] = mail_to

    smtp_client.sendmail(msg['From'], msg['To'], msg.as_string())
    smtp_client.quit()
    return "邮件发送成功"





class CustomMailSendTool(BaseTool):
    name = "邮件发送"
    description = "这是一个邮件发送工具，当需要发送邮件时，传入邮件内容正文和要发送的邮箱，即可发送邮件"
    # args_schema: Type[BaseModel] = MessageInput

    def _run(
        self, message: str, mail_to: str, run_manager: Optional[CallbackManagerForToolRun] = None
    ) -> str:
        """Use the tool."""
        return mail_send(message,mail_to)

    async def _arun(
        self, message: str, mail_to: str, run_manager: Optional[AsyncCallbackManagerForToolRun] = None
    ) -> str:
        """Use the tool asynchronously."""
        raise NotImplementedError("邮件发送不支持异步")



tools = [
    Tool(
        name="数据查询",
        func=post_message,
        description="这是一个查询销量的工具，如果涉及到销量的问题，请使用此工具进行查询，并返回答案",
    ),
    Tool(
        name="实时数据查询",
        func=post_message_realtime,
        description="这是一个实时数据查询的工具，如果需要查询今日数据，请使用此工具进行查询，并返回答案",
    ),
    Tool(
        name="邮件发送",
        func=CustomMailSendTool().run,
        description="这是一个邮件发送工具，当需要发送邮件时，可以使用此工具进行邮件发送",
    ),
    ]



# Set up the base template
template = """你是SX人工智能助手，请根据用户输入的问题，进行简洁专业的回答，不允许添加任何虚假的内容，推理过程可以使用下述工具(工具使用三个反引号分隔)：

```{tools}```

问题推理过程可以采取下面的步骤和格式，每个步骤包含关键字"Question，Thought，Action，Action Input，Observation，Thought，Final Answer"：

Question: 你需要回答的问题
Thought: 你需要考虑应该做什么
Action: 应该执行的行动，应该使用[{tool_names}]中的一个工具
Action Input: 行动的输入内容
Observation: 行动的输出结果
...(其中 Thought/Action/Action Input/Observation 可以重复很多次)
Thought: 当行动输出结果可以回答原始问题时，停止上述过程，并返回最终答案
Final Answer: 输入的原始问题的最终答案

开始！给出你的最终答案。

Question: {input}
{agent_scratchpad}"""




# Set up a prompt template
class CustomPromptTemplate(StringPromptTemplate):
    # The template to use
    template: str
    # The list of tools available
    tools: List[Tool]

    def format(self, **kwargs) -> str:
        # Get the intermediate steps (AgentAction, Observation tuples)
        # Format them in a particular way
        intermediate_steps = kwargs.pop("intermediate_steps")
        thoughts = ""
        for action, observation in intermediate_steps:
            thoughts += action.log
            thoughts += f"\nObservation: {observation}\nThought: "
        # Set the agent_scratchpad variable to that value
        kwargs["agent_scratchpad"] = thoughts
        # Create a tools variable from the list of tools provided
        kwargs["tools"] = "\n".join([f"{tool.name}: {tool.description}" for tool in self.tools])
        # Create a list of tool names for the tools provided
        kwargs["tool_names"] = ", ".join([tool.name for tool in self.tools])
        return self.template.format(**kwargs)



prompt = CustomPromptTemplate(
    template=template,
    tools=tools,
    # This omits the `agent_scratchpad`, `tools`, and `tool_names` variables because those are generated dynamically
    # This includes the `intermediate_steps` variable because that is needed
    input_variables=["input", "intermediate_steps"]
)

class CustomOutputParser(AgentOutputParser):
    
    def parse(self, llm_output: str) -> Union[AgentAction, AgentFinish]:
        # Check if agent should finish
        if "Final Answer:" in llm_output:
            return AgentFinish(
                # Return values is generally always a dictionary with a single `output` key
                # It is not recommended to try anything else at the moment :)
                return_values={"output": llm_output.split("Final Answer:")[-1].strip()},
                log=llm_output,
            )
        # Parse out the action and action input
        regex = r"Action\s*\d*\s*:(.*?)\nAction\s*\d*\s*Input\s*\d*\s*:[\s]*(.*)"
        match = re.search(regex, llm_output, re.DOTALL)
        if not match:
            raise ValueError(f"Could not parse LLM output: `{llm_output}`")
        action = match.group(1).strip()
        action_input = match.group(2)
        # Return the action and action input
        return AgentAction(tool=action, tool_input=action_input.strip(" ").strip('"'), log=llm_output)

output_parser = CustomOutputParser()

memory = ConversationBufferMemory(memory_key="chat_history", return_messages=True)

# chatglm_endpoint_url = ("http://*************:9000")
# llm = ChatGLM(endpoint_url=chatglm_endpoint_url,temperature = 0)
llm = WenxinLLM()
llm_chain = LLMChain(llm=llm, prompt=prompt)
tool_names = [tool.name for tool in tools]


agent = LLMSingleActionAgent(llm_chain=llm_chain,output_parser=output_parser,stop=["\nFinal Answer:"],allowed_tools=tool_names)
agent_executor = AgentExecutor.from_agent_and_tools(agent=agent, tools=tools, verbose=True, memory=memory)
response = agent_executor.run("请查询今天新增线索数量和潜客数量，并拟一封邮件发送给****************")
print(response)


# agent_chain = initialize_agent(tools, llm, agent=AgentType.CONVERSATIONAL_REACT_DESCRIPTION, verbose=True,
#                                 memory=memory,prefix=PREFIX,suffix=SUFFIX,format_instructions=FORMAT_INSTRUCTIONS)
# response = agent_chain.run(input="请查询23年帕萨特销量，并给相关负责人发邮件")
# print(response)

# response = agent_chain.run(input="我的上一个问题是什么")
# print(response)


# import openai
# os.environ["OPENAI_API_KEY"] = '***************************************************'
# openai.api_key = '***************************************************'
# from langchain.agents import OpenAIFunctionsAgent
# from langchain.prompts import MessagesPlaceholder
# from langchain.schema import SystemMessage
# # system_message = SystemMessage(content="You are very powerful assistant, but bad at calculating lengths of words.")
# # prompt = OpenAIFunctionsAgent.create_prompt(system_message=system_message)
# from langchain.chat_models import ChatOpenAI
# llm = ChatOpenAI(temperature=0,model_name="gpt-3.5-turbo")
# MEMORY_KEY = "chat_history"
# prompt = OpenAIFunctionsAgent.create_prompt(
#     extra_prompt_messages=[MessagesPlaceholder(variable_name=MEMORY_KEY)]
# )
# agent = OpenAIFunctionsAgent(llm=llm, tools=tools, prompt=prompt)
# from langchain.agents import AgentExecutor
# agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)
# response = agent_executor.run(input="2023年2月帕萨特销量")
# print(response)


# ai_prefix = "SX人工智能助手"
# human_prefix = "用户"
# agent = ConversationalAgent.from_llm_and_tools(llm=llm,tools=tools,output_parser=output_parser,
#                                                prefix=PREFIX,suffix=SUFFIX,memory=memory,format_instructions=FORMAT_INSTRUCTIONS)
# agent_chain = AgentExecutor.from_agent_and_tools(agent=agent, tools=tools, verbose=True,memory=memory)
# response = agent_chain.run(input="2023年2月帕萨特销量")
# print(response)


# response = agent_chain.run(input="我的上一个问题是什么")
# print(response)


# response = post_message("23年4月朗逸华北大区销量和同环比")
# print(response)

# response = mail_send("2023年帕萨特销量为**190,973**辆","<EMAIL>")
# print(response)



# def warroom_post() -> str:
#     url = "http://*************:28771/robot/v1/open/adapter/vw/listCompetitionAnalyseByModelBrand"
#     headers = {"Content-Type": "application/json"}
#     data = {"year":"2023","month":"2","rssc_cn":"","model_name_cn":"Lavida","intention":["销量","同比"]}
#     # result = requests.post(url, headers=headers,data=json.dumps(data)).json()
#     result = requests.post(url, headers=headers,data=json.dumps(data)).json()
#     return result["data"][0]

# response = warroom_post()
# print(response)