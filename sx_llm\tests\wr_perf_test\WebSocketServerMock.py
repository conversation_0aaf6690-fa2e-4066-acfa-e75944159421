from fastapi import FastAPI, WebSocket
import time
import asyncio
import random

app = FastAPI()

class ChatWarroomAssistant:
    """沙盘对话助手
    """
    def __init__(self, websocket):
        self.websocket = websocket
        self.input_json = None
        self.userId = None
        self.sessionId = None
        self.questionText = None
        self.category = None
        self.client = None
        self.language = '英文'
        self.input_text = None
        self.intention = None
        self.kpi = None
        self.t_input = time.time()
        self.history_json = None
        # 历史问题
        self.history_question = ""
        # 意图判断变量，记录上次用户查询意图：如果上次用户查询意图与本次相同，则不进行意图判断。1：不相同；0：相同
        self.memory_intention_judge = 1
        # 历史对话
        self.history_chat = [{"role": "user", "content": "你好"},{"role": "assistant", "content": "我是上汽大众（SVW）Copilot，有什么能帮您的吗？"}]
        # 对话轮数
        self.turn = 1
        # 分布数据是是否为汇总值：'False'，汇总值；'True'，非汇总值
        self.not_total = 'False'
        # 获取日期相关信息
        self.date_info = None

    # 接收ws输入
    async def ws_receive(self):
        self.input_json = await self.websocket.receive_json()
        self.t_input = time.time()
        self.userId = self.input_json["userId"]
        self.sessionId = self.input_json["sessionId"]
        self.questionText = self.input_json["questionText"]
        self.category = self.input_json.get("category", "")
        self.client = self.input_json.get("client", "")
        # self.save_log('输入',self.questionText)

    async def ws_return_response(self, response, is_finish=False):
        """给前端返回查询结果

        Args:
            response (List): 查询结果
        """
        await self.websocket.send_json({"answerText": response,"turn": self.turn,"answerType": "result","finished": is_finish})

@app.websocket("/api/V1/chat/ws")
async def stream_chat(websocket: WebSocket):
    """提供给沙盘的ws接口，用于进行问题回复

    Args:
        websocket (WebSocket): websocket连接
    """


    # 初始化一次对话实例
    chat_ass = ChatWarroomAssistant(websocket)
    # 建立ws连接
    await websocket.accept()
    await chat_ass.ws_return_response("我是AI大模型助手，有什么能帮您的吗？")
    while True:
        try:
            # 解析前端输入
            await chat_ass.ws_receive()
            await asyncio.sleep(2)
            response = [{"type": "text","text": {"content": "回答回答回答回答回答回答回答"}}]
            await chat_ass.ws_return_response(response,True)
            # ws输入处理
            # chat_ass.input_process(llm)
            # 如果意图变量为0，则不进行后续处理，结束单次对话，让用户输入查询指标
            # if chat_ass.memory_intention_judge == 0:
            #     chat_ass.memory_intention_judge = 1 - chat_ass.memory_intention_judge
            #     content = sx_guide_reply[chat_ass.language]
            #     response = [{"type": "text","text": {"content": content}}]
            #     await chat_ass.ws_return_response(response,True)
            # else:
            #     # 返回问题类型
            #     await chat_ass.ws_return_intention()
            #     # 返回最终答案
            #     await chat_ass.intent_act()

        # 捕获异常，返回异常回复
        except Exception as e:
            # t_result = time.time()
            # e_msg = traceback.format_exc()
            # chat_ass.save_log('异常捕获',e_msg,t_result - chat_ass.t_input)
            answerText = [{"type": "text","text": {"content": "抱歉，没有听清您的问题，麻烦您再说一次"}}]
            await chat_ass.ws_return_response(answerText,True)
        chat_ass.turn += 1

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, workers=1, ws_ping_interval=15, ws_ping_timeout=5, log_level="debug")