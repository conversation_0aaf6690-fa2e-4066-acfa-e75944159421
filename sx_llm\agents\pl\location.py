import os
import time
import json
import requests

import sys
# 添加上上层目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))


def get_all_location(org_id: str = 'VWSA'):
    url = "http://dev-ngaswagger.svwsx.cn/vd-report/api/basic/listProvincesAsTree"
    # url = "http://172.20.142.135:8776/vd-report/api/basic/listProvincesAsTree"
    # url = "http://172.20.143.184:30805/vd-report/api/basic/listProvincesAsTree"
    headers = {"reqSource": "wr-ai", "Content-Type": "application/json"}
    params = {
        "orgId": org_id
    }
    response = requests.get(url, params=params, headers=headers)
    data = response.json()['retData']
    return data


def extract_and_format_data(data):
    """
    从嵌套字典中递归地提取和格式化数据。

    参数:
      data: 要处理的字典或列表。

    返回:
      一个字典列表，每个字典都包含 'id'、'code'、'name'、'nameEn' 和 'status' 键。
    """
    results = []

    def recurse(item):
        if isinstance(item, dict):
            # 检查当前字典中是否包含所有必需的键
            if all(key in item for key in ['id', 'code', 'name', 'nameEn', 'status']):
                # 将格式化后的字典追加到结果列表中
                results.append({
                    "id": item.get("id"),
                    "code": item.get("code"),
                    "name": item.get("name"),
                    "nameEn": item.get("nameEn"),
                    "status": item.get("status")
                })

            # 如果存在 'children'，则递归处理
            if "children" in item and isinstance(item["children"], list):
                for child in item["children"]:
                    recurse(child)

        elif isinstance(item, list):
            # 如果是列表，则遍历其中每个元素
            for i in item:
                recurse(i)

    if data:
        recurse(data)

    return results






def get_data_with_cache(original_data, cache_file='./cache/data_cache.json', validity_days=1):
    """
    带有缓存功能的数据获取函数。

    参数:
      original_data: 原始的、未经处理的数据。
      cache_file (str): 用于存储缓存数据的文件名。
      validity_days (int): 缓存的有效期（天数）。
    """
    validity_seconds = validity_days * 24 * 60 * 60  # 将天数转换为秒
    
    # 检查缓存文件是否存在且有效
    if os.path.exists(cache_file):
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # 检查时间戳是否过期
            time_elapsed = time.time() - cache_data.get('timestamp', 0)
            if time_elapsed < validity_seconds:
                print(f"缓存有效，直接从 '{cache_file}' 读取数据。")
                return cache_data['data']
            else:
                print("缓存已过期，需要重新获取数据。")

        except (json.JSONDecodeError, KeyError):
            # 如果文件损坏或格式不正确，则重新获取
            print("缓存文件格式错误，需要重新获取数据。")

    # 如果缓存不存在或已过期，则处理新数据
    print("正在处理新数据...")
    formatted_data = extract_and_format_data(original_data)
    
    # 将新数据和当前时间戳存入缓存文件
    new_cache = {
        'timestamp': time.time(),
        'data': formatted_data
    }
    
    with open(cache_file, 'w', encoding='utf-8') as f:
        json.dump(new_cache, f, indent=2, ensure_ascii=False)
    
    print(f"新数据已处理完毕并存入缓存 '{cache_file}'。")
    return formatted_data






all_location = get_all_location()
results = extract_and_format_data(all_location)
final_data = get_data_with_cache(results, validity_days=7)
print(json.dumps(final_data, indent=2, ensure_ascii=False))

print("\n" + "="*30 + "\n")

# 模拟在有效期内再次运行
print("--- 第二次（短时间内）运行 ---")
final_data_from_cache = get_data_with_cache(results, validity_days=7)