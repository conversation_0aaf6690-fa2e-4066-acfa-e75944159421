import requests
from mcp.server.fastmcp import FastMCP

# 创建 MCP 服务器实例
mcp = FastMCP("GetBusinessData")

# 使用装饰器定义获取当前时间的工具函数
@mcp.tool()
def get_business_data(regionCode: str = None, agencyCode: str = None, businessManagerNum: str = None, 
                      dealerCode: str = None, provinceCode: str = None, cityCode: str = None, dimension: str = None, 
                      dateValue: str = None, endDateValue: str = None):
    """
    获取售后经营数据
    :param regionCode: 大区代码
    :param agencyCode: 营销小组代码
    :param businessManagerNum: 商务经理代码
    :param dealerCode: 经销商代码
    :param provinceCode: 省份代码
    :param cityCode: 城市代码
    :param dimension: 维度:MTD/QTD/YTD/OTHER
    :param dateValue: 示例:202412/2024Q4/2024/202401
    :param endDateValue: 例:202412(MTD/QTD/YTD不传)
    :return: 省份名称和省份代码
    """
    url = 'http://dac.csvw.com/report/mobile/wechat/sv/seviceComprehensiveReportController/performanceData'
    token = 'GJRzj5fhS60AFVD33LhZzYN3Ixy0X1aA'
    headers = {
        'Authorization': token
    }
    params = {
        "regionCode": regionCode,
        "agencyCode": agencyCode,
        "businessManagerNum": businessManagerNum,
        "dealerCode": dealerCode,
        "provinceCode": provinceCode,
        "cityCode": cityCode,
        "dimension": dimension,
        "dateValue": dateValue,
        "endDateValue": endDateValue
    }
    response = requests.get(url, params=params, headers=headers)
    return response

# 运行 MCP 服务器
if __name__ == "__main__":
    mcp.run()
    # provinceCode = '110000'
    # dimension = 'MTD'
    # dateValue = '202502'
    # response = get_business_data(provinceCode=provinceCode, dimension=dimension, dateValue=dateValue)
    # print(response.text)