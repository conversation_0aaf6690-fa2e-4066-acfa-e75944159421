import httpx
import asyncio
import json

async def request_data():
    # 与同步请求相同的参数
    api = 'http://************:8080/report/mobile/wechat/sv/afterSaleJyController/queryJyIndex'
    params = {"yearMonth": "202301", "queryType": "M"}
    auth_token = 'SYAAkjENRNIAFVoQKylHQ51nZRNeX12-'
    
    # 创建与同步请求完全相同的请求头
    headers = {
        'User-Agent': 'python-requests/2.32.3',
        'Accept-Encoding': 'gzip, deflate',
        'Accept': '*/*',
        'Connection': 'keep-alive',
        'Authorization': auth_token
    }
    
    # 打印将要发送的请求信息
    print(f"请求URL: {api}")
    print(f"请求参数: {params}")
    print(f"请求头: {headers}")
    
    try:
        # 创建异步客户端 - httpx的API与requests非常相似
        async with httpx.AsyncClient(
            timeout=30.0,
            verify=False,  # 禁用SSL验证
            follow_redirects=True  # 允许自动跟随重定向
        ) as client:
            print("发送请求...")
            
            # 发送GET请求
            response = await client.get(
                api,
                params=params,
                headers=headers
            )
            
            # 打印响应信息
            print(f"状态码: {response.status_code}")
            print(f"响应头: {response.headers}")
            
            # 检查响应状态
            if response.status_code == 200:
                # 获取响应内容
                content_type = response.headers.get('content-type', '')
                print(f"内容类型: {content_type}")
                
                # 打印响应文本的前100个字符
                print(f"响应内容前100个字符: {response.text[:100]}")
                
                # 尝试解析JSON
                try:
                    # httpx处理JSON的方式与requests相同
                    data = response.json()
                    print(f"成功解析JSON数据: {data}")
                    return data
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {e}")
                    
                    # 检查返回内容是否为HTML
                    if '<html' in response.text.lower():
                        print("服务器返回了HTML而不是JSON")
                    
                    return None
            else:
                print(f"请求失败，状态码: {response.status_code}")
                return None
    
    except httpx.RequestError as e:
        print(f"请求错误: {e}")
        return None
    except Exception as e:
        print(f"未知错误: {type(e).__name__}: {e}")
        return None

async def main():
    result = await request_data()
    print(f"最终响应: {result}")

if __name__ == "__main__":
    asyncio.run(main()) 