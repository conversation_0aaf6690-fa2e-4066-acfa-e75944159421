import calendar
import time
import datetime
from sx_dict import sx_date_name
from env_config import latest_date_url
# from utils.sx_dict import sx_date_name
# from utils.env_config import latest_date_url
import requests


# 将整数转换为对应的星期几的字符串
weekday_dict = {
    0: '星期一',
    1: '星期二',
    2: '星期三',
    3: '星期四',
    4: '星期五',
    5: '星期六',
    6: '星期日'
}


# 获取日期变量
def get_date_info():
    today_date_str = time.strftime('%Y%m%d',time.localtime())
    today_date = datetime.date.today()
    this_year = today_date.year
    this_month = today_date.month
    today_day = today_date.day
    today_date_cn = f"{this_year}年{this_month}月{today_day}日"
    this_year_first_day_date = datetime.datetime(this_year, 1, 1)
    this_year_first_day_date_str = this_year_first_day_date.strftime('%Y%m%d')
    this_year_last_day_date = datetime.datetime(this_year, 12, 31)
    this_year_last_day_date_str = this_year_last_day_date.strftime('%Y%m%d')
    next_month = today_date.replace(day=1) + datetime.timedelta(days=32)
    next_month = datetime.date(next_month.year, next_month.month, 1)
    next_month = next_month.strftime("%Y%m")
    this_month = time.strftime('%Y%m',time.localtime())
    last_month = today_date.replace(day=1) - datetime.timedelta(days=1)
    last_month = datetime.date(last_month.year, last_month.month, 1)
    last_month = last_month.strftime("%Y%m")
    this_month_last_day = calendar.monthrange(today_date.year, today_date.month)[1]
    today_weekday = weekday_dict[today_date.weekday()]
    return {
        'today_date_str': today_date_str,
        'today_date': today_date,
        'today_year': this_year,
        'today_month': this_month,
        'today_day': today_day,
        'today_date_cn': today_date_cn,
        'this_year_first_day_date': this_year_first_day_date,
        'this_year_first_day_date_str': this_year_first_day_date_str,
        'this_year_last_day_date': this_year_last_day_date,
        'this_year_last_day_date_str': this_year_last_day_date_str,
        'next_month': next_month,
        'this_month': this_month,
        'last_month': last_month,
        'this_month_last_day': this_month_last_day,
        'today_weekday':today_weekday
    }


def answer_date(date_type,t,language):
    """数据查询的回复时间处理

    Args:
        date_type (String): 数据查询时间的类型
        t (String): 日期
        language (String): 语言

    Returns:
        String: 查询回复展示的日期
    """
    if len(t) == 6 and date_type == 'day':
        date_type = 'month'
    if language == "中文":
        if date_type == "month":
            result = t[0:4]+'年'+str(int(t[4:6]))+"月"
        elif date_type == "week":
            result = t[0:4]+'年第'+str(int(t[4:6]))+"周"
        elif date_type == "day":
            result = t[0:4]+'年'+str(int(t[4:6]))+"月"+str(int(t[6:8]))+"日"
    elif language == "英文":
        if date_type == "month":
            result = sx_date_name[str(int(t[4:6]))][language]+" "+str(t[0:4])
        elif date_type == "week":
            result = str(t[0:4])+'W'+str(t[4:6])
        elif date_type == "day":
            result = sx_date_name[str(int(t[4:6]))][language]+' '+str(int(t[6:8]))+", "+str(t[0:4])
    return result

def get_warroom_latest_date():
    """获取沙盘最新数据时间"""
    url = latest_date_url
    headers = {"Content-Type": "application/json"}
    result = requests.get(url, headers=headers).json()
    result = result["data"]
    result['lastDataDateByDate'] = str(result['lastDataDateByDate'])
    result['lastDataDateByWeek'] = str(result['lastDataDateByWeek'])
    result['lastDataDateByMonth'] = str(result['lastDataDateByMonth'])
    return result

if __name__ == '__main__':
    date_info = get_date_info()
    print(date_info["today_date_str"])