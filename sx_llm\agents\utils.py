



async def rewrite_question(self):
    # 问题分类
    prompt = prompt_question_rewrite_template.format(current_question = self.question, history_question = self.history_question)
    content = [{"role": "user","content": prompt}]
    response = await self.process_llm.async_chat_by_token(content)
    result = ''
    async for chunk in response:
        content = chunk.choices[0].delta.content
        result += content
    self.sub_questions = ast.literal_eval(result)