import requests
import json
import csv
import random
import os
import re
from collections import namedtuple
from configure_logger import configure_logger

Summary = namedtuple('Summary', ['round', 'url', 'passed', 'total', 'passed_rate'])


@configure_logger
class ChatTester:
    def __init__(self, chat_urls, tsv_files, check_type, llm="wenxin", prompt="prompt_V7") -> None:
        self.chat_urls = chat_urls
        self.tsv_files = tsv_files
        self.check_type = check_type
        self.llm = llm
        self.prompt = prompt
        self._clear_summary()
        self._read_test_case()

    def _print_result(self, is_passed, question_text, answer_text) -> None:
        pass_str = "通过" if is_passed else "不通过"
        format_str = '\n'.join(["", "-"*5, pass_str, f"问题：{question_text}", f"回答：{answer_text}", "-"*5])
        self.logger.info(format_str)
    
    def _clear_summary(self) -> None:
        self.summary = []

    def print_summary(self) -> None:
        self.logger.info("*" * 20)

        urls = set(s.url for s in self.summary)
        for url in urls:
            url_summaries = [s for s in self.summary if s.url == url]
            url_avg_passed_rate = sum(s.passed_rate for s in url_summaries) / len(url_summaries)
            
            self.logger.info(f"平均通过率：{url_avg_passed_rate}；URL：{url}")
            
            for s in url_summaries:
                self.logger.info(f"第 {s.round} 轮测试总数：{s.total}；通过数量：{s.passed}；通过率：{s.passed_rate}")
            
        self.logger.info("*" * 20)

    def _try_parse_markdown_json(self, input_string):
        # 尝试解析Markdown格式的JSON
        try:
            json_pattern = r'```json\s+(.*?)\s+```'
            json_blocks = re.findall(json_pattern, input_string, re.IGNORECASE | re.DOTALL)
            if json_blocks:
                # 解析Markdown中的JSON内容
                parsed_json = json.loads(json_blocks[0])
                return parsed_json
        except Exception as e:
            pass

        # 如果Markdown解析失败，尝试直接解析为JSON对象, 解析失败直接抛出异常
        try:
            parsed_json = json.loads(input_string)
            return parsed_json
        except json.JSONDecodeError as e:
            self.logger.debug(f"尝试解析json失败：{input_string}")
            raise e

        return {}

    def _read_test_case(self) -> None:
        self.total_test_cases = []

        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        for tsv_file_name in self.tsv_files:
            tsv_file = os.path.join(parent_dir, 'data', tsv_file_name)
            with open(tsv_file, encoding='utf-8') as f:
                tsv_reader = csv.reader(f, delimiter='\t')
                for row in tsv_reader:
                    question = row[0]
                    answer = row[1]
                    flag = row[2]
                    test_case = {"question": question, "answer": answer, "flag": flag}
                    self.total_test_cases.append(test_case)
    
    # 对比字典中的键值对是否相同
    def _deep_compare(self, dict1, dict2):
        if isinstance(dict1, dict) and isinstance(dict2, dict):
            if set(dict1.keys()) != set(dict2.keys()):
                return False
            for key in dict1.keys():
                if not self._deep_compare(dict1[key], dict2[key]):
                    return False
            return True
        elif isinstance(dict1, list) and isinstance(dict2, list):
            return sorted(dict1) == sorted(dict2)
        else:
            return dict1 == dict2

    def request_chat_data(self, chat_url, question):
        headers = {"Content-Type": "application/json"}
        data = {
            "userId": "11",
            "sessionId": "11",
            "prompt": self.prompt,
            "llm": self.llm,
            "questionText": question,
            "client": "1",
            "category": "1"
        }
        return requests.post(chat_url, headers=headers, data=json.dumps(data))

    def sample_test_cases(self, flag_list, record_number=100) -> list:
        if not flag_list:
            filtered_cases = self.total_test_cases
        else:
            filtered_cases = [case for case in self.total_test_cases if case['flag'] in flag_list]

        if len(filtered_cases) < record_number:
            record_number = len(filtered_cases)
        sampled_cases = random.sample(filtered_cases, k=record_number)
        return sampled_cases

    def test_chat(self, chat_url, test_cases) -> list:
        total_tests = len(test_cases)
        passed_tests = 0

        for test_case in test_cases:
            try:
                response = self.request_chat_data(chat_url, test_case["question"])
            except:
                self.logger.info(f"请求失败：{test_case['question']}")
                continue

            if response.status_code == 200:
                result = response.json()

                if self.check_type == 'str':
                    if test_case["answer"] in str(result["answerText"]):
                        is_passed = 1
                    else:
                        is_passed = 0
                elif self.check_type == 'json':
                    try:
                        # Parse the test_case["answer"] and result["answerText"] into JSON objects
                        actual_answer = self._try_parse_markdown_json(str(result["answerText"]))
                        expected_answer = json.loads(str(test_case["answer"]))
                    except json.JSONDecodeError:
                        # If there is a JSON decoding error, use string to compare instread
                        actual_answer = str(result["answerText"])
                        expected_answer = str(test_case["answer"])
                    
                    self.logger.debug(f"回答内容，类型：{type(actual_answer)}，回答：{actual_answer}")
                    self.logger.debug(f"期望答案，类型：{type(expected_answer)}，回答：{expected_answer}")
                    if self._deep_compare(expected_answer, actual_answer):
                        is_passed = 1
                    else:
                        is_passed = 0

                else:
                    self.logger.error(f"Unknown check_type: {self.check_type}")
                    is_passed = 0

                if is_passed == 1:
                    passed_tests += 1
                    self._print_result(True, test_case['question'], result["answerText"])
                else:
                    self._print_result(False, test_case['question'], result["answerText"])

            else:
                self.logger.info(f"请求失败：{test_case['question']}，error code：{response.status_code}")

        self.logger.info(f"测试总数量：{total_tests}")
        self.logger.info(f"测试通过的数量：{passed_tests}")
        return (passed_tests, total_tests)
        

    def run_tests(self, round=1, flag_list=[], random_record=1, test_cases=[]) -> None:
        self._clear_summary()

        for round_num in range(round):
            if not test_cases:
                sampled_test_cases = self.sample_test_cases(flag_list=flag_list, record_number=random_record)
            else:
                sampled_test_cases = test_cases
            for url in self.chat_urls:
                passed_tests, total_tests = self.test_chat(url, sampled_test_cases)
                self.summary.append(Summary(round_num+1, url, passed_tests, total_tests, passed_tests*1.0/total_tests))

        if not test_cases:
            self.logger.info(f"flag_list = ${flag_list}")
        self.print_summary()


if __name__ == "__main__":
    chat_urls = [
        # 测试环境
        # "http://127.0.0.1:8000/api/V1/chat/embedding/chain",
        # "http://127.0.0.1:8000/api/V1/chat/embedding/doc",
        # "http://127.0.0.1:8000/api/V1/chat/embedding",
        "http://127.0.0.1:8000/api/V1/chat/embedding/format_json",
        # "http://127.0.0.1:8000/api/V1/chat/embedding/V2",
        # 生产环境
        # "http://172.20.135.16:8000/api/V1/chat/embedding",
    ]

    tsv_file_collection = ['test_cases_form_json1.tsv', 'test_cases_form_json2.tsv', 'test_cases_form_json3.tsv', 'test_cases_knowledge_json.tsv']
    # tester = ChatTester(chat_urls, tsv_files=tsv_file_collection, check_type='str')
    tester = ChatTester(chat_urls, tsv_files=tsv_file_collection, check_type='json', llm="wenxin", prompt="prompt_V7")
    # tester.run_tests(round=5, flag_list=['1010','1110','1111'], random_record=100)
    tester.run_tests(round=1, flag_list=['1010'], random_record=10)
    tester.run_tests(round=1, flag_list=['1110'], random_record=10)
    tester.run_tests(round=1, flag_list=['1111'], random_record=10)

    other_test_cases = [
        {'question':'你是谁', 'answer':'SX人工智能助手'},
        {'question':'你是机器人吗？', 'answer':'SX人工智能助手'},
        {'question':'请介绍下你自己？', 'answer':'SX人工智能助手'},
        {'question':'2010年途观L的销量', 'answer':'无法'},
        {'question':'今年3月途观L的销量', 'answer':'8887'},
        {'question':'23年3月途观L的销量', 'answer':'8887'},
    ]

    other_test_cases_json = [
        {'question':'途昂在2022年的销量', 'answer':'{"year": "2022", "month": "", "model_name_cn":"Teramont", "rssc_cn":"", "intention":["销量"]}'},
        {'question':'辉昂在2022年的销量', 'answer':'{"year": "2022", "month": "", "model_name_cn":"Phideon", "rssc_cn":"", "intention":["销量"]}'},
        {'question':'朗行在2022年1月的销量', 'answer':'{"year": "2022", "month": "1", "model_name_cn":"Gran Lavida", "rssc_cn":"", "intention":["销量"]}'},
        {'question':'2021年12月威然的销量是多少？', 'answer':'{"year": "2021", "month": "12", "model_name_cn":"Viloran", "rssc_cn":"", "intention":["销量"]}'},
        {'question':'Polo在2021年6月的销量', 'answer':'{"year": "2021", "month": "6", "model_name_cn":"Polo HB", "rssc_cn":"", "intention":["销量"]}'},
        {'question':'2021年8月上汽大众品牌帕萨特车型在华中大区的销量', 'answer':'{"year": "2021", "month": "8", "model_name_cn":"Passat", "rssc_cn":"华中", "intention":["销量"]}'},
        {'question':'凌渡在2022年8月西北大区的销量', 'answer':'{"year": "2022", "month": "8", "model_name_cn":"Lamando", "rssc_cn":"西北", "intention":["销量"]}'},
        {'question':'2022年2月Polo在华中大区的销量是多少？', 'answer':'{"year": "2022", "month": "2", "model_name_cn":"Polo HB", "rssc_cn":"华中", "intention":["销量"]}'},
        {'question':'2021年4月ID.4 X在华东大区的销量是多少？', 'answer':'{"year": "2021", "month": "4", "model_name_cn":"ID.4 X", "rssc_cn":"华东", "intention":["销量"]}'},
        {'question':'途岳在2021年12月西南大区的销量', 'answer':'{"year": "2021", "month": "12", "model_name_cn":"Tharu", "rssc_cn":"西南", "intention":["销量"]}'},
        {'question':'2021年11月上汽大众品牌帕萨特车型在华东大区的销量', 'answer':'{"year": "2021", "month": "11", "model_name_cn":"Passat", "rssc_cn":"华东", "intention":["销量"]}'},

        {'question':'帕萨特在2022年的销量', 'answer':'{"year": "2022", "month": "", "model_name_cn":"Passat", "rssc_cn":"", "intention":["销量"]}'},
        {'question':'2023年威然的销量', 'answer':'{"year": "2023", "month": "", "model_name_cn":"Viloran", "rssc_cn":"", "intention":["销量"]}'},
        {'question':'威然在2022年2月的销量', 'answer':'{"year": "2022", "month": "2", "model_name_cn":"Viloran", "rssc_cn":"", "intention":["销量"]}'},
        {'question':'帕萨特在2022年9月西北大区的销量', 'answer':'{"year": "2022", "month": "9", "model_name_cn":"Passat", "rssc_cn":"西北", "intention":["销量"]}'},
        {'question':'2022年9月辉昂在华南大区的销量是多少？', 'answer':'{"year": "2022", "month": "9", "model_name_cn":"Phideon", "rssc_cn":"华南", "intention":["销量"]}'},
        {'question':'2023年上汽大众品牌途观车型全年的销量', 'answer':'{"year": "2023", "month": "", "model_name_cn":"Tiguan", "rssc_cn":"", "intention":["销量"]}'},

        {'question':'2023年帕萨特的销量，同环比以及市场占有率', 'answer':'{"year": "2023", "month": "", "model_name_cn":"Passat", "rssc_cn":"", "intention":["销量","同比","环比","市占"]}'},
        {'question':'2023年3月id3的销量和同比', 'answer':'{"year": "2023", "month": "3", "model_name_cn":"ID.3", "rssc_cn":"", "intention":["销量","同比"]}'},
        {'question':'2023年途观的销量，环比和市场占有率', 'answer':'{"year": "2023", "month": "", "model_name_cn":"Tiguan", "rssc_cn":"", "intention":["销量","环比","市占"]}'},
        {'question':'今年Polo的销量，同比和市占', 'answer':'{"year": "2023", "month": "", "model_name_cn":"Polo HB", "rssc_cn":"", "intention":["销量","同比","市占"]}'},
        {'question':'上个月朗逸的市场占有率', 'answer':'{"year": "2023", "month": "7", "model_name_cn":"Lavida", "rssc_cn":"", "intention":["市占"]}'},
        {'question':'22年途昂的销量同环比和市场占有率', 'answer':'{"year": "2022", "month": "", "model_name_cn":"Teramont", "rssc_cn":"", "intention":["销量","同比","环比","市占"]}'},

        {'question':'请帮我把上面的销量数据拟一份邮件发给梁凯智', 'answer':'{}'},
        {'question':'你是谁', 'answer':'{}'},
        {'question':'你是机器人吗？', 'answer':'{}'},
        {'question':'上面数据的负责人是谁', 'answer':'{}'},
        {'question':'今天实时数据', 'answer':'{}'},
        {'question':'今天实时销量数据', 'answer':'{}'},
    ]

    tester.run_tests(round=1, test_cases=other_test_cases_json)
