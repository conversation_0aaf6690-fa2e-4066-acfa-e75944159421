import time
from functools import wraps

def time_logger(func):
    """装饰器，记录函数执行时间"""

    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()  # 记录开始时间
        result = func(*args, **kwargs)  # 调用原函数
        end_time = time.time()  # 记录结束时间
        execution_time = end_time - start_time  # 计算执行时间
        return result, execution_time
    return wrapper

def print_input_output(func):
    """装饰器，打印函数输入输出"""
    def wrapper(*args, **kwargs):
        print(f"Function {func.__name__} called with arguments:")
        print("Positional arguments:", args)
        # print("Keyword arguments:", kwargs)
        
        result = func(*args, **kwargs)
        
        print(f"Function {func.__name__} returned:")
        print(result)
        return result
    return wrapper



if __name__ == "__main__":
    @time_logger
    def example_function():
        time.sleep(2)  # 模拟耗时操作

    execution_time = example_function()
    print(execution_time)