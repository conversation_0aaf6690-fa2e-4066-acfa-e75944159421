from abc import ABC, abstractmethod


class BaseChartProcessor(ABC):
    """
    Base class for all chart processors.
    """

    def __init__(self, format_json: dict) -> None:
        self.json_kpi = format_json["kpi"]
        self.json_display = format_json["display"]
        self.json_data_dim = format_json["data_dim"]
        self.json_location = format_json["location"]
        self.json_model = format_json["model"]
        self.json_today = format_json["today"]
        self.json_date_type = format_json["date_type"]
        self.json_not_total = format_json["not_total"]


    @abstractmethod
    def chart_process(self, data: list[dict], language: str) -> list[dict]:
        """图表内容展示

        Args:
            data (List): 沙盘接口查询的数据
            language (String): 语言

        Returns:
            chart_list (List): 给沙盘前端展示的数据
        """

        raise NotImplementedError("Subclass must implement abstract method")