import asyncio
from typing import List

from multi_agent_client import MultiAgentClient
from agents.base_agent import BaseAgent



class InsuranceAgent(BaseAgent):
    def __init__(self):
        super().__init__()  # 调用父类初始化
        self.kpis = []
        self.categorys_kpis = {}
        self.category_kpi_api = []
        self.data_all = []


    def get_profiles_from_manager(self, agent: BaseAgent):
        # 从管理类获取数据
        self.client = agent.client
        self.access_token = agent.access_token
        self.input_json = agent.input_json
        self.user_id = agent.user_id
        self.conversation_id = agent.conversation_id
        self.question = agent.question
        self.is_internet_search = agent.is_internet_search
        self.is_deep_thinking = agent.is_deep_thinking
        self.language = agent.language
        self.input_time = agent.input_time
        self.history_message = agent.history_message
        self.history_question = agent.history_question
        self.conversation_all = agent.conversation_all
        self.message_id = agent.message_id


    async def call_agents(self, agent_urls: List[str]):
        # 创建多智能体客户端
        client = MultiAgentClient()
            # 发现并连接所有智能体
        for url in agent_urls:
            try:
                await client.discover_agent(url)
            except Exception as e:
                print(f"无法连接到智能体 {url}: {str(e)}")

        # 列出所有已连接的智能体
        agents = client.list_agents()
        if not agents:
            print("没有找到可用的智能体。请检查URL是否正确。")
            return
        print("\n=== 已连接的智能体 ===")
        for i, agent in enumerate(agents):
            print(f"{i+1}. {agent['name']}: {agent['description']}")
        
        message = self.question
        try:
            # 发送消息并自动选择智能体
            print("正在自动选择合适的智能体...")
            task = await client.send_message(message)

            if task:
                agent_name = client.current_agent
                print(f"已选择智能体: {agent_name}")
                print(f"任务已发送，任务ID: {task.id}")
                print(f"任务状态: {task.status.state}")

                # 等待任务完成
                print("等待任务完成...")
                completed_task = await client.wait_for_task_completion(task.id)

                if completed_task:
                    response = client.extract_agent_response(completed_task)
                    print(f"\n智能体回复:\n{response}")
                else:
                    print("任务未能完成")
            else:
                print("发送任务失败")
        except ValueError as e:
            print(f"错误: {str(e)}")
            print("请尝试手动选择智能体或在消息中明确提到智能体名称")
                    


if __name__ == "__main__":
    agent = InsuranceAgent()
    agent.question = "你好"
    agent_urls = ['http://localhost:10002']
    asyncio.run(agent.call_agents(agent_urls=agent_urls))
